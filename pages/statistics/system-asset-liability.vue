<template>
  <div class="table-data">
    <h2 style="font-family: 'Microsoft YaHei',<PERSON>l,sans-serif;">
      资产负债
    </h2>

    <el-form :inline="true" :model="search_data">

      <el-form-item label="币种">
        <el-select filterable clearable v-model="search_data.asset" placeholder="<ALL>" @change="search(true)" class="filter-paste">
          <el-option v-for="value in assets"
                     :key="value"
                     :label="value"
                     :value="value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="时间" v-show="search_data.asset">
        <el-col :span="11">
          <el-date-picker
            v-model="search_data.start_time"
            type="datetime"
            value-format="timestamp"
            placeholder="开始时间"
            @change="search(true)">
          </el-date-picker>
        </el-col>
        <el-col class="line" :span="2">-</el-col>
        <el-col :span="11">
          <el-date-picker
            v-model="search_data.end_time"
            type="datetime"
            value-format="timestamp"
            placeholder="结束时间"
            @change="search(true)">
          </el-date-picker>
        </el-col>
      </el-form-item>
      <el-form-item label="上次不平USD" v-show="!search_data.asset">
        <el-input v-model="search_data.min_usd" clearable style="width: 120px;"/>
      </el-form-item>
      <el-form-item>
        <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
          <el-button icon="el-icon-refresh-left"
                     circle
                     @click="search(true)"/>
        </el-tooltip>
      </el-form-item>
    </el-form>

    <el-table :data="items"
              :cell-style="handleCellStyle"
              v-loading="loading"
              height="1250"
              stripe
              v-if="!loading"
    @sort-change="sortHandle">

      <el-table-column
        prop="asset"
        label="币种">
        <template slot-scope="scope">
          <el-link :href="`?asset=${scope.row.asset}`"
                   type="primary"
                   :underline="false"
                   style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
            {{ scope.row.asset }}
          </el-link>
        </template>
      </el-table-column>

      <el-table-column label="平台资产" prop="sys_total">

        <el-table-column
          prop="hot_wallet"
          :formatter="row => formatAssetAmount(row.hot_wallet)"
          label="热钱包">
        </el-table-column>
        <el-table-column
          prop="cold_wallet"
          :formatter="row => formatAssetAmount(row.cold_wallet)"
          label="冷钱包">
        </el-table-column>
        <el-table-column
          prop="deposit_wallet"
          :formatter="row => formatAssetAmount(row.deposit_wallet)"
          label="充值钱包">
        </el-table-column>
        <el-table-column
          prop="onchain_wallet"
          :formatter="row => formatAssetAmount(row.onchain_wallet)"
          label="链上资产">
        </el-table-column>

        <el-table-column label="借贷未还">
          <el-table-column
            prop="margin_unflat"
            :formatter="row => formatAssetAmount(row.margin_unflat)"
            label="杠杆">
          </el-table-column>
          <el-table-column
              prop="credit_unflat"
              :formatter="row => formatAssetAmount(row.credit_unflat)"
              label="授信">
          </el-table-column>
          <el-table-column
              prop="pledge_unflat"
              :formatter="row => formatAssetAmount(row.pledge_unflat)"
              label="借贷">
          </el-table-column>
        </el-table-column>
      </el-table-column>

      <el-table-column label="平台负债" prop="sys_debt">
        <el-table-column
          prop="spot"
          :formatter="row => formatAssetAmount(row.spot)"
          label="现货账户">
        </el-table-column>
        <el-table-column
          prop="margin"
          :formatter="row => formatAssetAmount(row.margin)"
          label="杠杆账户">
        </el-table-column>
        <el-table-column
          prop="investment"
          :formatter="row => formatAssetAmount(row.investment)"
          label="理财账户">
        </el-table-column>
        <el-table-column
          prop="perpetual"
          :formatter="row => formatAssetAmount(row.perpetual)"
          label="合约账户">
        </el-table-column>
        <el-table-column
          prop="pledge"
          :formatter="row => formatAssetAmount(row.pledge)"
          label="借贷账户">
        </el-table-column>
        <el-table-column
          label="保险基金">
          <el-table-column
            prop="perpetual_insurance"
            :formatter="row => formatAssetAmount(row.perpetual_insurance)"
            label="合约">
          </el-table-column>
          <el-table-column
            prop="margin_insurance"
            :formatter="row => formatAssetAmount(row.margin_insurance)"
            label="杠杆">
          </el-table-column>
        </el-table-column>
      </el-table-column>

      <el-table-column label="平台权益"
                       prop="sys_income"
                       width="140px">
        <template slot-scope="scope">
          <el-popover
                      placement="left"
                      width="240"
                      trigger="hover">
            <el-row type="flex" justify="space-around" align="middle">
              <el-col :span="21">
                总资产: {{ formatAssetBalance(scope.row.sys_total) }}
                <br>
                总负债: {{ formatAssetBalance(scope.row.sys_debt) }}
              </el-col>
            </el-row>
            <span slot="reference">{{ formatAssetAmount(scope.row.sys_income) }}</span>
          </el-popover>
        </template>
      </el-table-column>

      <el-table-column label="平台权益(USD)"
                       prop="sys_income_value"
                       width="100px"
                       :sortable="search_data.asset? false: 'custom'"
                       >
        <template slot-scope="scope">
          <el-popover
            v-if="scope.row.asset === 'ALL'"
            placement="left"
            width="240"
            trigger="hover">
            <el-row type="flex" justify="space-around" align="middle">
              <el-col :span="21">
                <p style="color: #ff0000">50W+ USD为红色</p>
                <br>
                <p style="color: #ffa500">5W～50W USD为橙色</p>
                <br>
                <p style="color: #000080">1W ～5W USD为蓝色</p>
                <br>
                <p>1W USD以下为黑色</p>
              </el-col>
            </el-row>
            <span slot="reference">{{ formatAssetBalance(scope.row.sys_income_value) }}</span>
          </el-popover>
          <el-popover
            v-if="scope.row.asset !== 'ALL'"
            placement="left"
            width="240"
            trigger="hover">
            <el-row type="flex" justify="space-around" align="middle">
              <el-col :span="21">
                <p style="color: #ff0000">5W+ USD为红色</p>
                <br>
                <p style="color: #ffa500">5k～5W USD为橙色</p>
                <br>
                <p style="color: #000080">500～5k USD为蓝色</p>
                <br>
                <p>500 USD以下为黑色</p>
              </el-col>
            </el-row>
            <span slot="reference">{{ formatAssetBalance(scope.row.sys_income_value) }}</span>
          </el-popover>
        </template>
      </el-table-column>

      <el-table-column
        label="更新时间"
        :formatter="row => $formatDate(row.created_at)">
      </el-table-column>

      <el-table-column type="expand">
        <template slot-scope="props">
          <el-form label-position="left" inline class="demo-table-expand">
            <el-form-item label="异常充值">
              <span>{{ props.row.except_deposit }}</span>
            </el-form-item>
            <el-form-item label="充值未到账">
              <span>{{ props.row.processing_deposit }}</span>
            </el-form-item>
            <el-form-item label="提现未打出">
              <span>{{ props.row.processing_withdrawal }}</span>
            </el-form-item>
            <el-form-item label="站内转账未打出">
              <span>{{ props.row.processing_local_transfer }}</span>
            </el-form-item>
            <el-form-item label="C-Box未领取">
              <span>{{ props.row.ungrabbed_red_packet }}</span>
            </el-form-item>
            <el-form-item label="收付款待平账">
              <span>{{ props.row.payment_hedging_amount }}</span>
            </el-form-item>
            <el-form-item label="KYT冻结资产">
              <span>{{ props.row.kyt_frozen }}</span>
            </el-form-item>
            <el-form-item label="质押总数量">
              <span>{{ props.row.staking_amount }}</span>
            </el-form-item>
            <el-form-item label="发行数量">
              <span>{{ props.row.pre_asset_issue }}</span>
            </el-form-item>
            <el-form-item label="链上未提取收益">
              <span>{{ props.row.staking_pending_reward }}</span>
            </el-form-item>
            <el-form-item label="备注">
              <span>{{ props.row.remark }}</span>
            </el-form-item>
            <el-form-item label="修改备注">
              <el-tooltip content="编辑" placement="right" :open-delay="500" :hide-after="2000">
                <el-button size="mini"
                           type="primary"
                           icon="el-icon-edit"
                           circle
                           @click="edit_remark(props.row)"/>
              </el-tooltip>
              <el-tooltip v-if="props.row.asset !== 'ALL' " content="重新对账" placement="right" :open-delay="500" :hide-after="2000">
                <el-button size="mini"
                           type="warning"
                           icon="el-icon-s-data"
                           circle
                           @click="rerun_check(props.row)"/>
              </el-tooltip>
            </el-form-item>
          </el-form>
        </template>
      </el-table-column>

    </el-table>
    <el-pagination :current-page.sync="search_data.page"
                   :page-size.sync="search_data.limit"
                   @size-change="search(false)"
                   @current-change="search(false)"
                   :page-sizes="[100, 50]"
                   :hide-on-single-page="true"
                   layout="total, sizes, prev, pager, next, jumper"
                   :total="total"
                   v-show="search_data.asset">
    </el-pagination>
    <el-backtop/>

  </div>
</template>

<script>

  const binding_url = "/api/statistic/system-asset-liability";

  export default {
    mounted() {
      this.get_data();
    },
    methods: {
      get_data() {
        let query = this.search_data;
        this.loading = true;
        this.$axios.get(binding_url, {params: query})
        .then((res) => {
          if (res.data.code === 0) {
            let data = res.data.data;
            this.items = data.items;
            this.originItems = [...this.items];
            this.assets = data.assets;
            this.total = data.total;
            this.loading = false;
          } else {
            this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
          }
        })
        .catch((e) => {
          this.$message.error(`刷新失败! (${e})`);
        })
      },
      search(reset = true) {
        if (this.search_data.asset){
          this.search_data.min_usd = null;
        }
        if (reset === true) {
          this.search_data.page = 1;
        }
        this.get_data();
      },
      handleCellStyle({row, column, rowIndex, columnIndex}) {
        let green_columns = [
          "hot_wallet", "cold_wallet", "deposit_wallet", "onchain_wallet", "margin_unflat", "credit_unflat", "pledge_unflat"
        ];
        let yellow_columns = [
          "spot", "margin", "investment", "perpetual", "pledge",
          "perpetual_insurance", "margin_insurance",
          "except_deposit", "processing_deposit",
          "processing_withdrawal",
          "processing_local_transfer",
          "ungrabbed_red_packet"
        ];
        let warning_columns = ["sys_income_value"];

        if(green_columns.indexOf(column.property) !== -1) {
          //
          return "background: LimeGreen";
        }
        if(yellow_columns.indexOf(column.property) !== -1) {
          //
          return "background: Yellow";
        }

        if(warning_columns.indexOf(column.property) !== -1) {
          //
          const sys_income_usd = Math.abs(Number(row.sys_income_value));
          if (row.asset === 'ALL') {
              if(sys_income_usd > 500000){
                return "color:#ff0000"
              }
              if(sys_income_usd > 50000){
                return "color:#ffa500"
              }
              if(sys_income_usd > 10000){
                return "color:#000080"
              }
            } else {
            if (sys_income_usd > 50000) {
              return "color:#ff0000"
            }
            if (sys_income_usd > 5000) {
              return "color:#ffa500"
            }
            if (sys_income_usd > 500) {
              return "color:#000080"
            }
          }
        }
      },
      edit_remark(row) {
        this.$prompt(`编辑 <b>${row.asset}</b> (${row.sys_income})`, '', {
          dangerouslyUseHTMLString: true,
          inputValue: row.remark,
          closeOnClickModal: false
        }).then(({value}) => {
          this.$axios.put(`/api/statistic/system-asset-liability`, {id_:row.id, remark: value}).then(res => {
            if (res?.data?.code === 0) {
              let data = res.data.data;
              row.value = data.value;
              this.$message.success("备注成功!");
              this.get_data();
            } else {
              this.$message.error(`备注失败! (code: ${res.data?.code}; message: ${res.data?.message})`);
            }
          });
        });
      },
      rerun_check(row) {
        this.$confirm(`重新对账 ${row.asset}?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          this.$axios.patch(`/api/statistic/system-asset-liability`, {asset:row.asset}).then(res => {
            if (res?.data?.code === 0) {
              this.$message.success("对账成功!");
              this.get_data();
            } else {
              this.$message.error(`对账失败! (code: ${res.data?.code}; message: ${res.data?.message})`);
            }
          });
        });

      },
      toNonExponential(num) {
        const number = Number(num);
        const m = number.toExponential().match(/\d(?:\.(\d*))?e([+-]\d+)/);
        return number.toFixed(Math.max(0, (m[1] || '').length - m[2]));
      },
      formatAssetAmount(amount){
        // 10位有效数字，按千分位逗号隔离
        const b = this.toNonExponential(Number(amount).toPrecision(10));
        if(Math.abs(b) > 1000000){
          return Number(b).toLocaleString()
        }
        return b
      },
      formatAssetBalance(balance){
        const b = this.toNonExponential(Number(balance).toFixed(0));
        if(Math.abs(b) > 1000000){
          return Number(b).toLocaleString()
        }
        return b
      },
      cmp(a, b, k) {
        if(a['asset'] === 'ALL'){
          return 0;
        }
        let n1, n2;
        n1 = parseFloat(a[k]);
        n2 = parseFloat(b[k]);

        if (n1 > n2) {
          return 1
        } else {
          return -1
        }
      },
      sortHandle(column) {
        // debugger;
        let fieldName = column.prop;
        let sortingType = column.order;//排序类型
        let tableData = this.items;//渲染对应data数据

        let all
        tableData.map((item) => {
          if (item.asset === 'ALL') {
            all = item
          }
        })
        if (sortingType === "ascending") {
          //正序
          tableData = tableData.sort((a, b) => Number(a[fieldName]) - Number(b[fieldName]));

        } else if (sortingType === "descending") {
          // 倒序
          tableData = tableData.sort((a, b) => Number(b[fieldName]) - Number(a[fieldName]));

        } else {
          this.items = [...this.originItems]
          return
        }

        tableData.forEach((item, index) => {
          if (item.asset === "ALL") {
            tableData.splice(index, 1);
          }
        });
        this.items = [all, ...tableData];

      },
    },
    created() {
      this.$sync_router_query(this, 'search_data', {asset: String});
    },
    data() {
      return {
        search_data: {
          asset: null,
          min_usd: null,
          start_time: null,
          end_time: null,
          page: 1,
          limit: 100
        },
        loading: false,
        items: [],
        originItems: [],
        assets: [],
        total: 0
      }
    }
  }

</script>

<style>
  .el-table .warning {
    color: blue;
  }
  .el-table .danger {
    color: orange;
  }
  .el-table .error {
    color: red;
  }
</style>
