<template>
  <div class="wallet-data">
    <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
      资产锁定列表
    </h2>

    <div class="col-md-12">
    </div>

    <el-form :inline="true">
      <el-form-item label="排序">
        <el-select v-model="search_data.sort"
                   @change="get_data"
                   placeholder="<ALL>">
          <el-option v-for="(value, key) in sort_data"
                     :key="key"
                     :label="value"
                     :value="key">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="币种">
        <el-select clearable filterable v-model="search_data.asset" placeholder="<ALL>" @change="get_data">
          <el-option
            v-for="item in $store.state.spot.info.asset_list"
            :key="item"
            :label="item"
            :value="item">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="状态">
        <el-select v-model="search_data.status"
                   clearable
                   @change="get_data"
                   placeholder="<ALL>">
          <el-option v-for="(value, key) in status_data"
                     :key="key"
                     :label="value"
                     :value="key">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <UserSearch v-model="search_data.user_id" :refresh_method="get_data"></UserSearch>
      </el-form-item>

      <el-form-item>
        <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
          <el-button icon="el-icon-refresh-left"
                     circle
                     @click="get_data"></el-button>
        </el-tooltip>
      </el-form-item>
    </el-form>

    <el-table :data="items"
              v-loading="loading"
              stripe>

      <el-table-column
        prop="id"
        label="ID">
      </el-table-column>

      <el-table-column
        prop="create_time"
        label="上传时间"
        :formatter="row => $formatDate(row.create_time)">
      </el-table-column>

      <el-table-column
        prop="asset"
        label="币种">
      </el-table-column>

      <el-table-column
        prop="amount"
        label="数量">
      </el-table-column>

      <el-table-column
        prop="user_id"
        label="ID">
        <template slot-scope="scope">
          <el-link :href="'/users/user-details?id=' + scope.row.user_id"
                   type="primary"
                   target="_blank"
                   :underline="false"
                   style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
            {{ scope.row.user_id }}
          </el-link>
        </template>
      </el-table-column>

      <el-table-column
        prop="created_user_name"
        label="上传用户">
        <template slot-scope="scope">
          <el-link :href="'/users/user-details?id=' + scope.row.created_by"
                   type="primary"
                   target="_blank"
                   :underline="false"
                   style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
            {{ scope.row.created_user_name }}
          </el-link>
        </template>
      </el-table-column>

      <el-table-column
        prop="lock_time"
        label="锁定时间"
        :formatter="row => $formatDate(row.lock_time)">
      </el-table-column>

      <el-table-column
        prop="unlock_time"
        label="解锁时间"
        :formatter="row => $formatDate(row.unlock_time)">
      </el-table-column>

      <el-table-column
        prop="status"
        label="状态"
        :formatter="row => status_data[row.status]">
      </el-table-column>

      <el-table-column
        prop="remark"
        label="备注">
      </el-table-column>

      <el-table-column
        prop="operation"
        label="操作">
        <template slot-scope="scope">
          <el-tooltip content="删除" placement="right" :open-delay="500" :hide-after="2000">
            <el-button size="mini" type="danger" icon="el-icon-remove" circle :disabled="scope.row.status !== 'CREATED'"
                       @click="handleDelete(scope.row)"></el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination :current-page.sync="search_data.page"
                   :page-size.sync="search_data.limit"
                   @size-change="get_data"
                   @current-change="get_data"
                   :page-sizes="[100]"
                   :hide-on-single-page="true"
                   layout="total, sizes, prev, pager, next, jumper"
                   :total="total">
    </el-pagination>

    <UserWebAuthn
      ref="UserWebAuthn"
      :operation_type="operation_type"
    ></UserWebAuthn>

    <el-backtop></el-backtop>
  </div>
</template>

<script>

  import UserSearch from "../../components/user/UserSearch";
  import UserWebAuthn from "@/components/UserWebAuthn.vue";
  export default {
    components: {UserWebAuthn, UserSearch},
    methods: {
      get_data() {
        this.dialog_show = false;
        this.loading = true;
        this.$axios.get(
          '/api/asset/lock/',
          {params: this.search_data}
        ).then(
          res => {
            if (res.data.code === 0) {
              let data = res.data.data;
              this.items = data.data;
              this.total = data.total;
              this.loading = false;
              this.status_data = data.extra.statuses
            } else {
              this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
            }
          }
        ).catch(err => {
          this.$message.error(`刷新失败! (${err})`);
        });
      },
      handleDownloadTemplate() {
        let url = '/api/asset/lock/template';
        this.$download_from_url(url, 'lock-template.xlsx')
      },
      async beforeImport(file) {
        const UserWebAuthn = this.$refs["UserWebAuthn"];
        await UserWebAuthn.run();
        await UserWebAuthn.handleWebAuthn().then(() => {
          if(UserWebAuthn.success) {
            this.headers["WebAuthn-Token"] = UserWebAuthn.webauthn_token;
            return true;
          } else {
            this.$message.error("WebAuthn校验失败!");
            return false;
          }
        }).catch(err => {
          this.$message.error(`WebAuthn校验失败! (${err})`);
          return false;
        });
      },
      importSuccess(response, file, fileList) {
        if (response.code !== 0) {
          this.$alert(response.message, '错误'
          ).then(() => {
            this.get_data();
          }).catch(() => {
            this.get_data();
          });
          return;
        }
        this.$alert('成功上传' + response.data.count + '条记录', '成功');
        this.get_data();
      },
      importError(err, file, fileList) {
        console.log(err)
        this.$alert('上传失败', '错误'
        ).then(() => {
          this.get_data();
        }).catch(() => {
          this.get_data();
        });
      },
      handleDelete(row) {
        this.$axios.delete(
          `/api/asset/lock/${row.id}`,
        ).then(
          res => {
            this.get_data();
            this.$message.success("删除成功!");
          }
        ).catch(err => {
          this.$message.error(`失败! (${err})`);
        });
      },
    },
    mounted() {
      this.get_data();
    },
    created() {
      this.$sync_router_query(this, 'search_data', {user_id: Number, asset: String});
      this.search_keyword = this.search_data.user_id;
    },
    data() {
      return {
        sort_data: {
          ID: 'ID',
          LOCKED_AT: '锁定时间',
          UNLOCKED_AT: '解锁时间'
        },
        status_data: {},
        show: false,
        operation_type: "ASSET_LOCK",
        items: [],
        loading: true,
        dialog_show: false,
        search_data: {
          sort: 'ID',
          asset: null,
          status: null,
          user_id: null,
          page: 1,
          limit: 100
        },
        total: 0,
        search_keyword: null,
        search_result: [],
      }
    },
    computed: {
      headers() {
        return {
          'AUTHORIZATION': this.$cookies.get('admin_token'),
          'WebAuthn-Token': this.$refs["UserWebAuthn"] !== undefined ? this.$refs["UserWebAuthn"].webauthn_token: "",
          'Operate-Type': this.operation_type,
        }
      }
    }
  }
</script>
