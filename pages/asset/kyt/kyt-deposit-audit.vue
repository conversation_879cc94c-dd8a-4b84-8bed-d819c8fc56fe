<template>
  <div class="wallet-data">
    <el-row type="flex" justify="space-between" align="middle">
      <el-col :span="6">
        <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
          充值风险评估－待审核
        </h2>
      </el-col>

      <el-col :span="18">
        高风险充值分数 : {{ threshold }}
        <el-tooltip content="修改" placement="left" :open-delay="500" :hide-after="2000">
          <el-button icon="el-icon-edit" size="mini" type="primary" circle
                     @click="handleModifyThreshold(threshold)"></el-button>
        </el-tooltip>
      </el-col>
    </el-row>

    <el-form :inline="true">
      <el-form-item label="用户搜索">
        <el-input
          clearable
          v-model="filters.keyword"
          placeholder="手机/邮箱/ID"
          @change="refresh_page"
        ></el-input>
      </el-form-item>

      <el-form-item label="链">
        <el-select v-model="filters.chain"
                   clearable
                   filterable
                   :filter-method="filter_chains"
                   @focus="filter_chains('')"
                   @change="handle_chain_selection"
                   placeholder="<ALL>"
                   style="width: 120px;">
          <el-option v-for="chain in filtered_chains"
                     :key="chain"
                     :label="chain"
                     :value="chain">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="交易ID">
        <el-input
          clearable
          v-model="filters.tx_id"
          placeholder="TXID"
          @change="refresh_page"
        ></el-input>
      </el-form-item>

      <el-form-item label="评估对象">
        <el-input v-model.trim="filters.assess_object"
                  clearable
                  :placeholder="filters.chain? '评估对象' : '选择链后才能筛选评估对象'"
                  :disabled="!filters.chain"
                  @change="refresh_page"
                  style="width: 200px;"/>
      </el-form-item>

      <el-form-item label="状态">
        <el-select v-model="filters.status"
                   clearable
                   placeholder="<ALL>"
                   @change="refresh_page"
                   style="width: 120px;">
          <el-option v-for="(name, status) in statuses"
                     :key="status"
                     :label="name"
                     :value="status">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="风险等级">
        <el-select v-model="filters.risk_level"
                   clearable
                   placeholder="<ALL>"
                   @change="refresh_page"
                   style="width: 120px;">
          <el-option v-for="(name, level) in risk_levels"
                     :key="level"
                     :label="name"
                     :value="level">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item prop="start" label="评估时间">
        <el-date-picker
          @change="handleDateChanged"
          v-model="filters_mid.date_range[0]"
          type="datetime"
          value-format="timestamp"
          placeholder="开始时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item prop="end" label="">
        <el-date-picker
          @change="handleDateChanged"
          v-model="filters_mid.date_range[1]"
          type="datetime"
          value-format="timestamp"
          placeholder="结束时间">
        </el-date-picker>
      </el-form-item>

      <el-form-item prop="start" label="提交资料时间">
        <el-date-picker
          @change="handleDateChanged"
          v-model="filters_mid.date_range2[0]"
          type="datetime"
          value-format="timestamp"
          placeholder="开始时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item prop="end" label="">
        <el-date-picker
          @change="handleDateChanged"
          v-model="filters_mid.date_range2[1]"
          type="datetime"
          value-format="timestamp"
          placeholder="结束时间">
        </el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
          <el-button icon="el-icon-refresh-left"
                     circle
                     @click="refresh_page"/>
        </el-tooltip>
      </el-form-item>

      <el-form-item>
        <el-tooltip content="下载" placement="right" :open-delay="500" :hide-after="2000">
          <el-button icon="el-icon-download" circle type="success" @click="download"></el-button>
        </el-tooltip>
      </el-form-item>
    </el-form>
    <div style="margin-bottom: 10px; color: #606266; font-size: 14px;">
      共 {{ total }} 条记录；
      待审核记录：{{ aq_total }}；
    </div>

    <el-table :data="items"
              v-loading="loading"
              stripe>
      <el-table-column label="流水ID"
                       align="right"
                       width="80px"
                       prop="id"
                       show-overflow-tooltip>
      </el-table-column>
      <el-table-column label="充值风险评估ID"
                       align="right"
                       width="120px"
                       prop="sender_risk_id"
                       show-overflow-tooltip>
        <template slot-scope="scope">{{ scope.row.sender_risk_id? scope.row.sender_risk_id: '-' }}</template>
      </el-table-column>
      <el-table-column label="用户ID"
                       align="right"
                       width="80px"
                       show-overflow-tooltip>
        <template slot-scope="scope">
          <el-link type="primary"
                   :href="`/users/user-details?id=${scope.row.user_id}`"
                   :underline="false"
                   target="_blank">
            {{ scope.row.user_id }}
          </el-link>
        </template>
      </el-table-column>

      <el-table-column label="链"
                       align="left"
                       show-overflow-tooltip
                       width="80px">
        <template slot-scope="scope">
          <el-link v-on:click="filters.chain = scope.row.chain"
                   :underline="false">
            {{ scope.row.chain }}
          </el-link>
        </template>
      </el-table-column>

      <el-table-column label="评估对象"
                       width="300px">
        <template slot-scope="scope">
          <el-popover placement="left" width="240" trigger="hover" :open-delay="100">
            {{ scope.row.assess_object }}
            <el-link :underline="false"
                     style="font-weight: normal"
                     v-clipboard:copy="scope.row.address_from"
                     v-clipboard:success="handle_content_copy">
              <el-tooltip content="复制" placement="right" :open-delay="500" :hide-after="2000">
                <i class="el-icon-document-copy"/>
              </el-tooltip>
            </el-link>

            <span slot="reference">
              <el-link :href="scope.row.address_url"
                       :disabled="!scope.row.address_url"
                       type="primary"
                       target="_blank"
                       :underline="false"
                       style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                {{ scope.row.assess_object }}
              </el-link>
            </span>
          </el-popover>
        </template>
      </el-table-column>

      <el-table-column label="TXID"
                       width="170px">
        <template slot-scope="scope">
          <el-popover v-if="scope.row.tx_id"
                      placement="right"
                      width="240"
                      trigger="hover">
            <el-row type="flex" justify="space-around" align="middle">
              <el-col :span="21">
                {{ scope.row.tx_id }}
              </el-col>
              <el-col :span="2">
                <el-tooltip content="复制" placement="top" :open-delay="500" :hide-after="2000">
                  <el-button icon="el-icon-document-copy"
                             size="mini"
                             circle
                             v-clipboard:copy="scope.row.tx_id"
                             v-clipboard:success="handle_content_copy"></el-button>
                </el-tooltip>
              </el-col>
            </el-row>

            <span slot="reference">
              <el-link :href="scope.row.tx_url"
                       type="primary"
                       target="_blank"
                       :underline="false"
                       style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                <Truncated :text="scope.row.tx_id" head="10" tail="6"/>
              </el-link>
            </span>
          </el-popover>
        </template>
      </el-table-column>

      <el-table-column label="风险等级"
                       :formatter="row => risk_levels[row.risk_level]"
                       align="left"
                       width="100px"
                       show-overflow-tooltip>
      </el-table-column>

      <el-table-column label="币种"
                       width="80px"
                       prop="asset"
                       show-overflow-tooltip>
      </el-table-column>

      <el-table-column label="数量"
                       width="100px"
                       prop="amount"
                       show-overflow-tooltip>
      </el-table-column>

      <el-table-column label="市值"
                       width="100px"
                       prop="amount_usd"
                       show-overflow-tooltip>
      </el-table-column>

      <el-table-column label="评估时间"
                       :formatter="row => format_date(row.created_at)"
                       align="left"
                       width="160px"
                       show-overflow-tooltip>
      </el-table-column>

      <el-table-column label="提交资料时间"
                       :formatter="row => format_date(row.info_updated_at)"
                       align="left"
                       width="160px"
                       show-overflow-tooltip>
      </el-table-column>

      <el-table-column label="分数及风险等级"
                       prop="risk_result"
                       align="left"
                       width="270px"
                       show-overflow-tooltip>
      </el-table-column>

      <el-table-column label="状态"
                       align="left"
                       show-overflow-tooltip
                       width="135px">
        <template slot-scope="scope">
          {{ statuses[scope.row.status] }}
        </template>
      </el-table-column>

      <el-table-column label="备注"
                       prop="remark"
                       align="left"
                       width="70px"
                       show-overflow-tooltip>
      </el-table-column>

      <el-table-column label="操作人"
                       align="right"
                       width="80px"
                       show-overflow-tooltip>
        <template slot-scope="scope">
          <el-link v-if="scope.row.operator" type="primary"
                   :href="`/users/user-details?id=${scope.row.operator}`"
                   :underline="false"
                   target="_blank">
            {{ scope.row.operator_email }}
          </el-link>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <el-table-column label="操作"
                       width="120px">
        <template slot-scope="scope">
          <span>
            <el-link
              :href="`/asset/kyt/kyt-deposit-audit-detail?id=${scope.row.id}`"
              target="_blank" :underline="false">
              <el-tooltip content="详情" placement="left" :open-delay="500" :hide-after="2000">
                <el-button size="mini" type="primary" icon="el-icon-more" circle></el-button>
              </el-tooltip>
            </el-link>
          </span>
          <span>
            <el-tooltip content="备注" placement="left" :open-delay="500" :hide-after="2000">
              <el-button size="mini" type="primary" icon="el-icon-edit" circle
                         @click="handleRemark(scope.row)"></el-button>
            </el-tooltip>
          </span>
        </template>
      </el-table-column>

      <!--      <el-table-column label="..." type="expand" width="40px">-->
      <!--        <template slot-scope="scope">-->
      <!--          <el-form label-position="right" label-width="160px">-->
      <!--            <template v-for="(value, key) in scope.row.details">-->
      <!--              <el-form-item :set="details_explained=scope.row.details_explained">-->
      <!--                <span slot="label"><b>{{ key }}:</b></span>-->
      <!--                <span v-if="details_explained && details_explained[key]"-->
      <!--                      style="white-space: pre-wrap;">{{ JSON.stringify(details_explained[key], null, 4) }}</span>-->
      <!--                <span v-else style="white-space: pre-wrap;">{{ JSON.stringify(value, null, 4) }}</span>-->
      <!--              </el-form-item>-->
      <!--            </template>-->
      <!--          </el-form>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
    </el-table>

    <el-pagination :current-page.sync="filters.page"
                   :page-size.sync="filters.limit"
                   :page-sizes="[50, 100, 200, 500]"
                   :total="total"
                   @size-change="refresh_page"
                   @current-change="handle_page_change"
                   :hide-on-single-page="true"
                   layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>

    <el-backtop/>

    <el-dialog
      :visible.sync="thresholdDialog.show"
      destroy-on-close
      width="30%"
      :before-close="handleClose"
    >
      <el-form :model="thresholdDialog" ref="form" label-width="200px" label-position="right">
        <el-form-item label="现时高风险充值分数：">
          {{ thresholdDialog.value }}
        </el-form-item>

        <el-form-item label="调整后高风险充值分数：">
          <el-input type="number" v-model="thresholdDialog.after" style="width: 80px"></el-input>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="modifyThreshold">确 定</el-button>
        </span>
    </el-dialog>

    <el-dialog title="编辑备注" :visible.sync="dialogRemark.show" width="35%">
      <el-form :model="dialogRemark">
        <el-form-item label="备注:">
          <el-input v-model="dialogRemark.remark"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogRemark.show = false">取 消</el-button>
        <el-button type="primary" @click="editRemark">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import moment from "moment";
import FocusLock from "vue-focus-lock";
import Vue from 'vue';
import VueClipboard from 'vue-clipboard2';
import Truncated from "@/components/Truncated.vue";


Vue.use(VueClipboard);

const binding_url = '/api/asset/deposits/kyt/audit';

export default {
  methods: {
    download() {
      this.$download_from_url(binding_url, 'kyt-audit.xlsx', {...this.filters, export: true})
    },
    get_data() {
      this.loading = true;
      let filters = {...this.filters};
      this.$axios.get(binding_url, {params: filters}).then(res => {
        this.loading = false;
        if (res?.data?.code === 0) {
          let data = res.data.data;
          this.items = data.items;
          this.total = data.total;
          this.aq_total = data.aq_total;
          let extra = data.extra;
          this.chains = extra.chains;
          this.statuses = extra.statuses;
          this.risk_levels = extra.risk_levels;
        } else {
          this.items = [];
          this.total = 0;
          this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
        }
      });
    },
    getThreshold() {
      this.$axios.get('/api/kyt/settings/coinfirm_dangerous_threshold').then(res => {
        this.loading = false;
        if (res?.data?.code === 0) {
          let data = res.data.data;
          this.threshold = data.value;
        } else {
          this.threshold = 0;
          this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
        }
      });
    },
    handleModifyThreshold(value) {
      this.thresholdDialog.show = true;
      this.thresholdDialog.value = value;
    },
    handleClose() {
      this.$confirm('确认关闭？')
        .then(_ => {
          this.thresholdDialog.show = false;
          done();
        })
        .catch(_ => {
        });
    },
    modifyThreshold() {
      this.$axios.put('/api/kyt/settings/coinfirm_dangerous_threshold', {
        'value': this.thresholdDialog.after
      }).then(res => {
        this.loading = false;
        if (res?.data?.code === 0) {
          let data = res.data.data;
          this.threshold = data.value;
          this.thresholdDialog.show = false;
          this.$message.success('修改成功！');
          this.getThreshold();
        } else {
          this.threshold = 0;
          this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
        }
      });
    },
    handleRemark(row) {
      this.dialogRemark.remark = '';
      this.dialogRemark.id = row.id;
      this.dialogRemark.show = true;
    },
    editRemark() {
      this.$axios.patch(`${binding_url}/${this.dialogRemark.id}`, {
        'remark': this.dialogRemark.remark
      }).then(res => {
        this.loading = false;
        if (res?.data?.code === 0) {
          this.dialogRemark.show = false;
          this.$message.success('修改成功！');
          this.get_data();
        } else {
          this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
        }
      });
    },
    refresh_page() {
      this.reset_page();
      this.get_data();
    },
    handleDateChanged() {
      let date_range = this.filters_mid.date_range;
      let date_range2 = this.filters_mid.date_range2;
      Object.assign(this.filters, {
        start_time: date_range && date_range[0] ? date_range[0] / 1000 : null,
        end_time: date_range && date_range[1] ? date_range[1] / 1000 : null,
      });
      Object.assign(this.filters, {
        updated_start_time: date_range2 && date_range2[0] ? date_range2[0] / 1000 : null,
        updated_end_time: date_range2 && date_range2[1] ? date_range2[1] / 1000 : null,
      });
      this.refresh_page();
    },
    handle_chain_selection() {
      let filters = this.filters;
      if (!filters.chain) {
        filters.address_from = null;
      }
      this.refresh_page();
    },
    handle_page_change() {
      this.get_data();
    },
    reset_page() {
      this.filters.page = 1;
    },
    handle_content_copy() {
      this.$message.success('内容已复制到剪贴板');
    },
    filter_chains(chain) {
      let filtered_chains = [...this.chains];
      if (chain) {
        chain = chain.toLowerCase();
        filtered_chains = filtered_chains.filter(c => c.toLowerCase().startsWith(chain));
      }
      this.filtered_chains = filtered_chains;
    },
    format_date(timestamp, pattern = 'YYYY-MM-DD HH:mm:ss') {
      return timestamp ? moment(Number(timestamp) * 1000).format(pattern) : '';
    },
  },
  created() {
    this.$sync_router_query(this, 'filters',
      {
        keyword: String,
        chain: String,
        address_from: String,
        status: String,
        // start_time: Number,
        // end_time: Number,
        // updated_start_time: Number,
        // updated_end_time: Number,
      });
    let filters = this.filters;
    if (filters.start_time && filters.end_time) {
      this.filters_mid.date_range = [filters.start_time * 1000, filters.end_time * 1000];
    }
    if (filters.updated_start_time && filters.updated_end_time) {
      this.filters_mid.date_range2 = [filters.updated_start_time * 1000, filters.updated_end_time * 1000];
    }
  },
  mounted() {
    this.get_data();
    this.getThreshold();
  },
  data() {
    return {
      filters: {
        start_time: null,
        end_time: null,
        updated_start_time: null,
        updated_end_time: null,
        keyword: null,
        chain: null,
        tx_id: null,
        risk_level: null,
        assess_object: null,
        status: 'AUDIT_REQUIRED',
        page: 1,
        limit: 50
      },
      filters_mid: {
        date_range: [null, null],
        date_range2: [null, null],
      },
      threshold: 0,
      items: [],
      total: 0,
      aq_total: 0,
      chains: [],
      filtered_chains: [],
      statuses: {},
      risk_levels: {},
      loading: false,
      thresholdDialog: {
        show: false,
        value: null,
        after: null
      },
      dialogRemark: {
        show: false,
        id: null,
        remark: '',
      }
    }
  },
  watch: {
    chains: {
      handler(chains) {
        this.filtered_chains = [...chains];
      },
      immediate: true
    },
  },
  components: {
    FocusLock,
    Truncated
  }
}
</script>

<!--suppress CssUnusedSymbol -->
<style>
.el-table .approved {
  color: #000000;
  background: #adffa7;
}

.el-table .declined {
  color: #000000;
  background: rgb(255, 139, 139);
}
</style>
