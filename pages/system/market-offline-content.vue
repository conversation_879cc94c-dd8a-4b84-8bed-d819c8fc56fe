<template>
  <div class="table-data">
    <el-row type="flex" justify="space-between" align="middle">
      <el-col :span="22">
        <h2
          style="font-family: 'Microsoft YaHei', Arial, sans-serif"
        >{{ market_type_desc }}市场 下架文案配置</h2>
      </el-col>

      <el-col :span="1">
        <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
          <el-button icon="el-icon-refresh-left" circle @click="get_data"></el-button>
        </el-tooltip>
      </el-col>
    </el-row>

    <h3>市场名称：{{ this.$route.query.market }}</h3>

    <el-collapse v-model="activeNames">
      <template v-if="$route.query.id !== '0' && contents_visible">
        <el-collapse-item name="0">
          <template slot="title">
            <h3 style="font-family: 'Microsoft YaHei', <PERSON>l, sans-serif">下架文案</h3>&#12288;
            <el-tooltip placement="right" :open-delay="500">
              <div slot="content">
                <p>下架文案</p>
              </div>
              <i class="el-icon-question"></i>
            </el-tooltip>
          </template>
          <MessageEditor
            :messageConfig="messageConfig"
            :contents="contents"
            :languages="languages"
            :template_filters="template_filters"
          ></MessageEditor>
        </el-collapse-item>
      </template>
    </el-collapse>
  </div>
</template>

<script>
import Vue from "vue";
import VueClipboard from "vue-clipboard2";
import moment from "moment";
import MessageEditor from "@/components/MessageEditor";

const spot_save_url =
  "/api/spot/markets/offline-contents/spot/${id}/langs/${lang}";
const per_save_url =
  "/api/spot/markets/offline-contents/perpetual/${id}/langs/${lang}";

Vue.use(VueClipboard);

export default {
  components: {
    MessageEditor,
  },
  methods: {
    get_data(market_) {
      let market = this.$route.query.market || market_ || "";
      let market_type = this.$route.query.market_type;
      try {
        this.$axios
          .get(
            `/api/spot/markets/offline-contents?market=${market}&market_type=${market_type}`
          )
          .then((res) => {
            if (res?.data?.code === 0) {
              this.mode = market ? "edit" : "new";
              let data = res.data.data;
              this.assign_form(data);
            } else {
              this.$message.error(
                `请求失败! (code: ${res.data?.code}; message: ${res.data?.message})`
              );
            }
          });
      } catch (e) {
        this.$message.error(`请求失败! (${e})`);
      }
    },
    buildContent(content) {
      return content;
    },
    assign_form(data) {
      this.langs = data.extra.langs;
      this.languages = data.extra.langs;

      this.messageConfig.item_id = data.record.id;
      if (this.mode === "edit") {
        this.contents = data.contents;
        this.contents_visible = true;
      } else {
        this.contents = {};
      }

      let lang_list = Object.keys(this.languages);
      if (!lang_list.includes(this.cur_lang)) {
        this.messageConfig.cur_lang = lang_list[0];
      }

      this.contents = Object.fromEntries(
        lang_list.map((lang) => [
          lang,
          this.buildContent(this.contents[lang]) || {
            content: "",
            title: "",
          },
        ])
      );
    },
    format_date(timestamp, pattern = "YYYY-MM-DD HH:mm:ss") {
      return moment(Number(timestamp) * 1000).format(pattern);
    },
    notice(title, message) {
      this.$alert(message, title, {
        confirmButtonText: "确定",
      });
    },
    res_success_notice(r) {
      let title = "提交成功";
      this.notice(title, title);
    },
    res_fail_notice(res) {
      let title = "提交失败";
      let message = `(code: ${res.data?.code}; message: ${res.data?.message})`;
      this.notice(title, message);
    },
    res_error_notice(error) {
      let title = "提交失败";
      let message = `(code: ${error.response.status}; message: ${error.message})`;
      this.notice(title, message);
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.dialog_push_test = false;
          this.dialog_template = false;
          done();
        })
        .catch((_) => {});
    },
    download_template() {
      let url = "/api/operation/email-push/template";
      this.$download_from_url(url, "email-push-template.xlsx");
    },
    before_upload(file) {
      let name = file.name;
      if (
        !name.endsWith(".xlsx") &&
        !name.endsWith(".xls") &&
        !name.endsWith("csv")
      ) {
        this.$message.error("只能上传excel表格");
        return false;
      }
    },
    upload_success(res, filters_form) {
      if (res?.code === 0) {
        // debugger;
        filters_form.user_whitelist = res.data.items.join();
        this.$message.success(`共导入 ${res.data.total} 条记录`);
      } else {
        this.$message.error(
          `上传失败! (code: ${res?.code}; message: ${res?.message})`
        );
      }
    },
    handle_template() {
      this.dialog_template = true;
      this.template_filters.title = "";
      this.template_query();
    },
    handle_template_change() {
      this.template_filters.title = null;
      this.template_query();
    },
    template_query() {
      this.template_loading = true;
      this.$axios
        .get("/api/operation/templates", { params: this.template_filters })
        .then((res) => {
          this.template_loading = false;
          if (res && res.data.code === 0) {
            let data = res.data.data;
            this.template_items = data.items;
            this.template_total = data.total;
          } else {
            this.items = [];
            this.$message.error(
              `code: ${res.data?.code}; message: ${res.data?.message}`
            );
          }
        });
    },
    upload_error(err) {
      this.$message.error(`上传失败! (${err})`);
    },
  },
  provide() {
    return { testSendFunc: null };
  },
  mounted() {
    this.get_data();
    let market_type = this.$route.query.market_type;
    if (market_type == "SPOT") {
      this.market_type_desc = "现货";
    } else if (market_type == "PERPETUAL") {
      this.market_type_desc = "合约";
    } else {
      this.market_type_desc = "";
    }
  },
  data() {
    return {
      messageConfig: {
        extr_params: {},
        has_title: false,
        use_editor: false,
        save_url:
          this.$route.query.market_type == "SPOT"
            ? spot_save_url
            : per_save_url,
        cur_lang: null,
        has_test: false,
        template_name: "market-offline-content",
      },
      dialog_template: false,
      mode: null,
      contents: {},
      langs: {},
      uploading: false,
      market_type_desc: "",
      template_items: [],
      template_filters: {
        business: "",
        enabled: false,
        title: null,
        page: null,
        limit: 10,
      },
      template_total: 0,
      template_loading: false,

      contents_visible: false,
      languages: {},
      cur_lang: null,

      activeNames: ["0"],
      submit_loading: false,
    };
  },
};
</script>
