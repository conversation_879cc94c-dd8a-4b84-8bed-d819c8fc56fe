<template>
  <div class="table-data">
    <h2 style="font-family: 'Microsoft YaHei',<PERSON>l,sans-serif;">
      IP/设备ID查询
    </h2>

    <el-form :inline="true">
      <el-form-item label="用户">
        <UserSearch v-model="filters.user_id" @change="handle_page_refresh"></UserSearch>
      </el-form-item>

      <el-form-item prop="start" label="注册时间">
        <el-date-picker
          @change="on_datetime_range_changed"
          v-model="filters_mid.datetime_range[0]"
          type="datetime"
          value-format="timestamp"
          placeholder="开始时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item prop="end" label="">
        <el-date-picker
          @change="on_datetime_range_changed"
          v-model="filters_mid.datetime_range[1]"
          type="datetime"
          value-format="timestamp"
          placeholder="结束时间">
        </el-date-picker>
      </el-form-item>

      <el-form-item label="时间范围" :model="filters_mid">
        <el-select clearable v-model="filters_mid.time_type" placeholder="<ALL>" @change="on_time_type_changed">
          <el-option v-for="name in time_option" :key="name.value" :label="name.label" :value="name.value">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="国家">
        <el-select v-model="filters.location_code" clearable filterable placeholder="<ALL>"
          @change="handle_page_refresh">
          <el-option v-for="(code, value) in countries" :key="code" :label="`${code} (${value})`" :value="value" />
        </el-select>
      </el-form-item>

      <el-form-item label="语言">
        <el-select clearable filterable v-model="filters.language" placeholder="<ALL>" style="width: 110px"
          @change="handle_page_refresh">
          <el-option v-for="(code, value) in langs" :key="code" :label="code" :value="value">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
          <el-button icon="el-icon-refresh-left" circle @click="handle_page_refresh"></el-button>
        </el-tooltip>
      </el-form-item>

    </el-form>

    <el-form :inline="true">
      <el-form-item label="批量实时查询" required>
        <el-tooltip class="item" content="批量实时查询导入用户数量不超过200" placement="left-start">
          <i class="el-icon-question" />
        </el-tooltip>
        <el-input v-model="filters.batch_user_ids" style="width: 300px" disabled></el-input>
      </el-form-item>

      <el-form-item>
        <el-tooltip content="下载模板" placement="right" :open-delay="500" :hide-after="2000">
          <el-button type="primary" icon="el-icon-download" circle size="mini" style="margin-left: 10px;"
            @click="downloadTemplate"></el-button>
        </el-tooltip>
      </el-form-item>

      <el-form-item>
        <el-tooltip content="上传" placement="right" :open-delay="500" :hide-after="2000">
          <el-upload ref="upload" action="/api/operation/email-push/imports" name="file" :data="{ value_type: 'user' }"
            :headers="{ AUTHORIZATION: $cookies.get('admin_token') }" :before-upload="before_upload"
            :on-success="res => upload_success(res, false)" :on-error="upload_error" :show-file-list="false">
            <el-button type="primary" size="mini" icon="el-icon-upload" circle style="margin-left: 10px;"></el-button>
          </el-upload>
        </el-tooltip>
      </el-form-item>

      <el-form-item>
        <el-tooltip content="清除导入用户" placement="right" :open-delay="500" :hide-after="2000">
          <el-button type="primary" icon="el-icon-delete" circle size="mini" style="margin-left: 10px;"
            @click="clearImportUsers(false)"></el-button>
        </el-tooltip>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="handle_page_refresh" style="margin-left: 10px;">批量查询</el-button>
      </el-form-item>
    </el-form>

    <el-form :inline="true">
      <el-form-item label="异步查询下载" required>
        <el-tooltip class="item" content="异步查询导入用户数量不超过200000，完成后Excel链接会发送到导出人邮箱" placement="left-start">
          <i class="el-icon-question" />
        </el-tooltip>
        <el-input v-model="async_user_title" style="width: 300px" disabled></el-input>
      </el-form-item>

      <el-form-item>
        <el-tooltip content="下载模板" placement="right" :open-delay="500" :hide-after="2000">
          <el-button type="primary" icon="el-icon-download" circle size="mini" style="margin-left: 10px;"
            @click="downloadTemplate"></el-button>
        </el-tooltip>
      </el-form-item>

      <el-form-item>
        <el-tooltip content="上传" placement="right" :open-delay="500" :hide-after="2000" style="margin-left: 10px;">
          <el-upload ref="upload" action="/api/operation/email-push/imports" name="file" :data="{ value_type: 'user' }"
            :headers="{ AUTHORIZATION: $cookies.get('admin_token') }" :before-upload="before_upload"
            :on-success="res => upload_success(res, true)" :on-error="upload_error" :show-file-list="false">
            <el-button type="primary" size="mini" icon="el-icon-upload" circle></el-button>
          </el-upload>
        </el-tooltip>
      </el-form-item>

      <el-form-item>
        <el-tooltip content="清除导入用户" placement="right" :open-delay="500" :hide-after="2000">
          <el-button type="primary" icon="el-icon-delete" circle size="mini" style="margin-left: 10px;"
            @click="clearImportUsers(true)"></el-button>
        </el-tooltip>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="asyncExport" style="margin-left: 10px;">异步下载</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="items" v-loading="loading" stripe>
      <el-table-column label="用户 ID" align="left">
        <template slot-scope="scope">
          <el-link :href="'/users/user-details?id=' + scope.row.user_id" type="primary" target="_blank"
            :underline="false"
            style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
            {{ scope.row.user_id }}
          </el-link>
        </template>
      </el-table-column>

      <el-table-column label="邮箱" align="left">
        <template slot-scope="scope">
          <el-link :href="'/users/user-details?id=' + scope.row.user_id" type="primary" target="_blank"
            :underline="false"
            style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
            {{ scope.row.email }}
          </el-link>
        </template>
      </el-table-column>

      <el-table-column label="国家" prop="location"></el-table-column>
      <el-table-column label="语言" prop="lang"></el-table-column>
      <el-table-column label="资产折合" prop="total_usd"></el-table-column>

      <el-table-column label="注册IP" prop="reg_ip"></el-table-column>
      <el-table-column label="注册时间" :formatter="row => format_date(row.created_at)" show-overflow-tooltip>
      </el-table-column>

      <el-table-column label="同注册IP账号数" prop="ip_device_data.req_ip_count">
        <template slot-scope="scope">
          <el-link :href="'/users/relation-detail?user_id=' + scope.row.user_id" type="primary" target="_blank"
            :underline="false"
            style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
            {{ scope.row.ip_device_data.req_ip_count }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="最近登录IP" prop="last_login_ip"></el-table-column>
      <el-table-column label="同登录IP账号数" prop="ip_device_data.login_ip_count">
        <template slot-scope="scope">
          <el-link :href="'/users/relation-detail?user_id=' + scope.row.user_id" type="primary" target="_blank"
            :underline="false"
            style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
            {{ scope.row.ip_device_data.login_ip_count }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="最近访问设备" prop="last_device"></el-table-column>
      <el-table-column label="最近设备ID" prop="last_device_id"></el-table-column>
      <el-table-column label="同设备ID账号数" prop="ip_device_data.device_id_count">
        <template slot-scope="scope">
          <el-link :href="'/users/relation-detail?user_id=' + scope.row.user_id" type="primary" target="_blank"
            :underline="false"
            style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
            {{ scope.row.ip_device_data.device_id_count }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="邀请人用户ID" prop="referral_id">
        <template slot-scope="scope" v-if="scope.row.referral_id">
          <el-link :href="'/users/relation-detail?user_id=' + scope.row.referral_id" type="primary" target="_blank"
            :underline="false"
            style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
            {{ scope.row.referral_id }}
          </el-link>
        </template>


      </el-table-column>
      <el-table-column label="相同邀请人用户数" prop="referral_data">
        <template slot-scope="scope">
          <el-link :href="'/users/relation-detail?user_id=' + scope.row.user_id" type="primary" target="_blank"
            :underline="false"
            style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
            {{ scope.row.referral_data }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="站内转账次数" prop="local_transfer_data.count"></el-table-column>
      <el-table-column label="站内转账关联用户数" prop="local_transfer_data.user_count">
        <template slot-scope="scope">
          <el-link :href="'/users/relation-detail?user_id=' + scope.row.user_id" type="primary" target="_blank"
            :underline="false"
            style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
            {{ scope.row.local_transfer_data.user_count }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="最后一次提现地址" prop="last_address"></el-table-column>
      <el-table-column label="同提现地址用户数" prop="withdrawal_address_data">
        <template slot-scope="scope">
          <el-link :href="'/users/relation-detail?user_id=' + scope.row.user_id" type="primary" target="_blank"
            :underline="false"
            style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
            {{ scope.row.withdrawal_address_data }}
          </el-link>
        </template>


      </el-table-column>

    </el-table>

    <el-pagination :current-page.sync="filters.page" :page-size.sync="filters.limit" @size-change="handle_limit_change"
      @current-change="handle_page_change" :page-sizes="[50, 100]" :hide-on-single-page="true"
      layout="total, sizes, prev, pager, next, jumper" :total="total">
    </el-pagination>

    <el-backtop></el-backtop>
  </div>
</template>

<style scoped></style>

<script>
import moment from "moment";
import UserSearch from "~/components/user/UserSearch";

const list_url = '/api/users/relations';
const list_1day_url = '/api/users/relations-1day';
const async_export_url = '/api/users/relations/async-export';

export default {
  components: {
    UserSearch,
  },
  methods: {
    get_data() {
      this.loading = true;
      let method = 'get'
      let req_data = { params: this.filters };
      if (this.filters.batch_user_ids != "") {
        method = 'post';
        req_data = {
          batch_user_ids: this.filters.batch_user_ids,
        };
      }
      let url = list_1day_url;
      if (this.filter_is_one_day_ago()) {
        url = list_url;
      }
      if (this.filters.user_id != "" && this.filters.user_id != null) {
        url = list_1day_url;
      }
      this.$axios[method](url, req_data).then(
        res => {
          this.loading = false;
          if (res && res.data.code === 0) {
            let data = res.data.data;
            this.langs = data.langs;
            this.countries = data.countries;
            this.items = data.items;
            this.total = data.total;
          } else {
            this.langs = {};
            this.countries = {};
            this.items = [];
            this.total = 0;
            this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
          }
        }
      );
    },
    handle_limit_change() {
      this.reset_page();
      this.get_data();
    },
    handle_page_change() {
      this.get_data();
    },
    handle_page_refresh() {
      this.reset_page();
      this.get_data();
    },
    on_datetime_range_changed() {
      this.filters_mid.time_type = null;
      this.handle_page_refresh();
    },
    on_time_type_changed() {
      this.update_datetime_range();
      this.handle_page_refresh();
    },
    update_datetime_range() {
      if (this.filters_mid.time_type === null) {
        return
      }
      const now = new Date();

      let startTime = null;
      let endTime = now;
      switch (this.filters_mid.time_type) {
        case '15min':
          startTime = new Date(now.getTime() - 5 * 60 * 1000);
          break;
        case '30min':
          startTime = new Date(now.getTime() - 30 * 60 * 1000);
          break;
        case '1hour':
          startTime = new Date(now.getTime() - 60 * 60 * 1000);
          break;
        case '1day':
          startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          break;
        default:
          startTime = null;
          endTime = null;
      }
      if (startTime != null && endTime != null) {
        this.filters_mid.datetime_range = [startTime, endTime];
      } else {
        this.filters_mid.datetime_range = [null, null];
      }

    },
    filter_is_one_day_ago(){
      let now = new Date();
      let one_day_ago_time = new Date(now.getTime() - 24 * 60 * 60 * 1000 - 600 * 1000);
      let one_day_ago_ts = one_day_ago_time.getTime();
      if (this.filters.start_time && this.filters.start_time <= one_day_ago_ts){
        return true
      }
      if(this.filters_mid.time_type != "" && this.filters_mid.time_type != null){
        return false
      }
      return false
    },
    reset_page() {
      this.filters.page = 1;
    },
    format_date(timestamp, pattern = 'YYYY-MM-DD HH:mm:ss') {
      return moment(Number(timestamp) * 1000).format(pattern);
    },
    asyncExport() {
      let req_user_ids = this.async_user_ids || "";
      if (req_user_ids === "" || req_user_ids == null) {
        this.$message.error(`异步导入用户为空`);
        return
      }
      let req_data = {
        batch_user_ids: req_user_ids,
      };
      this.$axios.post(async_export_url, req_data).then(
        res => {
          this.loading = false;
          if (res && res.data.code === 0) {
            let data = res.data.data;
            this.$message.success(`异步导出执行中，共 ${data.total} 个用户`);
          } else {
            this.$message.error(`异步导出执行失败 code: ${res.data?.code}; message: ${res.data?.message}`);
          }
        }
      );
    },
    downloadTemplate() {
      let url = '/api/operation/email-push/template';
      this.$download_from_url(url, 'email-push-template.xlsx')
    },
    clearImportUsers(is_async) {
      if (is_async) {
        this.async_user_ids = "";
        this.async_user_title = "";
      } else {
        this.filters.batch_user_ids = "";
      }
    },
    before_upload(file) {
      let name = file.name;
      if (!name.endsWith('.xlsx') && !name.endsWith('.xls') && !name.endsWith('csv')) {
        this.$message.error("只能上传excel表格");
        return false;
      }
    },
    upload_success(res, is_async) {
      if (res?.code === 0) {
        let items = res.data.items;
        let total = res.data.total;
        if (total > 200 && !is_async) {
          this.$message.error(`批量实时查询目前只支持上传200条之内的用户`);
          return
        }
        if (total > 200000 && is_async) {
          this.$message.error(`异步查询目前只支持上传200000条之内的用户`);
          return
        }
        if (is_async) {
          this.async_user_ids = items.join();
          this.async_user_title = `共导入 ${res.data.total} 个用户`
        } else {
          this.filters.batch_user_ids = items.join();
        }
        this.$message.success(`共导入 ${res.data.total} 条记录`);
      } else {
        this.$message.error(`上传失败! (code: ${res?.code}; message: ${res?.message})`);
      }
    },
    upload_error(err) {
      this.$message.error(`上传失败! (${err})`);
    }
  },
  created() {
    this.$sync_router_query(this, 'filters', { user_id: String });
  },
  mounted() {
    this.update_datetime_range();
    this.get_data()
  },
  data() {
    return {
      filters: {
        start_time: null,
        end_time: null,
        user_id: null,
        batch_user_ids: "",
        location_code: null,
        language: null,
        page: 1,
        limit: 50
      },
      filters_mid: {
        datetime_range: [null, null],
        time_type: null
      },
      time_option: [
        { "label": "最近15分钟", "value": "15min" },
        { "label": "最近30分钟", "value": "30min" },
        { "label": "最近60分钟", "value": "1hour" },
        { "label": "最近24小时", "value": "1day" },
      ],
      async_user_ids: "",
      async_user_title: "",
      items: [],
      langs: {},
      countries: {},
      total: 0,
      loading: true,
    }
  },
  watch: {
    'filters_mid.datetime_range':
      function (datetime_range) {
        Object.assign(this.filters, {
          start_time: datetime_range && datetime_range[0] ? datetime_range[0] : null,
          end_time: datetime_range && datetime_range[1] ? datetime_range[1] : null
        });
      }
  }
}
</script>
