<template>
  <el-container>
    <el-main>
      <h2 style="font-family: 'Microsoft YaHei', <PERSON>l, sans-serif">
        代理列表
      </h2>

      <el-row> 数量：{{ total }} </el-row>
      <br />

      <el-form :inline="true">
        <el-form-item label="状态" :model="search_data">
          <el-select
            v-model="search_data.status"
            @change="get_data"
            placeholder="<ALL>"
          >
            <el-option
              v-for="(name, key) in status_dict"
              :key="key"
              :label="name"
              :value="key"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="是否预淘汰" :model="search_data">
          <el-select
            v-model="search_data.check_status"
            @change="get_data"
            clearable
            placeholder="<ALL>"
          >
            <el-option key="CHECK_NOT_PASSED" label="是" value="CHECK_NOT_PASSED"></el-option>
            <el-option key="CHECK_PASSED" label="否" value="CHECK_PASSED"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <UserSearch
            v-model="search_data.user_id"
            @change="get_data"
          ></UserSearch>
        </el-form-item>

        <el-form-item label="类型" :model="search_data">
          <el-select
            v-model="search_data.type"
            @change="get_data"
            clearable
            placeholder="<ALL>"
          >
            <el-option
              v-for="(name, key) in type_dict"
              :key="key"
              :label="name"
              :value="key"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="来源" :model="search_data">
          <el-select
            v-model="search_data.source"
            @change="get_data"
            clearable
            placeholder="<ALL>"
          >
            <el-option
              v-for="(name, key) in source_dict"
              :key="key"
              :label="name"
              :value="key"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="关闭类型" :model="search_data">
          <el-select
            v-model="search_data.delete_type"
            @change="get_data"
            clearable
            placeholder="<ALL>"
          >
            <el-option
              v-for="(name, key) in delete_type_dict"
              :key="key"
              :label="name"
              :value="key"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="所属商务">
          <UserSearch
            v-model="search_data.bus_user_id"
            @change="get_data"
          ></UserSearch>
        </el-form-item>

        <el-form-item label="所属团队">
          <el-select
            v-model="search_data.team_id"
            @change="get_data"
            placeholder="<ALL>"
            filterable
            clearable
          >
            <el-option
              v-for="(name, key) in team_name_map"
              :key="key"
              :label="name"
              :value="key"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="之前归属团队">
          <el-select
            v-model="search_data.last_team_id"
            @change="get_data"
            placeholder="<ALL>"
            filterable
            clearable
          >
            <el-option
              v-for="(name, key) in team_name_map"
              :key="key"
              :label="name"
              :value="key"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="地区">
          <el-select
            v-model="search_data.location_code"
            @change="get_data"
            placeholder="<ALL>"
            filterable
            clearable
          >
            <el-option
              v-for="(name, key) in country_map"
              :key="key"
              :label="name"
              :value="key"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-tooltip
            content="下载"
            placement="right"
            :open-delay="500"
            :hide-after="2000"
          >
            <el-button
              icon="el-icon-download"
              circle
              type="success"
              @click="download"
            ></el-button>
          </el-tooltip>
        </el-form-item>

        <el-form-item>
          <el-tooltip
            content="下载模板"
            placement="right"
            :open-delay="500"
            :hide-after="2000"
          >
            <el-button
              icon="el-icon-download"
              circle
              type="primary"
              @click="downloadTemplate"
            ></el-button>
          </el-tooltip>
        </el-form-item>

        <el-form-item>
          <el-tooltip content="上传" placement="right" :open-delay="500" :hide-after="2000">
            <el-upload
              ref="upload"
              action="/api/bus-amb/bus-ambassadors/template"
              name="batch-upload"
              :show-file-list="false"
              :headers="headers"
              :on-success="importSuccess"
              :on-error="importError"
              accept=".xlsx">
              <el-button type="primary" icon="el-icon-upload" circle
                         @click="$refs.upload.submit();"></el-button>
            </el-upload>
          </el-tooltip>
        </el-form-item>

        <el-form-item>
          <el-tooltip
            content="刷新"
            placement="right"
            :open-delay="500"
            :hide-after="2000"
          >
            <el-button
              icon="el-icon-refresh-left"
              circle
              @click="get_data"
            ></el-button>
          </el-tooltip>
        </el-form-item>

        <el-form-item>
          <el-tooltip
            content="新建"
            placement="right"
            :open-delay="500"
            :hide-after="2000"
          >
            <el-button
              type="primary"
              icon="el-icon-plus"
              circle
              @click="handleCreate"
            ></el-button>
          </el-tooltip>
        </el-form-item>
      </el-form>

      <el-table :data="items" v-loading="loading" width="100%" stripe border @sort-change="handle_sort">
        <el-table-column prop="user_id" label="用户ID" show-overflow-tooltip fixed min-width="120px">
          <template slot-scope="scope">
            <el-link
              :href="scope.row.user_id | user_link"
              type="primary"
              target="_blank"
              :underline="false"
              style="
                width: 100%;
                font-weight: normal;
                display: inline-block;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              "
            >
              {{ scope.row.user_id }}
            </el-link>
          </template>
        </el-table-column>

        <el-table-column show-overflow-tooltip prop="email" label="邮箱" fixed min-width="300px">
          <template slot-scope="scope">
            <el-link
              :href="scope.row.user_id | user_link"
              type="primary"
              target="_blank"
              :underline="false"
              style="
                width: 100%;
                font-weight: normal;
                display: inline-block;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              "
            >
              {{ scope.row.email }}
            </el-link>
            <el-col :span="2">
              <el-tooltip
                content="复制"
                placement="top"
                :open-delay="500"
                :hide-after="2000"
              >
                <el-button
                  icon="el-icon-document-copy"
                  size="mini"
                  circle
                  v-clipboard:copy="scope.row.email"
                  v-clipboard:success="handle_content_copy"
                ></el-button>
              </el-tooltip>
            </el-col>
          </template>
        </el-table-column>

        <el-table-column
          prop="status"
          label="状态"
          :formatter="(row) => status_dict[row.status]"
          show-overflow-tooltip
          min-width="100px"
        >
        </el-table-column>

        <el-table-column
          prop="check_status"
          label="是否预淘汰"
          show-overflow-tooltip
          min-width="120px"
        >
        <template slot-scope="scope">
            <div v-if="scope.row.type === 'NORMAL'">
              {{ scope.row.check_status === 'CHECK_NOT_PASSED' ? '是' : '否' }}
            </div>
            <div v-else>
              /
            </div>
          </template>
        </el-table-column>

        <el-table-column
          prop="type"
          label="类型"
          :formatter="(row) => type_dict[row.type]"
          show-overflow-tooltip
          min-width="140px"
        >
        </el-table-column>

        <el-table-column
          prop="source"
          label="类型"
          :formatter="(row) => source_dict[row.source]"
          show-overflow-tooltip
          min-width="120px"
        >
        </el-table-column>

        <el-table-column prop="total_ref_rate" label="总返佣比例" show-overflow-tooltip min-width="120px">
        </el-table-column>

        <el-table-column prop="rate" label="代理返佣比例" show-overflow-tooltip min-width="120px">
        </el-table-column>

        <el-table-column prop="share_ref_rate" label="商务代理返佣比例" show-overflow-tooltip min-width="140px">
          <template slot-scope="scope">
            <div v-if="scope.row.type === 'NORMAL'">
              {{ scope.row.share_ref_rate }}
            </div>
            <div v-else>
              /
            </div>
          </template>
        </el-table-column>

        <el-table-column
          prop="team_rate"
          label="团队返佣系数"
          show-overflow-tooltip
          min-width="120px"
        >
        </el-table-column>

        <el-table-column
          prop="delay_repay_month"
          label="预付金还款时间"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <div v-if="scope.row.type !== 'BROKER'">
              {{ scope.row.delay_repay_month }} 月
            </div>
            <div v-else>
              /
            </div>
          </template>
        </el-table-column>

        <el-table-column
          prop="bus_user_id"
          label="所属商务"
          show-overflow-tooltip
          min-width="300px"
        >
          <template slot-scope="scope">
            <el-link
              :href="scope.row.bus_user_id | user_link"
              type="primary"
              target="_blank"
              :underline="false"
              style="
                width: 100%;
                font-weight: normal;
                display: inline-block;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              "
            >
              {{ scope.row.bus_name || scope.row.bus_user_id }}
            </el-link>
          </template>
        </el-table-column>

        <el-table-column
          prop="team_name"
          label="所属团队"
          show-overflow-tooltip
          min-width="200px"
        >
        </el-table-column>

        <el-table-column
          prop="last_team_name"
          label="上一次归属团队"
          show-overflow-tooltip
          min-width="200px"
        >
        </el-table-column>



        <el-table-column
          prop="effected_at"
          label="本次成为代理时间"
          :formatter="(row) => $formatDate(row.effected_at)"
          show-overflow-tooltip
          min-width="200px"
        >
        </el-table-column>

        <el-table-column
          prop="bind_bus_at"
          label="绑定商务时间"
          :formatter="(row) => $formatDate(row.bind_bus_at)"
          show-overflow-tooltip
          min-width="200px"
        >
        </el-table-column>

        <el-table-column
          prop="location_name"
          label="地区"
          show-overflow-tooltip
          min-width="120px"
        >
        </el-table-column>

        <el-table-column prop="mobile_num" label="手机号" show-overflow-tooltip min-width="120px">
        </el-table-column>

        <el-table-column prop="refer_code" label="邀请码" show-overflow-tooltip min-width="120px">
          <template slot-scope="scope">
            <div style="display: flex; align-items: center;">
              <span style="flex: 1; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                {{ scope.row.refer_code }}
              </span>
              <el-tooltip
                content="复制"
                placement="top"
                :open-delay="500"
                :hide-after="2000"
              >
                <el-button
                  icon="el-icon-document-copy"
                  size="mini"
                  circle
                  v-clipboard:copy="scope.row.refer_code"
                  v-clipboard:success="handle_content_copy"
                ></el-button>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="effect_refer_count" label="refer总人数" show-overflow-tooltip min-width="120px">
          <template slot="header">
            refer总人数
            <el-tooltip
              content="该代理邀请的总人数（包括普通邀请，平台大使邀请、代理邀请），只要是【生效中】的邀请关系就算。"
              placement="top"
              :open-delay="500"
            >
              <i class="el-icon-question" style="margin-left: 4px; cursor: pointer;"></i>
            </el-tooltip>
          </template>
        </el-table-column>

        <el-table-column label="历史在职+本次在职数据">
          <el-table-column
            prop="amb_refer_count"
            label="refer人数"
            sortable="custom"
            show-overflow-tooltip
            min-width="120px"
          >
          </el-table-column>

          <el-table-column
            prop="amb_refer_trade_count"
            label="refer交易人数"
            sortable="custom"
            show-overflow-tooltip
            min-width="120px"
          ></el-table-column>

          <el-table-column
            prop="this_month_deal_amount"
            label="当月refer交易总额（USD）"
            sortable="custom"
            show-overflow-tooltip
            min-width="120px"
          ></el-table-column>

          <el-table-column
            prop="effect_refer_trade_amount"
            label="refer交易总额（USD）"
            sortable="custom"
            show-overflow-tooltip
            min-width="120px"
          ></el-table-column>

          <el-table-column
            prop="effect_refer_spot_amount"
            label="现货交易额"
            sortable="custom"
            show-overflow-tooltip
            min-width="120px"
          ></el-table-column>

          <el-table-column
            prop="effect_refer_perpetual_amount"
            label="合约交易额"
            sortable="custom"
            show-overflow-tooltip
            min-width="120px"
          ></el-table-column>

          <el-table-column
            prop="month_refer_amount"
            label="当月返佣（USDT）"
            sortable="custom"
            show-overflow-tooltip
            min-width="120px"
          ></el-table-column>

          <el-table-column
            prop="total_refer_amount"
            label="累计返佣（USDT）"
            sortable="custom"
            show-overflow-tooltip
            min-width="120px"
          >
          </el-table-column>
        </el-table-column>

        <el-table-column label="本次在职数据">
          <el-table-column
            prop="new_refer_count"
            label="refer人数"
            sortable="custom"
            show-overflow-tooltip
            min-width="120px"
          >
          </el-table-column>

          <el-table-column
            prop="new_refer_trade_count"
            label="refer交易人数"
            sortable="custom"
            show-overflow-tooltip
            min-width="120px"
          ></el-table-column>

          <el-table-column
            prop="new_this_month_deal_amount"
            label="当月refer交易总额（USD）"
            sortable="custom"
            show-overflow-tooltip
            min-width="120px"
          ></el-table-column>

          <el-table-column
            prop="new_refer_trade_amount"
            label="refer交易总额（USD）"
            sortable="custom"
            show-overflow-tooltip
            min-width="120px"
          ></el-table-column>

          <el-table-column
            prop="new_refer_spot_amount"
            label="现货交易额"
            sortable="custom"
            show-overflow-tooltip
            min-width="120px"
          ></el-table-column>

          <el-table-column
            prop="new_refer_perpetual_amount"
            label="合约交易额"
            sortable="custom"
            show-overflow-tooltip
            min-width="120px"
          ></el-table-column>

          <el-table-column
            prop="new_month_refer_amount"
            label="当月返佣（USDT）"
            sortable="custom"
            show-overflow-tooltip
            min-width="120px"
          ></el-table-column>

          <el-table-column
            prop="new_total_refer_amount"
            label="累计返佣（USDT）"
            sortable="custom"
            show-overflow-tooltip
            min-width="120px"
          >
          </el-table-column>
        </el-table-column>

        <el-table-column
          prop="delete_at"
          label="关闭时间"
          :formatter="(row) => row.delete_at ? $formatDate(row.delete_at): '-'"
          show-overflow-tooltip
          min-width="180px"
        >
        </el-table-column>

        <el-table-column
          prop="delete_type"
          label="关闭类型"
          :formatter="(row) => row.delete_at && row.delete_type ? delete_type_dict[row.delete_type]: '-'"
          show-overflow-tooltip
          min-width="120px"
        >
        </el-table-column>

        <el-table-column prop="remark" label="备注" show-overflow-tooltip min-width="120px">
        </el-table-column>

        <el-table-column prop="operation" label="操作">
          <template slot-scope="scope">
            <el-link
              v-if="
                scope.row.status === 'VALID' || scope.row.status === 'DELETED'
              "
              :href="
                '/users/bus-ambassador/bus-amb-detail?user_id=' +
                scope.row.user_id
              "
              type="primary"
              target="_blank"
              :underline="false"
            >
              <el-tooltip
                content="查看详情"
                placement="right"
                :open-delay="500"
                :hide-after="2000"
              >
                <el-button
                  size="mini"
                  type="primary"
                  icon="el-icon-more"
                  circle
                ></el-button>
              </el-tooltip>
            </el-link>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        :current-page.sync="search_data.page"
        :page-size.sync="search_data.limit"
        @size-change="get_data"
        @current-change="get_data"
        :page-sizes="[50, 100]"
        :hide-on-single-page="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>

      <el-dialog
        :title="action === DIALOG_CREATION ? '添加代理' : '编辑代理'"
        :visible.sync="dialog_show"
        :before-close="handleClose"
        width="80%"
      >
        <el-form
          :model="submit_data"
          ref="submit_data"
          label-width="120px"
          :validate-on-rule-change="false"
        >
          <el-form-item label="用户" required>
            <UserSearch
              v-model="submit_data.user_id"
              :disabled="action === DIALOG_EDIT"
            ></UserSearch>
          </el-form-item>

          <el-form-item label="类型" required>
            <el-select
              v-model="submit_data.type"
              clearable
              filterable
              @change="handle_form_amb_type_change"
              style="width: 300px"
            >
              <el-option
                v-for="(name, key) in type_dict"
                :key="key"
                :label="name"
                :value="key"
              >
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="商务" required>
            <el-select
              v-model="submit_data.bus_user_id"
              clearable
              filterable
              style="width: 300px"
            >
              <el-option
                v-for="(name, uid) in all_bus_name_dict"
                :key="uid"
                :label="uid + ' ' + name"
                :value="uid"
              >
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item prop="rate" label="代理返佣比例" required>
            <el-input-number
              style="width: 300px"
              v-model="submit_data.rate"
              :max="1"
              :min="0"
              :precision="4"
              :step="0.01"
            ></el-input-number>
          </el-form-item>

          <div v-if="submit_data.type === 'NORMAL'">
            <el-form-item label="增加代理瓜分返佣" label-width="200px">
              <el-button
                type="primary"
                @click="create_add_share_user"
                icon="el-icon-plus"
              ></el-button>
            </el-form-item>
            <template v-for="(share_user, index) in submit_data.share_users">
              <el-row :key="index + '_role'">
                <el-col :span="4">
                  <el-form-item
                    label="代理"
                    :prop="'share_users.' + index + '.target_user_id'"
                    required
                  >
                    <UserSearch
                      v-model="share_user.target_user_id"
                    ></UserSearch>
                  </el-form-item>
                </el-col>

                <el-col :span="4" style="margin-left: 20%">
                  <el-form-item
                    label="返佣比例"
                    :prop="'share_users.' + index + '.rate'"
                    required
                  >
                    <el-input-number
                      :max="1"
                      :min="0"
                      :precision="4"
                      :step="0.01"
                      style="width: 240px"
                      v-model="share_user.rate"
                      @input="force_refresh_share_input($event)"
                      placeholder="请输入返佣比例"
                    >
                    </el-input-number>
                  </el-form-item>
                </el-col>

                <el-col :span="2" style="margin-left: 20%">
                  <el-button
                    type="danger"
                    @click="create_remove_share_user(index)"
                    icon="el-icon-minus"
                  ></el-button>
                </el-col>
              </el-row>
            </template>
          </div>

          <el-form-item
            prop="delay_repay_month"
            label="预付金还款时间"
            v-if="submit_data.type !== 'BROKER'"
          >
            <el-input
              style="width: 200px; display:inline-block"
              v-model="submit_data.delay_repay_month"
              placeholder="请输入整数"
            ></el-input>
            <span>月(成为代理时间超过该时长才开始进行预付金还款)</span>
          </el-form-item>
          <el-form-item prop="mobile_num" label="手机号">
            <el-input v-model="submit_data.mobile_num"></el-input>
          </el-form-item>
          <el-form-item prop="remark" label="新增理由">
            <el-input
              v-model="submit_data.remark"
              type="textarea" rows="4"
              placeholder="请简要描述新增该代理的理由，提交后需要等待审核"
            ></el-input>
          </el-form-item>

          <el-form-item label="图片说明">
            <el-upload
              class="img-uploader"
              :class="{disable: this.disable_upload}"
              accept=".jpg, .jpeg, .png, .webp"
              action="/api/upload/image"
              list-type="picture-card"
              :limit="max_img"
              name="img"
              :on-preview="handlePictureCardPreview"
              :headers="{'AUTHORIZATION': $cookies.get('admin_token')}"
              ref="upload"
              :file-list="this.submit_data.file_list"
              :before-upload="before_upload"
              :on-success="upload_success"
              :on-remove="upload_remove"
            >
              <i class="el-icon-plus"></i>
              <div slot="tip" class="el-upload__tip">
                只能上传jpeg/png文件，且大小不超过5Mb，最多5张图片
              </div>
            </el-upload>
          </el-form-item>

        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submit">确 定</el-button>
        </span>
      </el-dialog>
      <el-dialog :visible.sync="dialogVisible">
        <img width="100%" :src="dialogImageUrl" alt="">
      </el-dialog>
      <el-backtop></el-backtop>
    </el-main>
  </el-container>
</template>

<script>
import UserSearch from "../../../components/user/UserSearch";
import _ from "lodash";
import Vue from "vue";
import VueClipboard from "vue-clipboard2";

const amb_list_url = "/api/bus-amb/bus-ambassadors";
const export_url = "/api/bus-amb/bus-ambassadors/export";
const add_amb_audit_url = "/api/bus-amb/bus-ambassador-audits/add-amb";

Vue.use(VueClipboard);
export default {
  components: {UserSearch},
  methods: {
    get_data(sort_col = null, sort_type = null) {
      this.dialog_show = false;
      this.loading = true;
      let new_query = _.omitBy(this.search_data, (value, _) => value === undefined || value === null || value === "")
      if (sort_col && sort_type) {
        new_query.sort_col = sort_col
        new_query.sort_type = sort_type
      }
      this.$axios
        .get(amb_list_url, {params: new_query})
        .then((res) => {
          if (res.data.code === 0) {
            let data = res.data.data;
            this.items = data.items;
            this.total = data.total;
            this.status_dict = data.extra.status_dict;
            this.type_dict = data.extra.type_dict;
            this.check_status_dict = data.extra.check_status_dict;
            this.source_dict = data.extra.source_dict;
            this.delete_type_dict = data.extra.delete_type_dict;
            this.all_bus_name_dict = data.extra.all_bus_name_dict;
            this.team_name_map = data.extra.team_name_map;
            this.country_map = data.extra.country_map;
            this.loading = false;
          } else {
            this.$message.error(
              `code: ${res.data?.code}; message: ${res.data?.message}; data: ${res.data?.data}`
            );
          }
        })
        .catch((err) => {
          this.$message.error(`刷新失败! (${err})`);
        });
    },
    handleCreate() {
      this.action = this.DIALOG_CREATION;
      this.submit_data = _.clone(this.create_data);
      this.dialog_show = true;
    },
    handleClose(done) {
      this.search_keyword = null;
      this.search_result = [];
      this.dialog_show = false;
      done();
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    upload_success(res) {
      let file_list = this.submit_data.file_list
      file_list.push({"name": res.data.file_key, "url": res.data.file_url})
      this.disable_upload = file_list.length >= this.max_img
    },
    upload_remove(file) {
      let file_list = this.submit_data.file_list
      if (file.status === "success"){
        _.pull(file_list, file)
      }
      this.disable_upload = file_list.length >= this.max_img
    },
    upload_error(err) {
      this.$message.error(`上传失败! (${err})`);
    },
    before_upload(file) {
      this.disable_upload = this.submit_data.file_list
      const isAvailableType =
        file.type === 'image/jpeg' ||
        'image/jpg' ||
        'image/gif' ||
        'image/png';
      const isAvailableLength = file.size / 1024 / 1024 < 5;
      if (!isAvailableType) {
        this.$message.error(
          '上传图片只能是 JPG、JPEG、GIF或PNG 格式!'
        );
        return false
      }
      if (!isAvailableLength) {
        this.$message.error('上传图片大小不能超过 5MB!');
        return false
      }
    },
    handle_content_copy() {
      this.$message.success("内容已复制到剪贴板");
    },
    handle_form_amb_type_change(){
      if(this.submit_data.type !== 'NORMAL'){
        this.submit_data.share_users = [];
        this.submit_data.delay_repay_month = null;
      }
    },
    submit() {
      this.$refs["submit_data"].validate((valid) => {
        if (!valid) {
          this.$alert("校验失败请修改", "校验失败请修改", {
            confirmButtonText: "确定",
          });
        } else {
          if (this.action === this.DIALOG_CREATION) {
            this.handleCreateConfirm();
          } else {
          }
        }
      });
    },
    handleCreateConfirm() {
      this.submit_data = _.omitBy(this.submit_data, (value, _) => value === undefined || value === null || value === "")
      if (this.submit_data.file_list.length !== 0) {
        this.submit_data.file_keys = _.map(this.submit_data.file_list, 'name')
      }
      let remark = this.submit_data.remark
      if (remark && remark.length > 1024) {
        this.$message.error("新增理由 长度需要小于1024个字符")
        return
      }
      let bus_user_id = this.submit_data.bus_user_id
      if (bus_user_id == "" || bus_user_id == undefined) {
        this.$message.error("缺少商务")
        return
      }
      this.$axios
        .post(add_amb_audit_url, this.submit_data)
        .then((res) => {
          if (res.data.code === 0) {
            this.$message.success("已提交审核，请耐心等待。");
            this.search_data.status = "CREATED";
            this.get_data();
            this.loading = false;
          } else {
            this.$message.error(
              `code: ${res.data?.code}; message: ${res.data?.message}; data: ${res.data?.data}`
            );
          }
        })
        .catch((err) => {
          this.$message.error(`失败! (${err})`);
        });
    },
    force_refresh_share_input() {
      this.$forceUpdate();
    },
    create_add_share_user() {
      this.submit_data.share_users.push({target_user_id: "", rate: ""});
    },
    create_remove_share_user(index) {
      this.submit_data.share_users.splice(parseInt(index), 1);
    },
    handle_sort(event) {
      let {_, order, prop} = event;
      this.get_data(prop, order);
    },
    download() {
      let params = {...this.search_data, export: 1};
      delete params["page"];
      delete params["limit"];
      this.$axios
        .get(export_url, {params: params, responseType: "blob"})
        .then((res) => {
          const content_type = res.headers["content-type"];
          const url = window.URL.createObjectURL(
            new Blob([res.data], content_type ? {type: content_type} : {})
          );
          const a = document.createElement("a");
          a.href = url;
          document.body.appendChild(a);
          const content_disposition = res.headers["content-disposition"];
          a.download = content_disposition
            ? content_disposition.split("filename=")[1]
            : "temp.xlsx";
          a.click();
          window.URL.revokeObjectURL(url);
        });
    },
    downloadTemplate() {
      let url = '/api/bus-amb/bus-ambassadors/template';
      this.$download_from_url(url, 'bus-ambassadors-template.xlsx');
    },
    importSuccess(response, file, fileList) {
      if (response.code !== 0) {
        this.$alert(response.message, '错误'
        ).then(() => {
          this.get_data();
        }).catch(() => {
          this.get_data();
        });
        return;
      }
      this.$alert('上传成功', '成功');
      this.get_data();
    },
    importError(err, file, fileList) {
      console.log(err)
      this.$alert('上传失败', '错误'
      ).then(() => {
        this.get_data();
      }).catch(() => {
        this.get_data();
      });
    },
  },
  mounted() {
    let bus_user_id = this.$route.query.bus_user_id;
    if (bus_user_id !== undefined) {
      this.bus_user_id = bus_user_id;
      this.search_data.bus_user_id = bus_user_id;
    }
    this.get_data();
  },
  created() {
    this.$sync_router_query(this, "search_data", {
      status: String,
      user_id: Number,
      bus_user_id: Number,
    });
  },
  computed: {
      headers() {
        return {
          'AUTHORIZATION': this.$cookies.get('admin_token'),
        }
      },
    },
  data() {
    return {
      max_img: 5,
      DIALOG_CREATION: "CREATION",
      DIALOG_EDIT: "EDIT",
      rowHasAgent: false,
      action: null,
      show: false,
      items: [],
      loading: true,
      dialog_show: false,
      cur_row: null,
      submit_data: {
        user_id: null,
        bus_user_id: null,
        type: null,
        rate: null,
        mobile_num: null,
        delay_repay_month: null,
        remark: null,
        share_users: [],
        file_list: [],
      },
      create_data: {
        user_id: null,
        bus_user_id: null,
        type: null,
        rate: null,
        mobile_num: null,
        remark: null,
        share_users: [],
        file_list: [],
      },
      search_data: {
        status: "VALID",
        user_id: null,
        bus_user_id: null,
        type: null,
        delete_type: null,
        source: null,
        check_status: null,
        page: 1,
        limit: 100,
      },
      total: 0,
      status_dict: {},
      check_status_dict: {},
      type_dict: {},
      source_dict: {},
      delete_type_dict: {},
      all_bus_name_dict: {},
      search_keyword: null,
      search_result: [],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now();
        },
      },
      disable_upload: false,
      countries: {},
      file_list: [],
      team_name_map: {},
      country_map: {},
      dialogVisible: false,
      dialogImageUrl: ""
    };
  },
};
</script>

<style>
.disable .el-upload--picture-card {
  display: none;
}
</style>
