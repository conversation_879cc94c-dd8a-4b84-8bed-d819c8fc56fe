<template>
  <div class="table-data">
    <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
      安全工具重置>自动审核记录
    </h2>
    <el-row style="margin-bottom: 30px">
      <el-col :span="3">答题用户数：{{statistics.user_count}}</el-col>
      <el-col :span="3">答题次数：{{statistics.answer_count}}</el-col>
    </el-row>
    <el-row style="margin-bottom: 30px">
      <el-col :span="3">成功次数：{{statistics.success_count}}</el-col>
      <el-col :span="3">失败次数：{{statistics.fail_count}}</el-col>
      <el-col :span="3">剩余答题次数：{{statistics.remaining_count}}</el-col>
    </el-row>
    <el-form :inline="true">
      <el-form-item label="状态">
        <el-select v-model="filters.status"
                   clearable
                   placeholder="<ALL>"
                   style="width: 120px;">
          <el-option v-for="(v, k) in statuses"
                     :key="k"
                     :label="v"
                     :value="k">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="类型">
        <el-select v-model="filters.type"
                   clearable
                   placeholder="<ALL>"
                   style="width: 120px;">
          <el-option v-for="(v, k) in types"
                     :key="k"
                     :label="v"
                     :value="k">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="时间">
        <el-col :span="11">
          <el-date-picker
            v-model="filters.start_date"
            type="datetime"
            value-format="timestamp"
            :picker-options="pickerOptions"
            placeholder="开始时间">
          </el-date-picker>
        </el-col>
        <el-col class="line" :span="2">-</el-col>
        <el-col :span="11">
          <el-date-picker
            v-model="filters.end_date"
            type="datetime"
            value-format="timestamp"
            :picker-options="pickerOptions"
            placeholder="结束时间">
          </el-date-picker>
        </el-col>
      </el-form-item>
      <el-form-item label="用户">
        <el-input v-model="filters.keyword"
                  clearable
                  placeholder="ID/邮箱/用户名/手机号"
                  @change="handle_user_keyword_input"
                  style="width: 160px;"></el-input>
      </el-form-item>
      <el-form-item label="身份认证">
        <el-select v-model="filters.kyc_status"
                   clearable
                   placeholder="<ALL>"
                   style="width: 120px;">
          <el-option v-for="(v, k) in kyc_statuses"
                     :key="k"
                     :label="v"
                     :value="k">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="新邮箱">
        <EmailSearch url="/api/operation/2fa-reset/new-email-search" v-model="filters.user_id" width="200px"></EmailSearch>
      </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handle_page_refresh">查询</el-button>
        </el-form-item>
    </el-form>

    <el-table :data="items"
              v-loading="loading"
              stripe>
      <el-table-column label="ID"
                       prop="id"
                       align="left">
      </el-table-column>

      <el-table-column label="答题时间"
                       :formatter="row => format_date(row.created_at)"
                       show-overflow-tooltip>
      </el-table-column>

      <el-table-column label="用户ID"
                       show-overflow-tooltip>
        <template slot-scope="scope">
          <el-link :href="'/users/user-details?id=' + scope.row.user_id"
                   type="primary"
                   target="_blank"
                   :underline="false"
                   style="width: 60%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
            {{scope.row.user_id}}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="身份认证"
                       show-overflow-tooltip>
        <template slot-scope="scope">
          <el-link v-if="scope.row.kyc_status === 'PASSED'" :href="'/operation/kyc-details?id=' + scope.row.kyc_id"
                   type="primary"
                   target="_blank"
                   :underline="false"
                   style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
            {{ kyc_statuses[scope.row.kyc_status] }}
          </el-link>
          <span v-else>
            {{ kyc_statuses[scope.row.kyc_status] }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="邮箱"
                       show-overflow-tooltip>
        <template slot-scope="scope">
          <el-link :href="'/users/user-details?id=' + scope.row.user_id"
                   type="primary"
                   target="_blank"
                   :underline="false"
                   style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
            {{ scope.row.email }}
          </el-link>
        </template>
      </el-table-column>

      <el-table-column label="重置类型"
                       prop="reset_type">
      </el-table-column>

      <el-table-column label="状态"
                       prop="status">
      </el-table-column>
    </el-table>

    <el-pagination :current-page.sync="filters.page"
                   :page-size.sync="filters.limit"
                   :page-sizes="[50, 100, 200, 500]"
                   :total="total"
                   @size-change="handle_limit_change"
                   @current-change="handle_page_change"
                   layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>

    <el-backtop></el-backtop>
  </div>
</template>

<style scoped>
</style>

<script>
  import moment from "moment";
  import EmailSearch from "~/components/user/EmailSearch.vue";

  export default {
    components: {
      EmailSearch
    },
    methods: {
      get_data() {
        this.loading = true;
        this.$axios.get('/api/operation/2fa-reset/answer-history', {params: this.filters}).then(
          res => {
            this.loading = false;
            if (res && res.data.code === 0) {
              let data = res.data.data;
              this.items = data.items;
              this.total = data.total;
              this.statistics = data.statistics;
              this.statuses = data.statuses;
              this.kyc_statuses = data.kyc_statuses
              this.types = data.types;
            } else {
              this.items = [];
              this.total = 0;
              this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
            }
          }
        );
      },
      handle_status_selection() {
        this.reset_page();
        this.get_data();
      },
      handle_user_keyword_input() {
        this.reset_page();
        this.get_data();
      },
      handle_id_num_input() {
        this.reset_page();
        this.get_data();
      },
      handle_date_range_selection() {
        this.reset_page();
        this.get_data();
      },
      handle_limit_change() {
        this.reset_page();
        this.get_data();
      },
      handle_page_change() {
        this.get_data();
      },
      handle_page_refresh() {
        this.reset_page();
        this.get_data();
      },
      reset_page() {
        this.filters.page = 1;
      },
      format_date(timestamp, pattern = 'YYYY-MM-DD HH:mm:ss') {
        return moment(Number(timestamp) * 1000).format(pattern);
      },
      update_router_query() {
        let query = {};
        Object.assign(query, this.filters);
        Object.keys(query).forEach((key) => !query[key] && delete query[key]);
        this.$router.replace({query: query});
      }
    },
    created() {
    },
    mounted() {
      this.get_data();
    },
    data() {
      return {
        filters: {
          status: null,
          kyc_status: null,
          type: null,
          new_email: null,
          start_date: null,
          end_date: null,
          keyword: null,
          page: null,
          limit: 50
        },
        items: [],
        total: 0,
        statuses: {},
        kyc_statuses: {},
        types: {},
        statistics: {},
        loading: true
      }
    },
  }
</script>
