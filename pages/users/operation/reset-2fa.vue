<template>
  <div class="table-data">
    <h2 style="font-family: 'Microsoft YaHei',<PERSON>l,sans-serif;">
      安全工具重置>重置管理
    </h2>

    <el-form :inline="true">
      <el-form-item label="状态">
        <el-select v-model="filters.status"
                   clearable
                   placeholder="<ALL>"
                   style="width: 120px;">
          <el-option v-for="(v, k) in statuses"
                     :key="k"
                     :label="v"
                     :value="k">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="类型">
        <el-select v-model="filters.type"
                   clearable
                   placeholder="<ALL>"
                   style="width: 120px;">
          <el-option v-for="(v, k) in types"
                     :key="k"
                     :label="v"
                     :value="k">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="审核类型">
        <el-select v-model="filters.pass_type"
                   clearable
                   placeholder="<ALL>"
                   style="width: 120px;">
          <el-option v-for="(v, k) in pass_types"
                     :key="k"
                     :label="v"
                     :value="k">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="流程类型">
        <el-select v-model="filters.flow"
                   clearable
                   placeholder="<ALL>"
                   style="width: 120px;">
          <el-option v-for="(v, k) in flows"
                     :key="k"
                     :label="v"
                     :value="k">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="资料类型">
        <el-select v-model="filters.data_type"
                   clearable
                   placeholder="<ALL>"
                   style="width: 120px;">
          <el-option v-for="k in data_types"
                     :key="k"
                     :label="k"
                     :value="k">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="身份认证">
        <el-select v-model="filters.kyc_status"
                   clearable
                   placeholder="<ALL>"
                   style="width: 120px;">
          <el-option value="PASSED" label="已认证"></el-option>
          <el-option value="NONE" label="未认证"></el-option>
          <el-option value="PROCESSING" label="处理中"></el-option>
          <el-option value="FAILED" label="失败"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="提交时资产">
        <el-select v-model="filters.balance_choice"
                   clearable
                   placeholder="<ALL>"
                   style="width: 120px;">
          <el-option v-for="(v, k) in balance_choices"
                     :key="k"
                     :label="v"
                     :value="k">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="时间">
          <el-col :span="11">
            <el-date-picker
              v-model="filters.start_date"
              type="datetime"
              value-format="timestamp"
              :picker-options="pickerOptions"
              placeholder="开始时间">
            </el-date-picker>
          </el-col>
          <el-col class="line" :span="2">-</el-col>
          <el-col :span="11">
            <el-date-picker
              v-model="filters.end_date"
              type="datetime"
              value-format="timestamp"
              :picker-options="pickerOptions"
              placeholder="结束时间">
            </el-date-picker>
          </el-col>
        </el-form-item>
      <el-form-item label="用户">
        <el-input v-model="filters.keyword"
                  clearable
                  placeholder="ID/邮箱/用户名/手机号"
                  @change="handle_user_keyword_input"
                  style="width: 160px;"></el-input>
      </el-form-item>

      <el-form-item label="审核人">
        <el-select v-model="filters.auditor_id"
                   clearable
                   filterable
                   placeholder="<ALL>"
                   style="width: 120px;">
                      <el-option v-for="data in auditor_list"
            :key="data.auditor_id"
            :label="data.name + '(' + data.count + ')'"
            :value="data.auditor_id">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="新邮箱">
        <el-select
          v-model="filters.new_email"
          filterable
          clearable
          reserve-keyword
          placeholder="输入邮箱搜索用户"
          remote
          :remote-method="search_for_email_user"
          v-loading="email_search_loading"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(255, 255, 255, 0.8)"
          @focus="handle_email_search_focus"
          @clear="clear_email_search_result"
          style="width: 200px;">

          <el-option-group
            label="搜索结果"
          >
            <el-option v-for="user in email_search_result"
                       :key="user.id"
                       :label="`${user.id} | ${user.email || ''}`"
                       :value="user.email">
            </el-option>
          </el-option-group>

          <el-option-group
            label="历史搜索"
          >
            <el-option v-for="user in email_search_history"
                       :key="user.id"
                       :label="`${user.id} | ${user.email || ''}`"
                       :value="user.email">
            </el-option>
          </el-option-group>

        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="handle_page_refresh">查询</el-button>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="handleBatchAssign()" :disabled="select_rows.length === 0">批量分配</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="items"
              v-loading="loading"
              @sort-change="handle_sort_change"
              @selection-change="handleSelectionChange"
              stripe>
      <el-table-column
        type="selection" :selectable="checkSelectable">
      </el-table-column>
      <el-table-column label="ID"
                       prop="id"
                       align="left">
      </el-table-column>

      <el-table-column label="申请时间"
                       prop="created_at"
                       sortable="custom"
                       :formatter="row => format_date(row.created_at)">
      </el-table-column>

      <el-table-column label="初审时间"
                        :formatter="row => row.audited_at?format_date(row.audited_at):'/'">
      </el-table-column>

      <el-table-column label="复审时间"
                        :formatter="row => row.checked_at?format_date(row.checked_at):'/'">
      </el-table-column>

      <el-table-column label="用户ID"
                       show-overflow-tooltip>
        <template slot-scope="scope">
          <el-link :href="'/users/user-details?id=' + scope.row.user_id"
                   type="primary"
                   target="_blank"
                   :underline="false"
                   style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
            {{scope.row.user_id}}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="邮箱"
                       show-overflow-tooltip>
        <template slot-scope="scope">
          <el-link :href="'/users/user-details?id=' + scope.row.user_id"
                   type="primary"
                   target="_blank"
                   :underline="false"
                   style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
            {{ scope.row.email }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="身份认证"
                       show-overflow-tooltip>
        <template slot-scope="scope">
          <el-link v-if="scope.row.kyc_status === 'PASSED'" :href="'/operation/kyc-details?id=' + scope.row.kyc_id"
                   type="primary"
                   target="_blank"
                   :underline="false"
                   style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
            {{ kyc_statuses[scope.row.kyc_status] }}
          </el-link>
          <span v-else>
            {{ kyc_statuses[scope.row.kyc_status] }}
          </span>
        </template>
      </el-table-column>

      <el-table-column label="重置类型"
                       prop="reset_type">
      </el-table-column>

      <el-table-column label="状态"
                       prop="status"
                       :formatter="row => statuses[row.status]">
      </el-table-column>
      <el-table-column label="生物识别验证ID"
                       show-overflow-tooltip>
        <template slot-scope="scope">
          <el-link v-if="scope.row.liveness_transaction_id" :href="'/operation/liveness-check?transaction_id=' + scope.row.liveness_transaction_id"
                   type="primary"
                   target="_blank"
                   :underline="false"
                   style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
            {{ scope.row.liveness_id }}
          </el-link>
          <span v-else>
            {{ scope.row.liveness_id }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="审核类型"
                       prop="reset_method">
      </el-table-column>
      <el-table-column label="流程类型"
                       prop="flow"
                       :formatter="row => flows[row.flow]">
      </el-table-column>
      <el-table-column label="资料类型"
                       prop="data_type">
      </el-table-column>
      <el-table-column label="生物识别验证结果"
                       prop="liveness_status"
                       :formatter="row => liveness_statuses[row.liveness_status]">
      </el-table-column>
      <el-table-column label="提交时资产"
                       prop="balance_usd">
      </el-table-column>

      <el-table-column label="审核人" show-overflow-tooltip>
        <template slot-scope="scope">

          <el-link :href="'/users/user-details?id=' + scope.row.auditor_id" type="primary" target="_blank"
            :underline="false"
            style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;"
            v-if="scope.row.auditor_id">
            {{ scope.row.auditor_name || scope.row.auditor_id }}
          </el-link>
          <span v-else
            style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">--</span>
        </template>
      </el-table-column>

      <el-table-column prop="remark" label="备注"> </el-table-column>

      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-tooltip content="编辑备注" placement="right" :open-delay="500" :hide-after="2000">
              <el-button size="mini" type="primary" icon="el-icon-edit" circle @click="comment_row(scope.row)"></el-button>
          </el-tooltip>
          <el-link :href=handle_detail_url(scope)
                   type="primary"
                   target="_blank"
                   :underline="false"
                   style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
            <el-tooltip content="去审核" placement="right" :open-delay="500" :hide-after="2000">
              <el-button size="mini" type="primary" icon="el-icon-more" circle></el-button>
            </el-tooltip>
          </el-link>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination :current-page.sync="filters.page"
                   :page-size.sync="filters.limit"
                   :page-sizes="[50, 100, 200, 500]"
                   :total="total"
                   @size-change="handle_limit_change"
                   @current-change="handle_page_change"
                   layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>

    <el-dialog :visible.sync="assign_show" title="选择审核人">
      <el-form
        :model="this.submit_data"
        label-position="left"
        label-width="80px"
      >
        <el-form-item label="审核人">
          <el-select v-model="batch_data.auditor_ids"
                   multiple
                   clearable
                   filterable
                   placeholder="请选择审核人"
                   style="width: 300px;">
            <el-option v-for="data in auditor_list.filter(item => item.auditor_id !== 0)"
              v-if="data.auditor_id !== -1"
              :key="data.auditor_id"
              :label="data.name + '(' + data.count + ')'"
              :value="data.auditor_id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-alert
            title="分配说明"
            type="info"
            :closable="false"
            show-icon>
            <div slot="default">
              <p>• 选择多个审核人时，系统会自动平均分配任务</p>
              <p>• 若无法整除，多余的任务会随机分配给已选中的审核人</p>
              <p>• 当前已选择 {{select_rows.length}} 条记录</p>
            </div>
          </el-alert>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="batchAssign">确 定</el-button>
      </span>
    </el-dialog>

    <el-backtop></el-backtop>
  </div>
</template>

<style scoped>
</style>

<script>
  import moment from "moment";

  export default {
    methods: {
      get_data() {
        this.loading = true;
        let filters = _.omitBy(this.filters, (value, _) => value === undefined || value === null || value === "");
        this.$axios.get('/api/operation/2fa-reset', {params: filters}).then(
          res => {
            this.loading = false;
            if (res && res.data.code === 0) {
              let data = res.data.data;
              this.items = data.items;
              this.total = data.total;
              this.statuses = data.statuses;
              this.flows = data.flows;
              this.types = data.types;
              this.data_types = data.data_types;
              this.balance_choices = data.balance_choices;
              this.pass_types = data.pass_types;
              this.kyc_statuses = data.kyc_statuses;
              this.liveness_statuses = data.liveness_statuses;
              this.reset_types = data.reset_types;
              this.auditor_list = data.auditor_list;
            } else {
              this.items = [];
              this.total = 0;
              this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
            }
          }
        );
      },
      handle_sort_change({ column, prop, order }) {
        this.filters.order = prop;
        if (order === 'descending') {
          this.filters.order_type = 'desc';
        } else if (order === 'ascending') {
          this.filters.order_type = 'asc';
        } else {
          this.filters.order_type = null;
        }
        this.handle_page_refresh();
      },
      handleSelectionChange(rows) {
        this.select_rows = rows;
      },
      checkSelectable(row, index) {
        if (row.status === "CREATED" || row.status === "AUDITED") {
          return true;
        }
        return false;
      },
      handleBatchAssign() {
        this.assign_show = true;
      },
      batchAssign() {
        if (this.select_rows.length === 0) {
          return;
        }
        if (this.batch_data.auditor_ids.length === 0) {
          this.$message.error("请选择审核人！")
          return;
        }
        this.batch_data.ids = [];
        this.select_rows.forEach((e) => {
          this.batch_data.ids.push(e.id);
        });

        this.$axios.post(
            `/api/operation/2fa-reset/auditors`,
            this.batch_data,
          ).then(
          (res) => {
            if (res.data.code === 0) {
              this.$message.success("已完成待审核人分配！")
              this.loading = false;
              this.assign_show = false;
              this.get_data();
            }
            else {
              this.$message.error(
                `code: ${res.data?.code}; message: ${res.data?.message}`
              );
            }
          }
        )
      },
      comment_row(row) {
        this.$prompt('请输入备注', '', {
          inputValue: row.remark,
          closeOnClickModal: false,
        }).then(({ value }) => {
          this.edit_remark(value, row.id);
        });
      },
      edit_remark(remark, id) {
        let remark_url = `/api/operation/2fa-reset/${id}/remark`
          this.$axios
            .patch(remark_url, { "remark": remark})
            .then(res => {
              if (res?.data?.code === 0) {
                this.get_data();
                this.$message.success('操作成功!');
              } else {
                this.$message.error(`操作失败! (code: ${res.data?.code}; message: ${res.data?.message}; data: ${res.data?.data});`);
              }
            })
            .catch(err => {
              this.$message.error(`操作失败! (${err})`);
            });
      },
      handle_detail_url(scope){
        let base_url = '/users/operation/reset-2fa-detail?application_id=' + scope.row.id
        let paramStr = ''
        let filters = {};
        Object.assign(filters, this.filters);
        Object.keys(filters).forEach(function (key) {
          if(filters[key]) {
            paramStr += `&${key}=${filters[key]}`
          }
        })
        return base_url + paramStr
      },
      handle_status_selection() {
        this.reset_page();
        this.get_data();
      },
      handle_user_keyword_input() {
        this.reset_page();
        this.get_data();
      },
      handle_id_num_input() {
        this.reset_page();
        this.get_data();
      },
      handle_date_range_selection() {
        this.reset_page();
        this.get_data();
      },
      handle_limit_change() {
        this.reset_page();
        this.get_data();
      },
      handle_page_change() {
        this.get_data();
      },
      handle_page_refresh() {
        this.reset_page();
        this.get_data();
      },
      reset_page() {
        this.filters.page = 1;
      },
      format_date(timestamp, pattern = 'YYYY-MM-DD HH:mm:ss') {
        return moment(Number(timestamp) * 1000).format(pattern);
      },
      update_router_query() {
        let query = {};
        Object.assign(query, this.filters);
        Object.keys(query).forEach((key) => !query[key] && delete query[key]);
        this.$router.replace({query: query});
      },
      search_for_email_user(keyword) {
        if (!keyword) {
          this.email_search_result = [];
          return;
        }
        this.email_search_loading = true;
        let __id = Math.random().toString();
        this.__email_search_id = __id;
        this.$axios.get('/api/users/search', {params: {keyword: keyword}}).then(res => {
          if (__id !== this.__email_search_id) {
            return;
          }
          if (res?.data?.code === 0) {
            this.email_search_result = res.data.data.items.filter(user => user.email);
          } else {
            this.email_search_result = [];
          }
        }).finally(() => {
          this.email_search_loading = false;
        })
      },
      clear_email_search_result() {
        this.email_search_result = [];
      },
      handle_email_search_focus() {
        this.email_search_history = this.get_email_search_history();
      },
      update_email_search_history(email) {
        let user = this.email_search_result.find(u => u.email === email);
        if (user) {
          this.insert_into_email_history(user);
        }
      },
      insert_into_email_history(user) {
        let history = this.get_email_search_history();
        let existingIndex = history.findIndex(item => item.id === user.id);

        if (existingIndex !== -1) {
          // 如果已存在，移到最前面
          history.splice(existingIndex, 1);
        }

        history.unshift(user);
        this.set_email_search_history(history);
        this.email_search_history = history;
      },
      get_email_search_history() {
        let ret = localStorage.getItem('email_search_history');
        if (ret) {
          return JSON.parse(ret);
        }
        return [];
      },
      set_email_search_history(val) {
        let slice_val = val.slice(0, 10); // 只保存最近10条
        localStorage.setItem('email_search_history', JSON.stringify(slice_val));
      }
    },
    created() {
    },
    mounted() {
      this.get_data();
    },
    data() {
      return {
        filters: {
          status: "CREATED",
          kyc_status: null,
          balance_choice: null,
          type: null,
          flow: null,
          data_type: null,
          pass_type: null,
          new_email: null,
          start_date: null,
          end_date: null,
          keyword: null,
          page: null,
          limit: 50
        },
        items: [],
        total: 0,
        assign_show: false,
        batch_data: {
          auditor_ids: [],
          ids: []
        },
        select_rows: [],
        auditor_list: [],
        statuses: {},
        liveness_statuses: {},
        flows: {},
        data_types: [],
        types: {},
        loading: true,
        email_search_result: [],
        email_search_history: [],
        email_search_loading: false
      }
    },
  }
</script>
