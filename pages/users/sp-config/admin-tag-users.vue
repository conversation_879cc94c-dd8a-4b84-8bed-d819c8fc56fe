<template>
  <el-container>
    <el-main>
      <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
        标签用户详情
      </h2>

      <el-form :inline="true" :model="filters">

        <el-form-item label="标签名称" style="margin-left: 20px;">
          <el-select filterable v-model="filters.tag_id" @change="handle_page_refresh" style="width: 120px">
            <el-option v-for="(tag_name, key) in tag_name_map" :key="key" :label="tag_name" :value="key">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="账号状态" style="margin-left: 20px;">
          <el-select clearable filterable v-model="filters.status" @change="handle_page_refresh" placeholder="<ALL>" style="width: 120px">
            <el-option v-for="(status_name, key) in status_dict" :key="key" :label="status_name" :value="key"> </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="用户">
          <UserSearch
            v-model="filters.user_id"
            :refresh_method="handle_page_refresh"
            @change="handle_page_refresh"
          ></UserSearch>
        </el-form-item>

        <el-form-item style="margin-left: 40px;">
          <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
            <el-button icon="el-icon-refresh-left" circle @click="handle_page_refresh"></el-button>
          </el-tooltip>
        </el-form-item>

        <el-form-item style="margin-left: 40px;" v-if="!editable_black_list.includes(parseInt(filters.tag_id))">
          <el-tooltip content="添加" placement="right" :open-delay="500" :hide-after="2000">
            <el-button type="primary" icon="el-icon-plus" circle @click="handle_creation"></el-button>
          </el-tooltip>
        </el-form-item>

        <el-form-item>
          <el-tooltip
            content="下载"
            placement="right"
            :open-delay="500"
            :hide-after="2000"
          >
            <el-button
              type="primary"
              icon="el-icon-download"
              circle
              style="margin-left: 10px"
              @click="download"
            ></el-button>
          </el-tooltip>
        </el-form-item>

        <el-form-item>
          <el-tooltip
            content="下载模版"
            placement="right"
            :open-delay="500"
            :hide-after="2000"
          >
            <el-button
              type="warning"
              icon="el-icon-download"
              circle
              style="margin-left: 10px"
              @click="downloadTemplate"
            ></el-button>
          </el-tooltip>
        </el-form-item>

        <el-form-item v-if="!editable_black_list.includes(parseInt(filters.tag_id))">
          <el-tooltip
            content="上传"
            placement="right"
            :open-delay="500"
            :hide-after="2000"
          >
            <el-upload
              ref="upload"
              :action="`/api/admin-tag/users/upload/${this.filters.tag_id}`"
              name="file"
              :data="{ tag_id: this.filters.tag_id }"
              :headers="{ AUTHORIZATION: $cookies.get('admin_token') }"
              :before-upload="before_upload"
              :on-success="(res) => upload_success(res, record)"
              :on-error="upload_error"
              :show-file-list="false"
            >
              <el-button
                type="primary"
                icon="el-icon-upload"
                circle
              ></el-button>
            </el-upload>
          </el-tooltip>
        </el-form-item>

      </el-form>

      <el-table :data="items"
                style="width: 100%"
                @selection-change="handleSelectionChange"
      >

        <el-table-column
          type="selection">
        </el-table-column>

        <el-table-column prop="user_id" label="用户ID">
          <template slot-scope="scope">
            <el-link
              :href="'/users/user-details?id=' + scope.row.user_id"
              type="primary"
              target="_blank"
              :underline="false"
              style="
              width: 100%;
              font-weight: normal;
              display: inline-block;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            "
            >
              {{ scope.row.user_id }}
            </el-link>
          </template>
        </el-table-column>

        <el-table-column prop="name_displayed" label="用户名">
          <template slot-scope="scope">
            <el-link
              :href="'/users/user-details?id=' + scope.row.user_id"
              type="primary"
              target="_blank"
              :underline="false"
              style="
              width: 100%;
              font-weight: normal;
              display: inline-block;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            "
            >
              {{ scope.row.name_displayed }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="生效状态" :formatter="row => status_dict[row.status]"></el-table-column>
        <el-table-column prop="email" label="邮箱">
          <template slot-scope="scope">
            <el-link
              :href="'/users/user-details?id=' + scope.row.user_id"
              type="primary"
              target="_blank"
              :underline="false"
              style="
              width: 100%;
              font-weight: normal;
              display: inline-block;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            "
            >
              {{ scope.row.email }}
            </el-link>
          </template>735371
        </el-table-column>
        <el-table-column prop="created_at" label="操作时间" :formatter="row => $formatDate(row.updated_at)"> </el-table-column>
        <el-table-column prop="remark" label="备注"> </el-table-column>
        <el-table-column prop="updated_by" label="操作人">
          <template slot-scope="scope">
            <el-link
              :href="'/users/user-details?id=' + scope.row.updated_by"
              type="primary"
              target="_blank"
              :underline="false"
              style="
              width: 100%;
              font-weight: normal;
              display: inline-block;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            "
            >
              {{ scope.row.updated_by }}
            </el-link>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="180">
          <template slot-scope="scope">
            <span v-if="scope.row.status === 'DELETED' && !editable_black_list.includes(scope.row.tag_id)">
                <el-tooltip content="生效" placement="top" :open-delay="500" :hide-after="2000">
                  <el-button size="mini" type="success" icon="el-icon-check" circle
                             @click="set_user_passed(scope.row, 'PASSED')"></el-button>
                </el-tooltip>
            </span>

            <span v-if="scope.row.status === 'PASSED' && !editable_black_list.includes(scope.row.tag_id)">
                <el-tooltip content="失效" placement="right" :open-delay="500" :hide-after="2000">
                  <el-button size="mini" type="danger" icon="el-icon-close" circle
                             @click="set_user_deleted(scope.row, 'DELETED')"></el-button>
                </el-tooltip>
            </span>

            <span style="margin-left: 10px;">
              <el-tooltip content="编辑">
                <el-button
                  size="mini"
                  @click="handle_edit(scope.row)"
                  type="primary"
                  icon="el-icon-edit"
                  circle
                ></el-button>
              </el-tooltip>
            </span>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        :current-page.sync="filters.page"
        :page-size.sync="filters.limit"
        @size-change="handle_limit_change"
        @current-change="handle_page_change"
        :page-sizes="[50, 25]"
        :hide-on-single-page="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>

      <div style="margin-top: 20px" v-if="!editable_black_list.includes(parseInt(filters.tag_id))">
        <el-button @click="set_batch_user_passed" :disabled="select_rows.length === 0">批量生效</el-button>
        <el-button @click="set_batch_user_deleted" :disabled="select_rows.length === 0">批量失效</el-button>
      </div>

      <el-dialog
        :title="dialog_type === DIALOG_CREATION ? '添加用户' : '编辑用户'"
        :visible.sync="dialog_visible"
        destroy-on-close
        width="60%"
        :before-close="handle_close"
      >
        <el-form :model="form_data" ref="form_data" label-width="200px">
          <el-form-item label="用户ID" required>
            <UserSearch
              v-model="form_data.user_id"
              :disabled="dialog_type === DIALOG_EDIT"
              :user_type="`NORMAL,INTERNAL_MAKER,EXTERNAL_MAKER,EXTERNAL_SPOT_MAKER,EXTERNAL_CONTRACT_MAKER,EXTERNAL`"
            ></UserSearch>
          </el-form-item>

          <el-form-item prop="remark" label="备注">
            <el-input v-model="form_data.remark" placeholder="备注"></el-input>
          </el-form-item>

        </el-form>

        <span slot="footer" class="dialog-footer">
          <el-button @click="handle_close">取 消</el-button>
          <el-button type="primary" @click="handle_submit">确 定</el-button>
        </span>
      </el-dialog>

      <el-backtop></el-backtop>
    </el-main>
  </el-container>
</template>

<script>
import moment from 'moment';
import XLSX from "xlsx";
const DIALOG_CREATION = 'creation';
const DIALOG_EDIT = 'edit';
const base_url = '/api/admin-tag/users';
const base_status_url = '/api/admin-tag/users/status';
import UserSearch from "../../../components/user/UserSearch";

export default {
  components: {
    UserSearch,
  },
  methods: {
    notice(title, message) {
      this.$alert(message, title, {
        confirmButtonText: '确定',
      });
    },
    res_success_notice(res) {
      let title = '提交成功';
      this.notice(title, title);
    },
    res_fail_notice(res) {
      let title = '提交失败';
      let message = `(code: ${res.data?.code}; message: ${res.data?.message}; data: ${res.data?.data})`;
      this.notice(title, message);
    },
    res_error_notice(error) {
      let title = '提交失败';
      let message = `(code: ${error.response.status}; message: ${error.message}; data: ${res.data?.data})`;
      this.notice(title, message);
    },
    reset_page() {
      this.filters.page = 1;
    },
    handle_limit_change() {
      this.reset_page();
      this.get_data();
    },
    handle_page_change() {
      this.get_data();
    },
    handle_page_refresh() {
      this.reset_page();
      this.get_data();
    },
    get_data() {
      this.loading = true;
      this.$axios.get(base_url, { params: this.filters }).then(res => {
        this.loading = false;
        if (res?.data?.code === 0) {
          let data = res.data.data;
          this.items = data.items;
          this.total = data.total;
          let extra = data.extra;
          this.status_dict = extra.status_dict;
          this.tag_name_map = extra.tag_name_map;
          this.editable_black_list = extra.editable_black_list;
        } else {
          this.items = [];
          this.total = 0;
          this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
        }
      });
    },
    update_router_query() {
      let query = {};
      Object.assign(query, this.filters);
      Object.keys(query).forEach((key) => !query[key] && delete query[key]);
      this.$router.replace({query: query});
    },
    handle_creation() {
      this.form_data = {
      };
      this.dialog_type = this.DIALOG_CREATION;
      this.dialog_visible = true;
    },
    handleSelectionChange(rows) {
      this.select_rows = rows;
      this.update_select_str();
    },
    update_select_str() {
      if (this.select_rows.length === 0) {
        this.selectVisible = false;
        this.select_str = '';
      } else {
        this.select_str = '已选择' + this.select_rows.length + '条记录';
        this.selectVisible = true;
      }
    },
    set_batch_user_deleted() {
      this.$confirm(`确认批量失效?`).then(() => {
        this.handleBatchAudit('DELETED');
      });
    },
    set_batch_user_passed() {
      this.$confirm(`确认批量生效?`).then(() => {
        this.handleBatchAudit('PASSED');
      });
    },

    handleBatchAudit(status) {
      if (this.select_rows.length === 0) {
        return;
      }
      this.submit_data.ids = [];
      this.select_rows.forEach((e) => {
        this.submit_data.ids.push(e.user_id);
      });
      this.submit_data.tag_id = this.filters.tag_id
      this.submit_data.status = status

      let url = '/api/admin-tag/users/status/batch'

      this.$axios.post(
        url,
        this.submit_data,
        {headers: this.headers}
      ).then(
        res => {
          if (res.data.code === 0) {
            this.$message.success('成功！');
            this.get_data();
          } else {
            this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
          }
        }
      ).catch(err => {
        this.$message.error(`刷新失败! (${err})`);
      });
    },
    handle_edit(row) {
      this.form_data = _.clone(row);
      this.dialog_type = this.DIALOG_EDIT;
      this.dialog_visible = true;
    },
    download() {
      let filename = 'admin_tag_user_list.xlsx'
      let params =  {...this.filters, export: true}
      this.$download_from_url(`${base_url}/export`, filename, params)
    },
    downloadTemplate() {
      // 定义表头数据
      const data = [
        ["UID", "邮箱", "备注"],
      ];
      // 创建工作表
      const worksheet = XLSX.utils.aoa_to_sheet(data);
      // 创建工作簿
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "template");
      // 导出文件
      XLSX.writeFile(workbook, "admin-tag-users-template.xlsx");
    },
    before_upload(file) {
      let name = file.name;
      if (
        !name.endsWith(".xlsx") &&
        !name.endsWith(".xls") &&
        !name.endsWith("csv")
      ) {
        this.$message.error("只能上传excel表格");
        return false;
      }
    },
    upload_success(res, filters_form) {
      if (res?.code === 0) {
        // debugger;
        this.$message.success(`共导入 ${res.data.total} 条记录`);
        this.handle_page_refresh()
      } else {
        this.$message.error(
          `上传失败! (code: ${res?.code}; message: ${res?.message})`
        );
      }
    },
    upload_error(err) {
      this.$message.error(`上传失败! (${err})`);
    },
    handleOnOffline(row, is_online) {
      this.$confirm('确认删除？')
        .then(_ => {
          this.$axios.delete(base_url + '/' + row.id)
            .then(res => {
              if (res?.data?.code === 0) {
                this.res_success_notice(res);
                this.form_data = {};
                this.get_data();
              } else {
                this.res_fail_notice(res);
              }
            })
            .catch(e => {
              this.res_error_notice(e);
            });
        })
        .catch(_ => {});
    },
    handle_close() {
      this.$confirm('确认关闭？')
        .then(_ => {
          this.dialog_visible = false;
          this.form_data = {};
          done();
        })
        .catch(_ => {
        });
    },
    handle_submit() {
      this.$refs['form_data'].validate(valid => {
        if (!valid) {
          this.$alert('校验失败请修改', '校验失败请修改', {
            confirmButtonText: '确定',
          });
          return false;
        } else {
          if (this.form_data.remark && this.form_data.remark.length > 50) {
            this.$alert('备注太长')
            return false;
          }
          this.submit_validate_data();
        }
      });
    },
    set_user_deleted(row) {
      this.$confirm(`确认失效 ${row.name_displayed}?`).then(() => {
        this.set_user_status(row, 'DELETED');
      });
    },
    set_user_passed(row) {
      this.$confirm(`确认生效 ${row.name_displayed}?`).then(() => {
        this.set_user_status(row, 'PASSED');
      });
    },
    set_user_status(row, status) {
      let form = {
        status: status,
        user_id: row.user_id,
        tag_id: this.filters.tag_id,
      }
      this.$axios.post(base_status_url, form).then(res => {
        if (res?.data?.code === 0) {
          this.$message.success("操作成功!");
          this.get_data();
        } else {
          this.$message.error(`操作失败! (code: ${res.data?.code}; message: ${res.data?.message})`);
        }
      }).catch(err => {
        this.$message.error(`操作失败! (${err})`);
      });
    },

    handleDownloadTemplate() {
      let url = "/api/activity/calendar/template";
      this.$download_from_url(url, 'calendar-template.xlsx')
    },
    importError(err, file, fileList) {
      this.$alert("上传失败", "错误")
    },
    importSuccess(response, file, fileList) {
      if (response.code !== 0) {
        this.$alert(response.message, '错误'
        ).then(() => {
        }).catch(() => {
        });
        return;
      }
      this.$alert('已成功上传，请检查修后的翻译', '成功');

      let tans_data = {}

      response.data.forEach((e) => {
        tans_data[e.lang] = e.title
      })
      this.form_data.details.forEach((e) => {
        let _lang = e.lang
        let _title = tans_data[_lang]
        if (_title) {
          this.$set(e, 'title', _title)
        }

      });
    },
    submit_validate_data() {
      let method = 'post';
      if (this.dialog_type !== DIALOG_CREATION) {
        method = 'put';
      }
      var new_form_data = this.form_data
      new_form_data.tag_id = this.filters.tag_id
      this.$axios[method](base_url, new_form_data)
        .then(res => {
          if (res?.data?.code === 0) {
            this.form_data = {};
            this.dialog_visible = false;
            this.res_success_notice(res);
            this.get_data();
          } else {
            this.res_fail_notice(res);
          }
        })
        .catch(e => {
          console.log(e);
          this.res_error_notice(e);
        });
    },
  },
  created() {
    this.DIALOG_CREATION = DIALOG_CREATION;
    this.DIALOG_EDIT = DIALOG_EDIT;

    let query = this.$route.query;
    let filters = this.filters;
    filters.limit = query.limit ? parseInt(query.limit) : 50;
    filters.page = query.page ? parseInt(query.page) : 1;
    filters.tag_id = query.tag_id ? String(query.tag_id) : '1';
    this.$watch('filters', {
      handler: function () {
        this.update_router_query();
      },
      deep: true
    });
  },
  mounted() {
    this.get_data();
  },
  data() {
    return {
      filters: {
        page: 1,
        limit: 50,
        tag_id: '1',
      },
      items: [],
      select_rows: [],
      submit_data: {
        ids: []
      },
      total: 0,
      status_dict: {},
      tag_name_map: {},
      editable_black_list: [],
      form_data: {
        remark: ''
      },
      dialog_type: null,
      dialog_visible: false,
      loading: true,
      upload_form: {
        type: null
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 86400000;
        },
      },
    };
  },
};
</script>

<style>
.img-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.img-uploader .el-upload:hover {
  border-color: #409eff;
}
.img-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.hide .el-upload--picture-card {
  display: none;
}
</style>
