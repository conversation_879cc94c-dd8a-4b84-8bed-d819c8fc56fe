<template>
  <div class="table-data">
    <el-row type="flex" justify="space-between" align="middle">
      <el-col :span="22">
        <h2 style="font-family: 'Microsoft YaHei', Arial, sans-serif">
          启动页设置
        </h2>
      </el-col>

      <el-col :span="1">
        <el-tooltip
          content="刷新"
          placement="right"
          :open-delay="500"
          :hide-after="2000"
        >
          <el-button
            icon="el-icon-refresh-left"
            circle
            @click="get_data"
          ></el-button>
        </el-tooltip>
      </el-col>
    </el-row>
    <el-collapse v-model="activeNames">
      <el-collapse-item name="0">
        <template slot="title"
          ><h3 style="font-family: 'Microsoft YaHei', Arial, sans-serif">
            基本信息
          </h3></template
        >
        <el-form
          :model="record"
          label-position="left"
          label-width="120px"
          :disabled="disabled"
        >
          <el-form-item label="标题" required>
            <el-input v-model="record.name" style="width: 300px"></el-input>
          </el-form-item>

          <el-form-item label="持续时长"  required>
            <el-input-number :min="3" :max="6" v-model="record.show_time">秒</el-input-number>
          </el-form-item>

          <el-form-item label="落地链接">
            <el-input v-model="record.return_url"></el-input>
          </el-form-item>

          <el-form-item label="弹窗终端" required>
            <el-radio-group
              v-model="record.platform"
            >
              <el-radio
                v-for="(val, key) in platforms"
                :key="key"
                :label="key"
              >{{ val.toUpperCase() }}</el-radio
              >
            </el-radio-group>
          </el-form-item>

          <el-form-item label="弹窗频率" required>
            <el-radio-group v-model="record.frequency">
              <el-radio
                v-for="(val, key) in frequencies"
                :key="key"
                :label="key"
                >{{ val }}</el-radio
              >
            </el-radio-group>
          </el-form-item>

          <el-form-item label="自动上下架时间" required>
            <el-date-picker
              v-model="timerange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
            >
            </el-date-picker>
            <el-row>
              <span>UTC：{{ $formatUTCDate(timerange[0]) }} 至 {{ $formatUTCDate(timerange[1]) }}</span>
            </el-row>
          </el-form-item>

        </el-form>
      </el-collapse-item>

      <el-collapse-item name="1">
        <template slot="title"
          ><h3 style="font-family: 'Microsoft YaHei', Arial, sans-serif">
            目标客群
          </h3></template
        >
        <el-form :disabled="disabled">
          <el-form-item label="客群类型" required>
            <el-radio-group v-model="record.filter_type">
              <el-radio
                v-for="(val, key) in filter_types"
                :key="key"
                :label="key"
                >{{ val }}</el-radio
              >
            </el-radio-group>
          </el-form-item>

          <template v-if="record.filter_type === 'filters'">
            <span v-if="isOldHistory">
              <OldUserGroupPicker
                :filters="record.filters"
                :disabled="this.disabled"
                ref="user_picker"
              ></OldUserGroupPicker>
            </span>
            <span v-else>
              <UserGroupPicker
                :tagGroups="tagGroups"
                :dialogUserGroupMap="dialogUserGroupMap"
              ></UserGroupPicker>
            </span>
            <el-form :model="record" :inline="true">
              <el-form-item>
                <el-checkbox v-model="record.whitelist_enabled"
                  >是否剔除免打扰客群</el-checkbox
                >
              </el-form-item>
              <template v-if="record.whitelist_enabled">
                <el-form-item>
                  <el-input
                    v-model="record.user_whitelist"
                    :disabled="true"
                  ></el-input>
                </el-form-item>
                <el-form-item>
                  <el-tooltip
                    content="上传"
                    placement="right"
                    :open-delay="500"
                    :hide-after="2000"
                  >
                    <el-upload
                      ref="upload"
                      action="/api/operation/email-push/imports"
                      name="file"
                      :data="{ value_type: 'user' }"
                      :headers="{ AUTHORIZATION: $cookies.get('admin_token') }"
                      :before-upload="before_upload"
                      :on-success="(res) => upload_success(res, record)"
                      :on-error="upload_error"
                      :show-file-list="false"
                    >
                      <el-button
                        type="primary"
                        size="mini"
                        icon="el-icon-upload"
                        circle
                      ></el-button>
                    </el-upload>
                  </el-tooltip>
                </el-form-item>
                <el-form-item>
                  <el-tooltip
                    content="下载模板"
                    placement="right"
                    :open-delay="500"
                    :hide-after="2000"
                  >
                    <el-button
                      type="primary"
                      icon="el-icon-download"
                      circle
                      size="mini"
                      style="margin-left: 10px"
                      @click="download_template"
                    ></el-button>
                  </el-tooltip>
                </el-form-item>
              </template>
            </el-form>
          </template>
          <el-form-item>
            <el-button type="primary" @click="save" :loading="submit_loading"
              >保存配置</el-button
            >
          </el-form-item>
        </el-form>
      </el-collapse-item>

      <template v-if="$route.query.id !== '0' && contents_visible">
        <el-collapse-item name="2">
          <template slot="title">
            <h3 style="font-family: 'Microsoft YaHei', Arial, sans-serif">
              通知内容
            </h3>
            &#12288;
          </template>
          <el-tabs v-model="cur_lang" type="card">
            <template v-for="(name, lang) in languages">
              <el-tab-pane :label="name" :name="lang" :key="lang">
                  <span>APP启动页图片</span>
                  <el-upload
                    class="img-uploader"
                    accept=".jpg, .jpeg, .png, .webp"
                    action="/api/upload/image"
                    name="img"
                    :limit="1"
                    list-type="picture-card"
                    :file-list="contents[cur_lang].images"
                    :headers="{ AUTHORIZATION: $cookies.get('admin_token') }"
                    :on-success="
                      (res, file, fileList) =>
                        uploadOneImage(res, cur_lang, file, fileList)
                    "
                    :on-remove="handleRemoveImage"
                    :disabled="disabled"
                  >
                    <i class="el-icon-plus"></i>
                    <div slot="tip" class="el-upload__tip">
                      只能上传jpg/jpeg/png文件, 图片尺寸要求：宽 840 * 高 1050
                    </div>
                  </el-upload>
                <br />
                <el-divider></el-divider>
                <div>
                  <el-button
                    type="primary"
                    @click="save_contents(lang)"
                    :disabled="disabled"
                  >
                    保存当前语言
                  </el-button>

                  <el-button
                    type="primary"
                    @click="save_contents('')"
                    :disabled="disabled"
                  >
                    保存所有语言
                  </el-button>
                </div>
              </el-tab-pane>
            </template>
          </el-tabs>
        </el-collapse-item>
      </template>
    </el-collapse>

    <UserTagGroupDialog
      :tagGroups="tagGroups"
      :dialogUserGroupMap="dialogUserGroupMap"
    ></UserTagGroupDialog>
  </div>
</template>

<script>
import Vue from "vue";
import tinymce from "tinymce/tinymce";
import "tinymce/themes/modern/theme";
import Editor from "@tinymce/tinymce-vue";
import "tinymce/plugins/image";
import "tinymce/plugins/link";
import "tinymce/plugins/code";
import "tinymce/plugins/table";
import "tinymce/plugins/lists";
import "tinymce/plugins/contextmenu";
import "tinymce/plugins/wordcount";
import "tinymce/plugins/colorpicker";
import "tinymce/plugins/textcolor";
import VueClipboard from "vue-clipboard2";
import _ from "lodash";
import { Chart } from "highcharts-vue";
import UserTagGroupDialog from "@/components/UserTagGroupDialog";
import UserGroupPicker from "@/components/UserGroupPicker";
import OldUserGroupPicker from "@/components/OldUserGroupPicker";
import { readFile } from "@/plugins/tools";
import XLSX from "xlsx";

Vue.use(VueClipboard);

export default {
  components: {
    Editor,
    highcharts: Chart,
    UserTagGroupDialog,
    UserGroupPicker,
    OldUserGroupPicker,
  },
  methods: {
    get_data(_id = 0) {
      let id = this.$route.query.id || _id || 0;
      try {
        this.$axios.get(`/api/operation/page/app-start-page/${id}`).then((res) => {
          if (res?.data?.code === 0) {
            this.mode = id ? "edit" : "new";
            let data = res.data.data;
            this.assign_form(data);
          } else {
            this.$message.error(
              `请求失败! (code: ${res.data?.code}; message: ${res.data?.message})`
            );
          }
        });
      } catch (e) {
        this.$message.error(`请求失败! (${e})`);
      }
    },

    buildContent(content) {
      if (content) {
        content.images.forEach((item, index, array) => {
          array[index] = { uid: index, url: item };
        });
      }
      return content;
    },

    assign_form(data) {
      this.tagGroups = data.extra.tag_groups || [];
      this.langs = data.extra.langs;
      this.platforms = data.extra.platforms;
      this.frequencies = data.extra.frequencies;
      this.filter_types = data.extra.filter_types;
      this.languages = data.extra.langs;
      this.param_types = data.extra.param_types;
      // this.page_ops = data.extra.page_ops;
      this.markets = data.extra.markets;
      this.margin_markets = data.extra.margin_markets;
      this.assets = data.extra.assets;
      this.margin_assets = data.extra.margin_assets;
      this.perpetual_markets = data.extra.perpetual_markets;
      this.perpetual_assets = data.extra.perpetual_assets;
      this.series_data = data.extra.series_data;

      if (this.mode === "edit") {
        this.record = data.record;
        this.handleHistoryOnEdit(this.record);
        this.contents = data.contents;
        this.contents_visible = true;
        this.timerange = [
          new Date(this.record.started_at * 1000),
          new Date(this.record.ended_at * 1000),
        ];
        let status = data.record.status;
        let has_content = data.record.has_content;
        this.activeNames = this.build_activeNames(status, has_content);
        // 如果以上架且有内容，就冻结所有选项
        this.disabled = ((status === 'online' && has_content) || status === "offline");
        // this.disabled = status === "offline";
      } else {
        this.record = {
          platform: "ALL",
          frequency: "once",
          filter_type: "filters",
          user_whitelist: "",
        };
        this.contents = {};
      }

      let lang_list = Object.keys(this.languages);
      if (!lang_list.includes(this.cur_lang)) {
        this.cur_lang = lang_list[0];
      }
      this.contents = Object.fromEntries(
        lang_list.map((lang) => [
          lang,
          this.buildContent(this.contents[lang]) || {
            images: [],
          },
        ])
      );
    },

    handleHistoryOnEdit(row) {
      let timePoint = new Date(2022, 11, 8).getTime();
      this.isOldHistory = row.created_at * 1000 <= timePoint;
      if (!this.isOldHistory) {
        return;
      }
      this.disabled = true;
    },
    save() {
      let canPostReq = this.beforeSaveRule();
      if (!canPostReq) {
        return;
      }
      let groups = [];
      this.tagGroups.forEach((group) => {
        groups.push(group.id);
      });
      this.record.groups = groups;
      let promise = null;
      if (this.mode === "edit") {
        promise = this.$axios.patch(
          `/api/operation/page/app-start-page/${this.record.id}`,
          this.record
        );
      } else if (this.mode === "new") {
        promise = this.$axios.post("/api/operation/page/app-start-page", this.record);
      } else {
        return;
      }
      this.submit_loading = true;
      try {
        promise.then((res) => {
          if (res?.data?.code === 0) {
            let id = res.data.data.id;
            this.$message.success("保存成功");
            if (this.mode === "new") {
              this.$router.replace({ query: { id: id } });
              this.mode = "edit";
              this.record.id = id;
            }
            this.get_data(id);
            this.activeNames = ["0", "1", "2"];
          } else {
            this.$message.error(`请求失败! ${res.data?.message})`);
          }
          this.submit_loading = false;
        });
      } catch (e) {
        this.$message.error(`请求失败! (${e})`);
        this.submit_loading = false;
      }
    },
    beforeSaveRule() {
      let form = this.record;
      if (!form.name) {
        this.$message.error("请输入名称!");
        return false;
      }
      if (!form.platform) {
        this.$message.error("请选择弹窗终端!");
        return false;
      }
      let checkSuccess = true;
      if (!checkSuccess) {
        this.$message.error("请检查触发页面配置是否完善!");
        return false;
      }
      if (!form.show_time) {
        this.$message.error("请选择持续时长!");
        return false;
      }
      if (!form.frequency) {
        this.$message.error("请选择弹窗频率!");
        return false;
      }
      if (!form.started_at && !form.ended_at) {
        this.$message.error("请选择自动上下架时间!");
        return false;
      }
      if (!form.filter_type) {
        this.$message.error("请选择客群类型!");
        return false;
      }
      if (form.filter_type !== "none") {
        if (!this.tagGroups || this.tagGroups.length === 0) {
          this.$message.error("请选择客群!");
          return;
        }
      }
      return true;
    },
    save_contents(lang = "", silent = false) {
      let contents = this.contents;
      let id = this.$route.query.id;
      let languages = lang ? [lang] : Object.keys(this.languages);
      let putLanguages = [];

      if (!lang) {
        languages.forEach((lang) => {
          let content = contents[lang];
          if (content.images.length !== 0) {
            putLanguages.push(lang);
          }
        });

        if (putLanguages.length === 0) {
          this.$message.error(
            "触达内容不能为空，请至少在一个语言区输入标题与内容/图片!"
          );
          return;
        }
      } else {
        putLanguages = languages;
      }

      putLanguages.forEach((lang) => {
        let content = contents[lang];
        this.$axios
          .put(`/api/operation/page/app-start-page/${id}/langs/${lang}`, {
            images: content.images.map((obj) => {
              return obj.url;
            }),
          })
          .then((res) => {
            if (res?.data?.code === 0) {
              if (!silent) {
                this.$message.success(`${lang} 保存成功!`);
              }
            } else {
              this.$message.error(
                `保存失败! (code: ${res.data?.code}; message: ${res.data?.message})`
              );
            }
          })
          .catch((err) => {
            this.$message.error(`保存失败! (${err})`);
          });
      });
    },
    notice(title, message) {
      this.$alert(message, title, {
        confirmButtonText: "确定",
      });
    },
    download_template() {
      let url = "/api/operation/email-push/template";
      this.$download_from_url(url, "email-push-template.xlsx");
    },
    before_upload(file) {
      let name = file.name;
      if (
        !name.endsWith(".xlsx") &&
        !name.endsWith(".xls") &&
        !name.endsWith("csv")
      ) {
        this.$message.error("只能上传excel表格");
        return false;
      }
    },
    upload_success(res, filters_form) {
      if (res?.code === 0) {
        // debugger;
        filters_form.user_whitelist = res.data.items.join();
        this.$message.success(`共导入 ${res.data.total} 条记录`);
      } else {
        this.$message.error(
          `上传失败! (code: ${res?.code}; message: ${res?.message})`
        );
      }
    },
    uploadOneImage(res, cur_lang, file, fileList) {
      if (res?.code === 0) {
        let images = this.contents[cur_lang].images;
        let url = res.data.file_url;
        images.push({ url: url });
        this.record.imageUrl = url;
      } else {
        _.pull(fileList, file);
        this.$message.error(
          `上传失败! (code: ${res?.code}; message: ${res?.message})`
        );
      }
    },
    handleRemoveImage(file) {
      let images = this.contents[this.cur_lang].images;
      images = _.pull(images, file);
    },
    upload_error(err) {
      this.$message.error(`上传失败! (${err})`);
    },
    build_activeNames(status, has_content) {
      if (status === "pending" || !has_content) {
        return ["0", "1", "2"];
      } else {
        return ["0", "3"];
      }
    },
    async fmt_read_excel_data(json_data) {
      let character = {
        lang: "lang",
        content: "正文",
        title: "标题",
        summary: "摘要",
      };
      let arr = [];
      json_data.forEach((item) => {
        let obj = {};
        for (let key in character) {
          if (!character.hasOwnProperty(key)) break;
          let v = item[character[key]];
          obj[key] = v;
        }
        arr.push(obj);
      });
      return arr;
    },
    async write_data_to_from(data_arr) {
      data_arr.forEach((item) => {
        let lang = item.lang.toUpperCase();
        this.contents[lang].title = item.title;
        this.contents[lang].content = item.content;
        this.contents[lang].summary = item.summary;
      });
      this.$message.success("上传成功");
    },
    async handle_update_template(file) {
      let dataBinary = await readFile(file.raw);
      let workBook = XLSX.read(dataBinary, { type: "binary", cellDates: true });
      let workSheet = workBook.Sheets[workBook.SheetNames[0]];
      let json_data = XLSX.utils.sheet_to_json(workSheet);
      //   console.log(json_data); //这里已经能拿到转换后的json
      let data_arr = await this.fmt_read_excel_data(json_data);
      await this.write_data_to_from(data_arr);
    },
  },
  mounted() {
    tinymce.init({});
    this.get_data();
  },
  data() {
    return {
      isOldHistory: false,
      dialog_template: false,
      mode: null,
      timerange: [],
      record: {},
      contents: {},
      langs: {},
      platforms: {},
      frequencies: {},
      filter_types: {},
      target_pages: {},
      markets: [],
      margin_markets: [],
      assets: [],
      margin_assets: [],
      perpetual_markets: [],
      perpetual_assets: [],
      param_types: {},
      page_ops: { IN: "in" },
      uploading: false,
      upload_tip: null,
      series_data: {},
      template_items: [],
      template_filters: {
        business: "POPUP_WINDOW",
        enabled: true,
        title: null,
        page: null,
        limit: 10,
      },
      template_total: 0,
      template_loading: false,

      tagGroups: [],
      dialogUserGroupMap: {
        dialogUserGroup: false,
        dialogUserGroupTotal: 0,
        dialogUserGroupItems: [],
        group_types: {},
        dialogUserGroupLoading: false,
        dialogUserGroupFilters: {
          name: null,
          page: null,
          limit: 10,
        },
      },

      dayDisplay: false,
      chart_options: {
        series: [
          {
            name: null,
            data: null,
          },
        ],
      },
      DIALOG_CREATION: "CREATION",
      DIALOG_EDIT: "EDIT",
      contents_visible: false,
      languages: {},
      cur_lang: null,

      activeNames: ["0", "1", "2"],
      disabled: false,
      submit_loading: false,

      init: {
        skin_url: "/tinymce/lightgray",
        height: 300,
        content_style: "body { line-height: 2; } p { margin: 0px 0; }",
        plugins:
          "link lists image code table colorpicker textcolor wordcount contextmenu",
        toolbar:
          "bold italic underline strikethrough | fontsizeselect | forecolor backcolor | alignleft aligncenter alignright alignjustify | bullist numlist | outdent indent blockquote | undo redo | link unlink image code | removeformat",
        branding: false,
      },
    };
  },
  watch: {
    timerange(val) {
      this.record.started_at = val && val[0] ? val[0].getTime() / 1000 : null;
      this.record.ended_at = val && val[1] ? val[1].getTime() / 1000 : null;
    },
  },
};
</script>
