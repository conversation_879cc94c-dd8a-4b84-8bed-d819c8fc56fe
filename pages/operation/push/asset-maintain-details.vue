<template>
  <div class="table-data">
    <el-row type="flex" justify="space-between" align="middle">
      <el-col :span="22">
        <h2 style="font-family: 'Microsoft YaHei', Arial, sans-serif">
          币种维护设置
        </h2>
      </el-col>

      <el-col :span="1">
        <el-tooltip
          content="刷新"
          placement="right"
          :open-delay="500"
          :hide-after="2000"
        >
          <el-button
            icon="el-icon-refresh-left"
            circle
            @click="get_data"
          ></el-button>
        </el-tooltip>
      </el-col>
    </el-row>
    <el-collapse v-model="activeNames">
      <el-collapse-item name="0">
        <template slot="title"
        ><h3 style="font-family: 'Microsoft YaHei', Arial, sans-serif">
          基本信息
        </h3></template
        >
        <el-form
          :model="record"
          label-position="left"
          label-width="120px"
          :disabled="disabled"
        >
          <el-form-item label="币种" required>
            <el-select v-model="record.asset"
                       clearable
                       filterable
                       placeholder="<ALL>"
                       style="width: 120px;">
              <el-option v-for="asset in assets"
                         :key="asset"
                         :label="asset"
                         :value="asset">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="终端" required>
            <el-radio-group
              v-model="record.platform"
              @change="changeSelectableTriggerPages"
            >
              <el-radio
                v-for="(val, key) in platforms"
                :key="key"
                :label="key"
              >{{ val.toUpperCase() }}
              </el-radio
              >
            </el-radio-group>
          </el-form-item>

          <el-form-item label="维护类型: " required>
            <el-radio-group
              v-model="record.maintain_type"
            >
              <el-radio
                v-for="(val, key) in maintain_types"
                :key="key"
                :label="key"
              >{{ val }}
              </el-radio
              >
            </el-radio-group>
          </el-form-item>

          <el-form-item label="标签配置: " required>
            <el-checkbox :indeterminate="isIndeterminateTag" v-model="checkAllTag" @change="handleCheckAllTagChange">全选
            </el-checkbox>
            <div style="margin: 15px 0;"></div>
            <el-checkbox-group v-model="record.tag_pages" @change="handleCheckedTagChange">
              <el-checkbox v-for="(val, key) in tag_pages" :label="key" :key="key" v-if="key !== 'ALL'">{{ val }}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>

          <el-form-item label="时间" required>
            <el-date-picker
              v-model="timerange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
            >
            </el-date-picker>
            <el-row>
              <span>UTC：{{ $formatUTCDate(timerange[0]) }} 至 {{ $formatUTCDate(timerange[1]) }}</span>
            </el-row>
          </el-form-item>

          <el-form-item label="是否跳转: " required>
            <el-radio-group v-model="record.jump_page_enabled">
              <el-radio :label="false">否</el-radio>
              <el-radio :label="true">是</el-radio>
            </el-radio-group>
          </el-form-item>

          <span v-if="record.jump_page_enabled">
            <el-form-item label="跳转类型: " required>
              <el-select
                v-model="record.jump_type"
                style="width: 300px"
                @change="getTargetPages()"
              >
                <el-option
                  v-for="(label, value) in selectable_jump_types"
                  :key="value"
                  :label="label"
                  :value="value"
                >
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="公共链接: " required>
              <el-select
                popper-class="jumpManagement"
                v-model="record.jump_id"
                style="width: 300px"
                filterable
              >
                <el-option
                  v-for="target_page in target_pages"
                  :key="target_page.id"
                  :label="`${target_page.remark} -- ${target_page.jump_data}`"
                  :value="target_page.id"
                  width="300px"
                >
                </el-option>
              </el-select>
              <el-button
                icon="el-icon-s-primary"
                type="primary"
                @click="handleJumpPages"
              >跳转管理</el-button
              >
            </el-form-item>
          </span>
          <el-form-item label="提示条配置: ">
            <el-checkbox :indeterminate="isIndeterminateTipBar" v-model="checkAllTipBar"
                         @change="handleCheckAllTipBarChange">全选
            </el-checkbox>
            <div style="margin: 15px 0;"></div>
            <el-checkbox-group v-model="record.tip_pages" @change="handleCheckedTipBarChange">
              <el-checkbox v-for="(val, key) in tip_pages" :label="key" :key="key" v-if="key !== 'ALL'">{{ val }}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="save" :loading="submit_loading"
            >保存配置
            </el-button
            >
          </el-form-item>
        </el-form>
      </el-collapse-item>

      <template v-if="$route.query.id !== '0' && contents_visible">
        <el-collapse-item name="2">
          <template slot="title">
            <h3 style="font-family: 'Microsoft YaHei', Arial, sans-serif">
              提示条内容
            </h3>
            &#12288;
            <el-tooltip placement="right" :open-delay="500">
              <div slot="content">
                <p>
                  若提示条需要跳转，通知文案结尾请手动输入双箭头。双箭头样式：>>
                </p>
              </div>
              <i class="el-icon-question"></i>
            </el-tooltip>
          </template>
          <MessageEditor
            :messageConfig="messageConfig"
            :contents="contents"
            :languages="languages"
            :template_filters="template_filters"
          ></MessageEditor>
        </el-collapse-item>
        <template
          v-if="
            ['FINISHED', 'AUDITED', 'REJECTED', 'FAILED'].includes(
              record.status
            )
          "
        >
          <el-collapse-item name="3">
            <template slot="title"
            ><h3 style="font-family: 'Microsoft YaHei', Arial, sans-serif">
              审核信息
            </h3></template
            >
            <template>
              <el-table
                :data="audit_data"
                border
                :show-header="false"
                style="width: 50%"
              >
                <el-table-column fixed prop="key1"></el-table-column>
                <el-table-column fixed prop="key2">
                  <template slot-scope="scope">
                    <el-link
                      v-if="scope.row.key3"
                      :href="'/users/user-details?id=' + scope.row.key3"
                      type="primary"
                      target="_blank"
                      :underline="false"
                      style="
                          width: 100%;
                          font-weight: normal;
                          display: inline-block;
                          overflow: hidden;
                          text-overflow: ellipsis;
                          white-space: nowrap;
                        "
                    >
                      {{ scope.row.key2 }}
                    </el-link>
                    <span v-else>{{ scope.row.key2 }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </template>
          </el-collapse-item>
        </template>
      </template>
    </el-collapse>

    <el-dialog title="跳转管理" :visible.sync="jump_show" width="70%">
      <el-tooltip
        content="新建"
        placement="right"
        :open-delay="500"
        :hide-after="2000"
      >
        <el-button
          type="primary"
          icon="el-icon-plus"
          circle
          @click="handleJumpCreate"
        ></el-button>
      </el-tooltip>
      <el-table :data="jump_pages" stripe>
        <el-table-column prop="remark" label="跳转标注"></el-table-column>

        <el-table-column prop="jump_data" label="跳转链接"></el-table-column>

        <el-table-column
          prop="jump_type"
          :formatter="(row) => jump_types[row.jump_type]"
          label="跳转类型"
        >
        </el-table-column>

        <el-table-column prop="operation" label="操作">
          <template slot-scope="scope">
            <el-tooltip
              content="编辑"
              placement="right"
              :open-delay="500"
              :hide-after="2000"
            >
              <el-button
                size="mini"
                type="primary"
                icon="el-icon-edit"
                circle
                @click="handleJumpEdit(scope.row)"
              ></el-button>
            </el-tooltip>
            <el-tooltip
              content="删除"
              placement="right"
              :open-delay="500"
              :hide-after="2000"
            >
              <el-button
                size="mini"
                type="danger"
                icon="el-icon-delete"
                circle
                @click="handleDeleteJump(scope.row)"
              ></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <el-dialog
      :title="jump_action === DIALOG_CREATION ? '添加跳转' : '编辑跳转'"
      :visible.sync="jump_action_show"
      :before-close="handleClose"
      width="60%"
    >
      <el-form
        :model="jump_data"
        ref="jump_data"
        label-width="80px"
        :validate-on-rule-change="false"
      >
        <el-form-item label="跳转标示">
          <el-input v-model="jump_data.remark"></el-input>
        </el-form-item>

        <el-form-item label="跳转类型" required>
          <el-select clearable v-model="jump_data.jump_type">
            <el-option
              v-for="(value, key) in jump_types"
              :key="value"
              :label="value"
              :value="key"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="跳转链接" required>
          <el-input v-model="jump_data.jump_data"></el-input>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="jump_submit">确 定</el-button>
      </span>
    </el-dialog>
    <UserTagGroupDialog
      :tagGroups="tagGroups"
      :dialogUserGroupMap="dialogUserGroupMap"
    ></UserTagGroupDialog>
  </div>
</template>
<style>
.jumpManagement {
  width: 900px;
}
</style>
<script>
import Vue from "vue";
import VueClipboard from "vue-clipboard2";
import _ from "lodash";
import {Chart} from "highcharts-vue";
import UserTagGroupDialog from "@/components/UserTagGroupDialog";
import UserGroupPicker from "@/components/UserGroupPicker";
import OldUserGroupPicker from "@/components/OldUserGroupPicker";
import moment from "moment";
import MessageEditor from "@/components/MessageEditor";

Vue.use(VueClipboard);

export default {
  components: {
    highcharts: Chart,
    UserTagGroupDialog,
    UserGroupPicker,
    MessageEditor,
    OldUserGroupPicker,
  },
  methods: {
    get_data(_id = 0) {
      let id = this.$route.query.id || _id || 0;
      try {
        this.$axios.get(`/api/operation/asset-maintain/${id}`).then((res) => {
          if (res?.data?.code === 0) {
            this.mode = id ? "edit" : "new";
            let data = res.data.data;
            this.assign_form(data);
            this.updateSelectableTriggerPages();
            this.fetchJumpPages();
          } else {
            this.$message.error(
              `请求失败! (code: ${res.data?.code}; message: ${res.data?.message})`
            );
          }
        });
      } catch (e) {
        this.$message.error(`请求失败! (${e})`);
      }
    },

    buildContent(content) {
      return content;
    },

    assign_form(data) {
      // debugger
      this.tagGroups = data.extra.tag_groups || [];
      this.langs = data.extra.langs;
      this.maintain_types = data.extra.maintain_types;
      this.tag_pages = data.extra.tag_pages;
      this.tip_pages = data.extra.tip_pages;
      this.platforms = data.extra.platforms;
      this.trigger_pages = data.extra.trigger_pages;
      this.filter_types = data.extra.filter_types;
      this.languages = data.extra.langs;
      this.jump_types = data.extra.jump_types;
      this.param_types = data.extra.param_types;
      this.markets = data.extra.markets;
      this.margin_markets = data.extra.margin_markets;
      this.assets = data.extra.assets;
      this.margin_assets = data.extra.margin_assets;
      this.perpetual_markets = data.extra.perpetual_markets;
      this.perpetual_assets = data.extra.perpetual_assets;
      this.series_data = data.extra.series_data;

      this.selectable_jump_types = {...this.jump_types};
      if (this.mode === "edit") {
        this.record = data.record;
        // this.handleHistoryOnEdit(this.record);
        this.contents = data.contents;
        this.contents_visible = true;
        this.timerange = [
          new Date(this.record.started_at * 1000),
          new Date(this.record.ended_at * 1000),
        ];
        let status = data.record.status;
        let has_content = data.record.has_content;
        this.activeNames = this.build_activeNames(status, has_content);
        // 如果以上架且有内容，就冻结所有选项
        this.disabled =
          (status === "AUDITED" &&
            Number(this.record.started_at * 1000) <= Date.now()) ||
          status === "FINISHED";
        this.push_table_data(data);
      } else {
        this.record = {
          asset: null,
          platform: "ALL",
          maintain_type: null,
          tip_pages: [],
          tag_pages: [],
          trigger_pages: [
            {
              trigger_page: "SPOT_MARKET",
              param_type: "MARKET",
              page_op: "IN",
              trigger_page_params: [],
            },
          ],
          filter_type: "filters",
          jump_id: null,
          jump_page_enabled: false,
          user_whitelist: "",
        };
        this.contents = {};
      }

      let lang_list = Object.keys(this.languages);
      if (!lang_list.includes(this.cur_lang)) {
        this.messageConfig.cur_lang = lang_list[0];
      }
      this.contents = Object.fromEntries(
        lang_list.map((lang) => [
          lang,
          this.buildContent(this.contents[lang]) || {
            content: "",
          },
        ])
      );
    },
    push_table_data(data) {
      this.audit_data.push({
        key1: "创建人",
        key2: data.record.created_user_email,
        key3: data.record.created_by,
      });
      this.audit_data.push({
        key1: "创建时间",
        key2: this.format_date(data.record.created_at),
        key3: null,
      });
      this.audit_data.push({
        key1: "审核人",
        key2: data.record.audited_user_email,
        key3: data.record.audited_by,
      });
      this.audit_data.push({
        key1: "审核结果",
        key2: data.extra.statuses[data.record.status],
        key3: null,
      });
      this.audit_data.push({
        key1: "审核时间",
        key2: data.record.audited_at
          ? this.format_date(data.record.audited_at)
          : "-",
        key3: null,
      });
      this.audit_data.push({
        key1: "审核备注",
        key2: data.record.auditor_remark,
        key3: null,
      });
    },
    format_date(timestamp, pattern = "YYYY-MM-DD HH:mm:ss") {
      return moment(Number(timestamp) * 1000).format(pattern);
    },
    handleHistoryOnEdit(row) {
      let timePoint = new Date(2022, 11, 8).getTime();
      this.isOldHistory = row.created_at * 1000 <= timePoint;
      if (!this.isOldHistory) {
        return;
      }
      this.disabled = true;
    },
    save() {
      let canPostReq = this.beforeSaveRule();
      if (!canPostReq) {
        return;
      }
      let promise = null;
      if (this.mode === "edit") {
        let permitStatusList = [
          "DRAFT",
          "CREATED",
          "AUDITED",
          "REJECTED",
          "FAILED",
        ];
        if (permitStatusList.includes(this.cur_status)) {
          this.$message.error("当前状态不允许修改!");
          return;
        }
        promise = this.$axios.patch(
          `/api/operation/asset-maintain/${this.record.id}`,
          this.record
        );
      } else if (this.mode === "new") {
        promise = this.$axios.post("/api/operation/asset-maintain", this.record);
      } else {
        return;
      }
      this.submit_loading = true;
      try {
        promise.then((res) => {
          if (res?.data?.code === 0) {
            let id = res.data.data.id;
            this.$message.success("保存成功");
            if (this.mode === "new") {
              this.$router.replace({query: {id: id}});
              this.mode = "edit";
              this.record.id = id;
            }
            this.get_data(id);
            this.activeNames = ["0", "1", "2"];
          } else {
            this.$message.error(`请求失败! ${res.data?.message})`);
          }
          this.submit_loading = false;
        });
      } catch (e) {
        this.$message.error(`请求失败! (${e})`);
        this.submit_loading = false;
      }
    },
    beforeSaveRule() {
      let form = this.record;
      if (!form.asset) {
        this.$message.error("请输入币种!");
        return false;
      }
      if (!form.platform) {
        this.$message.error("请选择终端!");
        return false;
      }
      if (!form.maintain_type) {
        this.$message.error("请选择维护类型!");
        return false;
      }
      if (form.tag_pages.length === 0) {
        this.$message.error("请选择标签配置!");
        return false;
      }
      let checkSuccess = true;
      if (!form.started_at && !form.ended_at) {
        this.$message.error("请选择时间!");
        return false;
      }
      if (!form.jump_page_enabled) {
        form.jump_page_enabled = false;
        delete form.jump_id;
        delete form.jump_type;
      } else {
        if (!form.jump_type) {
          this.$message.error("请输入跳转类型!");
          return false;
        }
        if (!form.jump_id) {
          this.$message.error("请输入跳转目标页!");
          return false;
        }
      }
      return true;
    },

    fetchJumpPages() {
      let url = "/api/operation/page/new-app-entrances/jump-list";
      let jump_pages = this.jump_pages;
      this.$axios.get(url).then((res) => {
        if (res?.data?.code === 0) {
          jump_pages = res.data.data;
          jump_pages.forEach((e) => {
            if (e.jump_type === "原生") {
              e.jump_type = "NATIVE";
            }
          });
          this.jump_pages = jump_pages;
          this.getTargetPages(true);
        } else {
          this.$message.error(
            `code: ${res.data?.code}; message: ${res.data?.message}`
          );
        }
      });
    },
    getTargetPages(init = false) {
      // debugger;
      if (!init) {
        this.record.jump_id = null;
      }
      let jumpType = this.record.jump_type;
      let target_pages = [];
      this.jump_pages.forEach((e) => {
        if (e.jump_type === jumpType) {
          target_pages.push({
            id: e.id,
            remark: e.remark,
            jump_data: e.jump_data,
          });
        }
      });
      this.target_pages = target_pages;
    },
    handleJumpPages() {
      this.jump_show = true;
    },
    handleJumpCreate() {
      this.jump_action = this.DIALOG_CREATION;
      this.jump_data = {};
      this.jump_action_show = true;
    },
    handleJumpEdit(row) {
      this.jump_action = this.DIALOG_EDIT;
      this.jump_data = _.clone(row);
      this.jump_action_show = true;
    },
    handleDeleteJump(row) {
      this.$confirm(`确认删除跳转 ${row.id} ${row.remark}?`).then(() => {
        this.$axios
          .delete(`/api/operation/page/new-app-entrances/jump-list/${row.id}`)
          .then((res) => {
            this.fetchJumpPages();
            if (res.data.code === 0) {
              this.dialog_show = false;
              this.jump_show = false;
              this.jump_action_show = false;
              this.jump_action = "";
              this.$message.success("删除成功!");
              this.loading = false;
            } else {
              this.$message.error(
                `code: ${res.data?.code}; message: ${res.data?.message}`
              );
            }
          })
          .catch((err) => {
            this.$message.error(`失败! (${err})`);
          });
      });
    },
    addItem() {
      this.record.trigger_pages.push({
        trigger_page: null,
        param_type: null,
        page_op: null,
        trigger_page_params: [],
      });
    },
    deleteItem(item, index) {
      this.record.trigger_pages.splice(index, 1);
    },
    jump_submit() {
      this.$refs["jump_data"].validate((valid) => {
        if (!valid) {
          this.$alert("校验失败请修改", "校验失败请修改", {
            confirmButtonText: "确定",
          });
        } else {
          let method = "put";
          if (this.jump_action === this.DIALOG_CREATION) {
            method = "post";
          }
          this.$axios[method](
            "/api/operation/page/new-app-entrances/jump-list",
            this.jump_data
          )
            .then((res) => {
              this.fetchJumpPages();
              if (res.data.code === 0) {
                this.res_success_notice(res);
                this.dialog_show = false;
                this.jump_show = false;
                this.jump_action_show = false;
                this.jump_action = "";
              } else {
                this.res_fail_notice(res);
              }
            })
            .catch((_) => {
              this.res_error_notice(res);
            });
        }
      });
    },
    notice(title, message) {
      this.$alert(message, title, {
        confirmButtonText: "确定",
      });
    },
    res_success_notice(r) {
      let title = "提交成功";
      this.notice(title, title);
    },
    res_fail_notice(res) {
      let title = "提交失败";
      let message = `(code: ${res.data?.code}; message: ${res.data?.message})`;
      this.notice(title, message);
    },
    res_error_notice(error) {
      let title = "提交失败";
      let message = `(code: ${error.response.status}; message: ${error.message})`;
      this.notice(title, message);
    },
    changeSelectableTriggerPages() {
      this.record.jump_type = null;
      this.record.trigger_pages = [];
      this.selectable_trigger_pages = {...this.trigger_pages};
      this.selectable_jump_types = {...this.jump_types};
      this.updateSelectableTriggerPages();
      this.getTargetPages();
    },
    updateSelectableTriggerPages() {
      if (this.record.platform === "WEB") {
        delete this.selectable_jump_types["NATIVE"];
      } else if (this.record.platform === "ALL") {
        delete this.selectable_jump_types["NATIVE"];
      }
    },
    initTriggerPageParams(item) {
      // debugger
      item.trigger_page_params = null;
      item.page_type = null;
      item.page_op = null;
    },
    initTriggerPageMarketParams(item) {
      // debugger
      item.trigger_page_params = null;
      // item.param_type = "MARKET";
      item.page_op = "IN";
      item.trigger_page_params = [];
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.dialog_push_test = false;
          this.dialog_template = false;
          done();
        })
        .catch((_) => {
        });
    },
    download_template() {
      let url = "/api/operation/email-push/template";
      this.$download_from_url(url, "email-push-template.xlsx");
    },
    before_upload(file) {
      let name = file.name;
      if (
        !name.endsWith(".xlsx") &&
        !name.endsWith(".xls") &&
        !name.endsWith("csv")
      ) {
        this.$message.error("只能上传excel表格");
        return false;
      }
    },
    upload_success(res, filters_form) {
      if (res?.code === 0) {
        // debugger;
        filters_form.user_whitelist = res.data.items.join();
        this.$message.success(`共导入 ${res.data.total} 条记录`);
      } else {
        this.$message.error(
          `上传失败! (code: ${res?.code}; message: ${res?.message})`
        );
      }
    },
    handle_template() {
      this.dialog_template = true;
      this.template_filters.title = "";
      this.template_query();
    },
    handle_template_change() {
      this.template_filters.title = null;
      this.template_query();
    },
    template_query() {
      this.template_loading = true;
      this.$axios
        .get("/api/operation/templates", {params: this.template_filters})
        .then((res) => {
          this.template_loading = false;
          if (res && res.data.code === 0) {
            let data = res.data.data;
            this.template_items = data.items;
            this.template_total = data.total;
          } else {
            this.items = [];
            this.$message.error(
              `code: ${res.data?.code}; message: ${res.data?.message}`
            );
          }
        });
    },
    handleCurrentChange(row, column, cell, event) {
      if (column.label === "操作") {
        return;
      }
      this.$confirm(`确认选择?`).then(() => {
        let id = row.id;
        if (!id) {
          return;
        }
        let contents = this.contents;
        let lang_list = Object.keys(this.languages);
        lang_list.forEach((lang) => {
          this.$axios
            .get(`/api/operation/template-content/${id}/langs/${lang}`)
            .then((res) => {
              if (res?.data?.code === 0) {
                let data = res.data.data;
                contents[lang] = {
                  content: data.content,
                };
              }
            });
        });
        this.dialog_template = false;
      });
    },
    upload_error(err) {
      this.$message.error(`上传失败! (${err})`);
    },
    build_activeNames(status, has_content) {
      if (status === "pending" || !has_content) {
        return ["0", "1", "2"];
      } else {
        return ["0", "3"];
      }
    },
    handleCheckAllTagChange(val) {
      this.record.tag_pages = val ? Object.keys(this.tag_pages) : [];
      this.isIndeterminateTag = false;
    },
    handleCheckedTagChange(value) {
      let checkedCount = value.length;
      this.checkAllTag = checkedCount === Object.keys(this.tag_pages).length;
      this.isIndeterminateTag = checkedCount > 0 && checkedCount < this.record.tag_pages.length;
    },
    handleCheckAllTipBarChange(val) {
      this.record.tip_pages = val ? Object.keys(this.tip_pages) : [];
      this.isIndeterminateTipBar = false;
    },
    handleCheckedTipBarChange(value) {
      let checkedCount = value.length;
      this.checkAllTipBar = checkedCount === Object.keys(this.tip_pages).length;
      this.isIndeterminateTipBar = checkedCount > 0 && checkedCount < this.record.tip_pages.length;
    },
  },
  provide() {
    return {testSendFunc: null};
  },
  mounted() {
    this.get_data();
  },
  data() {
    return {
      messageConfig: {
        extr_params: {},
        has_title: false,
        use_editor: true,
        save_url: "/api/operation/asset-maintain/${id}/langs/${lang}",
        cur_lang: null,
        has_test: false,
      },
      isOldHistory: false,
      dialog_template: false,
      mode: null,
      timerange: [],
      record: {
        asset: null,
        started_at: null,
        tag_pages: [],
        tip_pages: [],
      },
      contents: {},
      checkAllTag: false,
      checkAllTipBar: false,
      tag_pages: {},
      tip_pages: {},
      isIndeterminateTag: true,
      isIndeterminateTipBar: true,
      langs: {},
      maintain_types: {},
      platforms: {},
      trigger_pages: {},
      filter_types: {},
      jump_pages: [],
      target_pages: {},
      markets: [],
      margin_markets: [],
      assets: [],
      margin_assets: [],
      perpetual_markets: [],
      perpetual_assets: [],
      jump_types: {},
      param_types: {},
      page_ops: {IN: "in"},
      selectable_jump_types: {},
      uploading: false,
      upload_tip: null,
      series_data: {},
      audit_data: [],
      template_items: [],
      template_filters: {
        business: "TIP_BAR",
        enabled: true,
        title: null,
        page: null,
        limit: 10,
      },
      template_total: 0,
      template_loading: false,

      tagGroups: [],
      dialogUserGroupMap: {
        dialogUserGroup: false,
        dialogUserGroupTotal: 0,
        dialogUserGroupItems: [],
        group_types: {},
        dialogUserGroupLoading: false,
        dialogUserGroupFilters: {
          name: null,
          page: null,
          limit: 10,
        },
      },

      // statistics_data: [],
      dayDisplay: false,
      chart_options: {
        series: [
          {
            name: null,
            data: null,
          },
        ],
      },
      jump_action: null,
      jump_show: false,
      jump_action_show: false,
      jump_data: {},
      DIALOG_CREATION: "CREATION",
      DIALOG_EDIT: "EDIT",
      contents_visible: false,
      languages: {},
      cur_lang: null,

      activeNames: ["0", "1", "2"],
      disabled: false,
      submit_loading: false,
    };
  },
  watch: {
    timerange(val) {
      this.record.started_at = val && val[0] ? val[0].getTime() / 1000 : null;
      this.record.ended_at = val && val[1] ? val[1].getTime() / 1000 : null;
    },
  },
};
</script>
