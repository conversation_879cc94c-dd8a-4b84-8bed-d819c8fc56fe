<template>
  <div class="table-data">
    <el-row type="flex" justify="space-between" align="middle">
      <el-col :span="22">
        <h2 style="font-family: 'Microsoft YaHei', Arial, sans-serif">
          弹窗设置
        </h2>
      </el-col>

      <el-col :span="1">
        <el-tooltip
          content="刷新"
          placement="right"
          :open-delay="500"
          :hide-after="2000"
        >
          <el-button
            icon="el-icon-refresh-left"
            circle
            @click="get_data"
          ></el-button>
        </el-tooltip>
      </el-col>
    </el-row>
    <el-collapse v-model="activeNames">
      <el-collapse-item name="0">
        <template slot="title"
          ><h3 style="font-family: 'Microsoft YaHei', Arial, sans-serif">
            基本信息
          </h3></template
        >
        <el-form
          :model="record"
          label-position="left"
          label-width="120px"
          :disabled="disabled"
        >
          <el-form-item label="弹窗名称" required>
            <el-input v-model="record.name" style="width: 300px"></el-input>
          </el-form-item>

          <el-form-item label="弹窗终端" required>
            <el-radio-group
              v-model="record.platform"
              @change="changeSelectableTriggerPages"
            >
              <el-radio
                v-for="(val, key) in platforms"
                :key="key"
                :label="key"
                >{{ val.toUpperCase() }}</el-radio
              >
            </el-radio-group>
          </el-form-item>

          <el-form-item label="触发页面: " required>
            <el-tooltip
              content="添加"
              placement="right"
              :open-delay="500"
              :hide-after="2000"
            >
              <el-button icon="el-icon-circle-plus" circle @click="addItem">
              </el-button>
            </el-tooltip>
            <div v-for="(item, index) in record.trigger_pages" :key="index">
              <el-select
                v-model="item.trigger_page"
                style="width: 300px"
                @change="initTriggerPageParams(item)"
              >
                <el-option
                  v-for="(label, value) in trigger_pages"
                  :key="value"
                  :label="label"
                  :value="value"
                >
                </el-option>
              </el-select>

              <span v-if="item.trigger_page === 'ASSET_DATA'">
                <el-select v-model="item.page_op" style="width: 100px">
                  <el-option
                    v-for="(label, value) in page_ops"
                    :key="value"
                    :label="label"
                    :value="value"
                  >
                  </el-option>
                </el-select>
                <el-select
                  v-model="item.trigger_page_params"
                  style="width: 150px"
                  multiple
                  clearable
                  filterable
                  placeholder="默认全部"
                >
                  <el-option
                    v-for="asset in assets"
                    :key="asset"
                    :label="asset"
                    :value="asset"
                  >
                  </el-option>
                </el-select>
              </span>

              <span
                v-if="
                  ['MARGIN_MARKET', 'PERPETUAL_MARKET', 'SPOT_MARKET'].includes(
                    item.trigger_page
                  )
                "
              >
                <el-select
                  v-model="item.param_type"
                  style="width: 100px"
                  @change="initTriggerPageParams(item)"
                >
                  <el-option
                    v-for="(label, value) in param_types"
                    :key="value"
                    :label="label"
                    :value="value"
                  >
                  </el-option>
                </el-select>
                <el-select v-model="item.page_op" style="width: 100px">
                  <el-option
                    v-for="(label, value) in page_ops"
                    :key="value"
                    :label="label"
                    :value="value"
                  >
                  </el-option>
                </el-select>
                <el-select
                  v-model="item.trigger_page_params"
                  style="width: 150px"
                  multiple
                  clearable
                  filterable
                  placeholder="默认全部"
                >
                  <span v-if="item.trigger_page === 'MARGIN_MARKET'">
                    <el-option
                      v-for="key in item.param_type === 'ASSET'
                        ? margin_assets
                        : margin_markets"
                      :key="key"
                      :label="key"
                      :value="key"
                    >
                    </el-option>
                  </span>
                  <span v-if="item.trigger_page === 'PERPETUAL_MARKET'">
                    <el-option
                      v-for="key in item.param_type === 'ASSET'
                        ? perpetual_assets
                        : perpetual_markets"
                      :key="key"
                      :label="key"
                      :value="key"
                    >
                    </el-option>
                  </span>
                  <span v-if="item.trigger_page === 'SPOT_MARKET'">
                    <el-option
                      v-for="key in item.param_type === 'ASSET'
                        ? assets
                        : markets"
                      :key="key"
                      :label="key"
                      :value="key"
                    >
                    </el-option>
                  </span>
                </el-select>
              </span>

              <el-button @click="deleteItem(item, index)" type="danger"
                >删除</el-button
              >
            </div>
          </el-form-item>

          <el-form-item label="显示位置" required>
            <el-radio-group v-model="record.pop_position">
              <el-radio
                v-for="(val, key) in getPopPosition(pop_position)"
                :key="key"
                :label="key"
                >{{ val }}</el-radio
              >
            </el-radio-group>
          </el-form-item>

          <el-form-item label="弹窗频率" required>
            <el-radio-group v-model="record.frequency">
              <el-radio
                v-for="(val, key) in frequencies"
                :key="key"
                :label="key"
                >{{ val }}</el-radio
              >
            </el-radio-group>
          </el-form-item>

          <el-form-item label="弹窗时间" required>
            <el-date-picker
              v-model="timerange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
            >
            </el-date-picker>
            <el-row>
            <span>UTC：{{ $formatUTCDate(timerange[0]) }} 至 {{ $formatUTCDate(timerange[1]) }}</span>
            </el-row>
          </el-form-item>

          <el-form-item label="是否跳转: " required>
            <el-radio-group v-model="record.jump_page_enabled">
              <el-radio :label="false">否</el-radio>
              <el-radio :label="true">是</el-radio>
            </el-radio-group>
          </el-form-item>

          <span v-if="record.jump_page_enabled">
            <el-form-item label="跳转类型: " required>
              <el-select
                v-model="record.jump_type"
                style="width: 300px"
                @change="getTargetPages()"
              >
                <el-option
                  v-for="(label, value) in selectable_jump_types"
                  :key="value"
                  :label="label"
                  :value="value"
                >
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="公共链接: " required>
              <el-select
                v-model="record.jump_id"
                style="width: 300px"
                filterable
              >
                <el-option
                  v-for="target_page in target_pages"
                  :key="target_page.id"
                  :label="`${target_page.remark} -- ${target_page.jump_data}`"
                  :value="target_page.id"
                >
                </el-option>
              </el-select>
              <el-button
                icon="el-icon-s-primary"
                type="primary"
                @click="handleJumpPages"
                >跳转管理</el-button
              >
            </el-form-item>
          </span>

          <el-form-item label="弹窗样式" required>
            <el-radio-group v-model="record.content_style">
              <el-radio
                v-for="(val, key) in getContentStyle(content_style)"
                :key="key"
                :label="key"
                >{{ val }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </el-collapse-item>

      <el-collapse-item name="1">
        <template slot="title"
          ><h3 style="font-family: 'Microsoft YaHei', Arial, sans-serif">
            目标客群
          </h3></template
        >
        <el-form :disabled="disabled">
          <el-form-item label="客群类型" required>
            <el-radio-group v-model="record.filter_type">
              <el-radio
                v-for="(val, key) in filter_types"
                :key="key"
                :label="key"
                >{{ val }}</el-radio
              >
            </el-radio-group>
          </el-form-item>

          <template v-if="record.filter_type === 'filters'">
            <span v-if="isOldHistory">
              <OldUserGroupPicker
                :filters="record.filters"
                :disabled="this.disabled"
                ref="user_picker"
              ></OldUserGroupPicker>
            </span>
            <span v-else>
              <UserGroupPicker
                :tagGroups="tagGroups"
                :dialogUserGroupMap="dialogUserGroupMap"
              ></UserGroupPicker>
            </span>
            <el-form :model="record" :inline="true">
              <el-form-item>
                <el-checkbox v-model="record.whitelist_enabled"
                  >是否剔除免打扰客群</el-checkbox
                >
              </el-form-item>
              <template v-if="record.whitelist_enabled">
                <el-form-item>
                  <el-input
                    v-model="record.user_whitelist"
                    :disabled="true"
                  ></el-input>
                </el-form-item>
                <el-form-item>
                  <el-tooltip
                    content="上传"
                    placement="right"
                    :open-delay="500"
                    :hide-after="2000"
                  >
                    <el-upload
                      ref="upload"
                      action="/api/operation/email-push/imports"
                      name="file"
                      :data="{ value_type: 'user' }"
                      :headers="{ AUTHORIZATION: $cookies.get('admin_token') }"
                      :before-upload="before_upload"
                      :on-success="(res) => upload_success(res, record)"
                      :on-error="upload_error"
                      :show-file-list="false"
                    >
                      <el-button
                        type="primary"
                        size="mini"
                        icon="el-icon-upload"
                        circle
                      ></el-button>
                    </el-upload>
                  </el-tooltip>
                </el-form-item>
                <el-form-item>
                  <el-tooltip
                    content="下载模板"
                    placement="right"
                    :open-delay="500"
                    :hide-after="2000"
                  >
                    <el-button
                      type="primary"
                      icon="el-icon-download"
                      circle
                      size="mini"
                      style="margin-left: 10px"
                      @click="download_template"
                    ></el-button>
                  </el-tooltip>
                </el-form-item>
              </template>
            </el-form>
          </template>
          <el-form-item>
            <el-button type="primary" @click="save" :loading="submit_loading"
              >保存配置</el-button
            >
          </el-form-item>
        </el-form>
      </el-collapse-item>

      <template v-if="$route.query.id !== '0' && contents_visible">
        <el-collapse-item name="2">
          <template slot="title">
            <h3 style="font-family: 'Microsoft YaHei', Arial, sans-serif">
              通知内容
            </h3>
            &#12288;
          </template>
          <el-row
            v-if="this.record.content_style !== 'image'"
            type="flex"
            justify="space-between"
            align="middle"
          >
            <el-col :span="22"></el-col>
            <el-col :span="2">
              <el-tooltip
                content="上传模板"
                placement="right"
                :open-delay="500"
                :hide-after="2000"
              >
                <el-upload
                  action
                  ref="upload"
                  name="batch-upload"
                  :auto-upload="false"
                  :show-file-list="false"
                  :on-change="handle_update_template"
                  accept=".xlsx, .xls, .csv"
                >
                  <el-button
                    type="primary"
                    @click="$refs.upload.submit()"
                    :disabled="disabled"
                    >上传模板</el-button
                  >
                </el-upload>
              </el-tooltip>
            </el-col>
            <el-col :span="2">
              <el-tooltip
                content="选择模板"
                placement="right"
                :open-delay="500"
                :hide-after="2000"
              >
                <el-button
                  type="primary"
                  @click="handle_template"
                  :disabled="disabled"
                  >选择模板</el-button
                >
              </el-tooltip>
            </el-col>
          </el-row>
          <el-tabs v-model="cur_lang" type="card">
            <template v-for="(name, lang) in languages">
              <el-tab-pane :label="name" :name="lang" :key="lang">
                <div v-if="record.content_style !== 'image'">
                  <el-form :model="record" label-width="50px">
                    <el-form-item label="标题">
                      <el-input v-model="contents[lang].title"></el-input>
                    </el-form-item>

                    <el-form-item v-if="isCornetText()" label="摘要">
                      <el-input
                        type="textarea"
                        autosize
                        v-model="contents[lang].summary"
                      ></el-input>
                    </el-form-item>
                  </el-form>
                  <editor
                    v-model="contents[lang].content"
                    :init="init"
                    v-if="!isCornetText()"
                  ></editor>
                </div>
                <div v-else>
                  <span>弹窗图片</span>
                  <el-upload
                    class="img-uploader"
                    accept=".jpg, .jpeg, .png, .webp"
                    action="/api/upload/image"
                    name="img"
                    :limit="1"
                    list-type="picture-card"
                    :data="{ img_size: '840,960', auto_zip: 'WEBP' }"
                    :file-list="contents[cur_lang].images"
                    :headers="{ AUTHORIZATION: $cookies.get('admin_token') }"
                    :on-success="
                      (res, file, fileList) =>
                        uploadOneImage(res, cur_lang, file, fileList)
                    "
                    :on-remove="handleRemoveImage"
                    :disabled="disabled"
                  >
                    <i class="el-icon-plus"></i>
                    <div slot="tip" class="el-upload__tip">
                      只能上传jpg/jpeg/png/webp文件, 图片尺寸要求：宽 840 * 高 960
                    </div>
                  </el-upload>
                </div>
                <br />
                <div>
                  <el-form :model="record" label-width="100px">
                    <el-form-item label="跳转链接">
                      <el-input v-model="contents[lang].url"></el-input>
                    </el-form-item>
                  </el-form>
                </div>
                <el-divider></el-divider>
                <div>
                  <el-button
                    type="primary"
                    @click="save_contents(lang)"
                    :disabled="disabled || is_copy"
                    v-preventReClick
                  >
                    保存当前语言
                  </el-button>

                  <el-button
                    type="primary"
                    @click="save_contents('')"
                    :disabled="disabled || is_copy"
                    v-preventReClick
                  >
                    保存所有语言
                  </el-button>
                </div>
              </el-tab-pane>
            </template>
          </el-tabs>
        </el-collapse-item>

        <template v-if="record.status !== 'pending' && !is_copy">
          <el-collapse-item name="3">
            <template slot="title">
              <h3 style="font-family: 'Microsoft YaHei', Arial, sans-serif">
                弹窗统计
              </h3>
              &#12288;
            </template>
            <el-row type="flex" justify="space-between" align="middle">
              <el-col :span="12">
                <template>
                  <el-table :data="statistics_data" border>
                    <el-table-column prop="platform" label="终端"></el-table-column>
                    <el-table-column prop="target_user_number" label="客群数"></el-table-column>
                    <el-table-column prop="page_view" label="浏览量"></el-table-column>
                    <el-table-column prop="click_count" label="点击量"></el-table-column>
                    <el-table-column prop="click_rate" label="点击率"></el-table-column>
                  </el-table>
                </template>
              </el-col>
              <el-col :span="12" style="position: relative">
                <el-select v-model="series_platform" clearable filterable placeholder="ALL"
                           @change="change_series_platform" size="mini"
                           style="position: absolute; top: 0; z-index: 1; right: 160px; width: 110px">
                  <el-option v-for="plat in this.order" :key="plat" :label="plat" :value="plat">
                  </el-option>
                </el-select>

                <el-button-group
                  style="position: absolute; top: 0; z-index: 1; right: 40px"
                >
                  <el-button
                    :type="!dayDisplay ? 'success' : 'default'"
                    size="mini"
                    @click="toggleDisplay(false)"
                  >
                    24H
                  </el-button>
                  <el-button
                    :type="dayDisplay ? 'success' : 'default'"
                    size="mini"
                    @click="toggleDisplay(true)"
                  >
                    7天
                  </el-button>
                </el-button-group>
                <highcharts
                  class="hc"
                  :reflow="true"
                  :options="chart_options"
                ></highcharts>
              </el-col>
            </el-row>
          </el-collapse-item>
        </template>
      </template>
    </el-collapse>

    <el-dialog title="跳转管理" :visible.sync="jump_show" width="70%">
      <el-tooltip
        content="新建"
        placement="right"
        :open-delay="500"
        :hide-after="2000"
      >
        <el-button
          type="primary"
          icon="el-icon-plus"
          circle
          @click="handleJumpCreate"
        ></el-button>
      </el-tooltip>
      <el-table :data="jump_pages" stripe>
        <el-table-column prop="remark" label="跳转标注"> </el-table-column>

        <el-table-column prop="jump_data" label="跳转链接"> </el-table-column>

        <el-table-column
          prop="jump_type"
          :formatter="(row) => jump_types[row.jump_type]"
          label="跳转类型"
        >
        </el-table-column>

        <el-table-column prop="operation" label="操作">
          <template slot-scope="scope">
            <el-tooltip
              content="编辑"
              placement="right"
              :open-delay="500"
              :hide-after="2000"
            >
              <el-button
                size="mini"
                type="primary"
                icon="el-icon-edit"
                circle
                @click="handleJumpEdit(scope.row)"
              ></el-button>
            </el-tooltip>
            <el-tooltip
              content="删除"
              placement="right"
              :open-delay="500"
              :hide-after="2000"
            >
              <el-button
                size="mini"
                type="danger"
                icon="el-icon-delete"
                circle
                @click="handleDeleteJump(scope.row)"
              ></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <el-dialog
      :title="'选择模板'"
      :visible.sync="dialog_template"
      :before-close="handleClose"
      width="70%"
    >
      <template>
        <div class="table-data">
          <el-form :inline="true">
            <el-form-item>
              <el-input
                v-model="template_filters.title"
                clearable
                placeholder="关键词"
                style="width: 120px"
              >
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handle_template_search">
                查询
              </el-button>
            </el-form-item>
          </el-form>

          <el-table
            :data="template_items"
            v-loading="template_loading"
            highlight-current-row
            @cell-click="handleCurrentChange"
            stripe
          >
            <el-table-column type="index" width="50"> </el-table-column>
            <el-table-column
              label="ID"
              prop="id"
              show-overflow-tooltip
              align="left"
            >
            </el-table-column>

            <el-table-column
              label="模板名称"
              prop="title"
              show-overflow-tooltip
            >
            </el-table-column>

            <el-table-column label="备注" prop="remark" show-overflow-tooltip>
            </el-table-column>

            <el-table-column label="操作" prop="name" show-overflow-tooltip>
              <template slot-scope="scope">
                <span>
                  <el-link
                    :href="
                      '/operation/push/popup-window-template-details?id=' +
                      scope.row.id
                    "
                    target="_blank"
                    :underline="false"
                  >
                    <el-tooltip
                      content="查看详情"
                      placement="left"
                      :open-delay="500"
                      :hide-after="2000"
                    >
                      <el-button
                        size="mini"
                        type="primary"
                        icon="el-icon-more"
                        circle
                      ></el-button>
                    </el-tooltip>
                  </el-link>
                </span>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            :current-page.sync="template_filters.page"
            :page-size.sync="template_filters.limit"
            :page-sizes="[10, 50, 100, 200, 500]"
            :total="template_total"
            @size-change="handle_template_change"
            @current-change="template_query"
            :hide-on-single-page="true"
            layout="total, sizes, prev, pager, next, jumper"
          >
          </el-pagination>
        </div>
      </template>
    </el-dialog>

    <el-dialog
      :title="jump_action === DIALOG_CREATION ? '添加跳转' : '编辑跳转'"
      :visible.sync="jump_action_show"
      :before-close="handleClose"
      width="60%"
    >
      <el-form
        :model="jump_data"
        ref="jump_data"
        label-width="80px"
        :validate-on-rule-change="false"
      >
        <el-form-item label="跳转标示">
          <el-input v-model="jump_data.remark"></el-input>
        </el-form-item>

        <el-form-item label="跳转类型" required>
          <el-select clearable v-model="jump_data.jump_type">
            <el-option
              v-for="(value, key) in jump_types"
              :key="value"
              :label="value"
              :value="key"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="跳转链接" required>
          <el-input v-model="jump_data.jump_data"></el-input>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="jump_submit">确 定</el-button>
      </span>
    </el-dialog>
    <UserTagGroupDialog
      :tagGroups="tagGroups"
      :dialogUserGroupMap="dialogUserGroupMap"
    ></UserTagGroupDialog>
  </div>
</template>

<script>
import Vue from "vue";
import tinymce from "tinymce/tinymce";
import "tinymce/themes/modern/theme";
import Editor from "@tinymce/tinymce-vue";
import "tinymce/plugins/image";
import "tinymce/plugins/link";
import "tinymce/plugins/code";
import "tinymce/plugins/table";
import "tinymce/plugins/lists";
import "tinymce/plugins/contextmenu";
import "tinymce/plugins/wordcount";
import "tinymce/plugins/colorpicker";
import "tinymce/plugins/textcolor";
import VueClipboard from "vue-clipboard2";
import _ from "lodash";
import { Chart } from "highcharts-vue";
import UserTagGroupDialog from "@/components/UserTagGroupDialog";
import UserGroupPicker from "@/components/UserGroupPicker";
import OldUserGroupPicker from "@/components/OldUserGroupPicker";
import { readFile } from "@/plugins/tools";
import XLSX from "xlsx";

Vue.use(VueClipboard);

export default {
  components: {
    Editor,
    highcharts: Chart,
    UserTagGroupDialog,
    UserGroupPicker,
    OldUserGroupPicker,
  },
  methods: {
    get_data(_id = 0) {
      let id = _id || this.$route.query.id || 0;
      try {
        this.$axios.get(`/api/operation/popup-windows/${id}`).then((res) => {
          if (res?.data?.code === 0) {
            this.mode = id ? "edit" : "new";
            let data = res.data.data;
            this.assign_form(data);
            this.updateSelectableTriggerPages();
            this.fetchJumpPages();
            if (this.mode === "edit") {
              this.update_chart_options();
            }
          } else {
            this.$message.error(
              `请求失败! (code: ${res.data?.code}; message: ${res.data?.message})`
            );
          }
        });
      } catch (e) {
        this.$message.error(`请求失败! (${e})`);
      }
    },

    buildContent(content) {
      if (content) {
        content.images.forEach((item, index, array) => {
          array[index] = { uid: index, url: item };
        });
      }
      return content;
    },

    assign_form(data) {
      this.tagGroups = data.extra.tag_groups || [];
      this.langs = data.extra.langs;
      this.platforms = data.extra.platforms;
      this.content_style = data.extra.content_style;
      this.pop_position = data.extra.pop_position;
      this.frequencies = data.extra.frequencies;
      this.trigger_pages = data.extra.trigger_pages;
      this.filter_types = data.extra.filter_types;
      this.languages = data.extra.langs;
      this.jump_types = data.extra.jump_types;
      this.param_types = data.extra.param_types;
      // this.page_ops = data.extra.page_ops;
      this.markets = data.extra.markets;
      this.margin_markets = data.extra.margin_markets;
      this.assets = data.extra.assets;
      this.margin_assets = data.extra.margin_assets;
      this.perpetual_markets = data.extra.perpetual_markets;
      this.perpetual_assets = data.extra.perpetual_assets;
      this.statistics_map = data.statistic

      this.selectable_jump_types = { ...this.jump_types };
      if (this.mode === "edit") {
        this.record = data.record;
        this.handleHistoryOnEdit(this.record);
        this.contents = data.contents;
        this.contents_visible = true;
        this.timerange = [
          new Date(this.record.started_at * 1000),
          new Date(this.record.ended_at * 1000),
        ];
        this.push_table_data(data);
        let status = data.record.status;
        let has_content = data.record.has_content;
        this.activeNames = this.build_activeNames(status, has_content);
        // 如果以上架且有内容，就冻结所有选项
        // this.disabled = ((status === 'online' && has_content) || status === "offline");
        // this.disabled = status === "offline";
        this.is_copy = this.$route.query.is_copy === '1' ? true : false
        this.disabled = !this.is_copy && status === "offline";
        if (this.is_copy) {
          // 一些值的初始化
          this.tagGroups = []
          this.record.filter_type = 'filters'
          this.timerange = []
          this.record.started_at = null
          this.record.ended_at = null
        }
      } else {
        this.record = {
          platform: "ALL",
          trigger_pages: [
            {
              trigger_page: "HOME",
              param_type: null,
              page_op: null,
              trigger_page_params: [],
            },
          ],
          frequency: "once",
          filter_type: "filters",
          jump_id: null,
          pop_position: "center",
          content_style: "text",
          jump_page_enabled: false,
          user_whitelist: "",
        };
        this.contents = {};
      }

      let lang_list = Object.keys(this.languages);
      if (!lang_list.includes(this.cur_lang)) {
        this.cur_lang = lang_list[0];
      }
      this.contents = Object.fromEntries(
        lang_list.map((lang) => [
          lang,
          this.buildContent(this.contents[lang]) || {
            title: "",
            content: "",
            url: "",
            images: [],
          },
        ])
      );
    },

    handleHistoryOnEdit(row) {
      let timePoint = new Date(2022, 11, 8).getTime();
      this.isOldHistory = row.created_at * 1000 <= timePoint;
      if (!this.isOldHistory) {
        return;
      }
      this.disabled = true;
    },
    save() {
      let canPostReq = this.beforeSaveRule();
      if (!canPostReq) {
        return;
      }
      let groups = [];
      this.tagGroups.forEach((group) => {
        groups.push(group.id);
      });
      this.record.groups = groups;
      let promise = null;
      if (this.mode === "edit") {
        if (this.is_copy === true) {
          promise = this.$axios.post("/api/operation/popup-windows", this.record);
        }
        else {
          promise = this.$axios.patch(
            `/api/operation/popup-windows/${this.record.id}`,
            this.record
          );
        }
      } else if (this.mode === "new") {
        promise = this.$axios.post("/api/operation/popup-windows", this.record);
      } else {
        return;
      }
      this.submit_loading = true;
      try {
        promise.then((res) => {
          if (res?.data?.code === 0) {
            let id = res.data.data.id;
            if (this.mode === "new" || this.is_copy) {
              let query_data = {
                id: id,
                is_copy: 0,
              }
              this.save_contents('', false, id)
              this.$router.replace({ query: query_data });
              this.mode = "edit";
              this.is_copy = false
              this.record.id = id;
            }
            this.$message.success("保存成功");
            if (this.mode === "new") {
              this.$router.replace({ query: { id: id } });
              this.mode = "edit";
              this.record.id = id;
            }
            this.get_data(id);
            this.activeNames = ["0", "1", "2"];
          } else {
            this.$message.error(`请求失败! ${res.data?.message})`);
          }
          this.submit_loading = false;
        });
      } catch (e) {
        this.$message.error(`请求失败! (${e})`);
        this.submit_loading = false;
      }
    },
    beforeSaveRule() {
      let form = this.record;
      if (!form.name) {
        this.$message.error("请输入名称!");
        return false;
      }
      if (!form.platform) {
        this.$message.error("请选择弹窗终端!");
        return false;
      }
      if (form.trigger_pages.length <= 0) {
        this.$message.error("请输入触发页面!");
        return false;
      }
      let checkSuccess = true;
      form.trigger_pages.forEach((e) => {
        if (!e.trigger_page) {
          checkSuccess = false;
        }
        if (
          ["MARGIN_MARKET", "PERPETUAL_MARKET", "SPOT_MARKET"].includes(
            e.trigger_page
          )
        ) {
          if (!e.param_type) {
            checkSuccess = false;
          }
          if (!e.page_op) {
            checkSuccess = false;
          }

        }
        if (["ASSET_DATA"].includes(e.trigger_page)) {
          if (!e.page_op) {
            checkSuccess = false;
          }
        }
      });
      if (!checkSuccess) {
        this.$message.error("请检查触发页面配置是否完善!");
        return false;
      }

      if (!form.frequency) {
        this.$message.error("请选择弹窗频率!");
        return false;
      }
      if (!form.pop_position) {
        this.$message.error("请选择弹窗位置!");
        return false;
      }
      if (!form.content_style) {
        this.$message.error("请选择弹窗样式!");
        return false;
      }
      if (!form.started_at && !form.ended_at) {
        this.$message.error("请选择弹窗时间!");
        return false;
      }
      if (!form.jump_page_enabled) {
        form.jump_page_enabled = false;
        delete form.jump_id;
        delete form.jump_type;
      } else {
        if (!form.jump_type) {
          this.$message.error("请输入跳转类型!");
          return false;
        }
        if (!form.jump_id) {
          this.$message.error("请输入跳转目标页!");
          return false;
        }
      }
      if (!form.filter_type) {
        this.$message.error("请选择客群类型!");
        return false;
      }
      if (form.filter_type !== "none") {
        if (!this.tagGroups || this.tagGroups.length === 0) {
          this.$message.error("请选择客群!");
          return;
        }
      }
      return true;
    },
    save_contents(lang = "", silent = false, row_id = 0) {
      let contents = this.contents;
      let id = row_id !== 0 ? row_id : this.$route.query.id
      let content_style = this.record.content_style;
      let pop_position = this.record.pop_position;
      let languages = lang ? [lang] : Object.keys(this.languages);
      let putLanguages = [];

      if (!lang) {
        languages.forEach((lang) => {
          let content = contents[lang];
          if (content_style === "image") {
            if (content.images.length !== 0) {
              putLanguages.push(lang);
            }
          } else {
            if (content_style === "text" && pop_position === "corner") {
              if (content.title) {
                putLanguages.push(lang);
              }
            } else {
              if (content.title && content.content) {
                putLanguages.push(lang);
              }
            }
          }
        });

        if (putLanguages.length === 0) {
          this.$message.error(
            "触达内容不能为空，请至少在一个语言区输入标题与内容/图片!"
          );
          return;
        }
      } else {
        putLanguages = languages;
      }

      putLanguages.forEach((lang) => {
        let content = contents[lang];
        this.$axios
          .put(`/api/operation/popup-windows/${id}/langs/${lang}`, {
            title: content.title,
            content: content.content,
            url: content.url || "",
            images: content.images.map((obj) => {
              return obj.url;
            }),
            summary: content.summary,
            content_style,
            pop_position,
          })
          .then((res) => {
            if (res?.data?.code === 0) {
              if (!silent) {
                this.$message.success(`${lang} 保存成功!`);
              }
            } else {
              this.$message.error(
                `保存失败! (code: ${res.data?.code}; message: ${res.data?.message})`
              );
            }
        })
          .catch((err) => {
            this.$message.error(`保存失败! (${err})`);
          });
      });
    },

    fetchJumpPages() {
      let url = "/api/operation/page/new-app-entrances/jump-list";
      let jump_pages = this.jump_pages;
      this.$axios.get(url).then((res) => {
        if (res?.data?.code === 0) {
          jump_pages = res.data.data;
          jump_pages.forEach((e) => {
            if (e.jump_type === "原生") {
              e.jump_type = "NATIVE";
            }
          });
          this.jump_pages = jump_pages;
          this.getTargetPages(true);
        } else {
          this.$message.error(
            `code: ${res.data?.code}; message: ${res.data?.message}`
          );
        }
      });
    },
    getTargetPages(init = false) {
      // debugger;
      if (!init) {
        this.record.jump_id = null;
      }
      let jumpType = this.record.jump_type;
      let target_pages = [];
      this.jump_pages.forEach((e) => {
        if (e.jump_type === jumpType) {
          target_pages.push({
            id: e.id,
            remark: e.remark,
            jump_data: e.jump_data,
          });
        }
      });
      this.target_pages = target_pages;
    },
    handleJumpPages() {
      this.jump_show = true;
    },
    handleJumpCreate() {
      this.jump_action = this.DIALOG_CREATION;
      this.jump_data = {};
      this.jump_action_show = true;
    },
    handleJumpEdit(row) {
      this.jump_action = this.DIALOG_EDIT;
      this.jump_data = _.clone(row);
      this.jump_action_show = true;
    },
    handleDeleteJump(row) {
      this.$confirm(`确认删除跳转 ${row.id} ${row.remark}?`).then(() => {
        this.$axios
          .delete(`/api/operation/page/new-app-entrances/jump-list/${row.id}`)
          .then((res) => {
            this.fetchJumpPages();
            if (res.data.code === 0) {
              this.dialog_show = false;
              this.jump_show = false;
              this.jump_action_show = false;
              this.jump_action = "";
              this.$message.success("删除成功!");
              this.loading = false;
            } else {
              this.$message.error(
                `code: ${res.data?.code}; message: ${res.data?.message}`
              );
            }
          })
          .catch((err) => {
            this.$message.error(`失败! (${err})`);
          });
      });
    },
    addItem() {
      this.record.trigger_pages.push({
        trigger_page: null,
        param_type: null,
        page_op: null,
        trigger_page_params: [],
      });
    },
    deleteItem(item, index) {
      this.record.trigger_pages.splice(index, 1);
    },
    jump_submit() {
      this.$refs["jump_data"].validate((valid) => {
        if (!valid) {
          this.$alert("校验失败请修改", "校验失败请修改", {
            confirmButtonText: "确定",
          });
        } else {
          let method = "put";
          if (this.jump_action === this.DIALOG_CREATION) {
            method = "post";
          }
          this.$axios[method](
            "/api/operation/page/new-app-entrances/jump-list",
            this.jump_data
          )
            .then((res) => {
              this.fetchJumpPages();
              if (res.data.code === 0) {
                this.res_success_notice(res);
                this.dialog_show = false;
                this.jump_show = false;
                this.jump_action_show = false;
                this.jump_action = "";
              } else {
                this.res_fail_notice(res);
              }
            })
            .catch((_) => {
              this.res_error_notice(res);
            });
        }
      });
    },
    notice(title, message) {
      this.$alert(message, title, {
        confirmButtonText: "确定",
      });
    },
    res_success_notice(r) {
      let title = "提交成功";
      this.notice(title, title);
    },
    res_fail_notice(res) {
      let title = "提交失败";
      let message = `(code: ${res.data?.code}; message: ${res.data?.message})`;
      this.notice(title, message);
    },
    res_error_notice(error) {
      let title = "提交失败";
      let message = `(code: ${error.response.status}; message: ${error.message})`;
      this.notice(title, message);
    },
    getPopPosition() {
      if (this.record.content_style !== "text") {
        this.record.pop_position = "center";
        return { center: "正中间" };
      }

      if (this.record.platform === "WEB") {
        this.pop_position.corner = "右下角";
      } else {
        for (let x of this.record.trigger_pages) {
          if (x.trigger_page === "ALL") {
            this.record.pop_position = "center";
            return { center: "正中间" };
          }
        }
        if (this.record.platform === "APP") {
          this.pop_position.corner = "顶部";
        } else {
          this.pop_position.corner = "右上角/顶部";
        }
      }
      return this.pop_position;
    },
    getContentStyle() {
      if (this.record.pop_position === "corner") {
        this.record.content_style = "text";
        return { text: "纯文字" };
      } else {
        return this.content_style;
      }
    },
    isCornetText() {
      return (
        this.record.pop_position === "corner" &&
        this.record.content_style === "text"
      );
    },
    changeSelectableTriggerPages() {
      this.record.jump_type = null;
      this.record.trigger_pages = [];
      this.selectable_trigger_pages = { ...this.trigger_pages };
      this.selectable_jump_types = { ...this.jump_types };
      this.updateSelectableTriggerPages();
      this.getTargetPages();
    },
    updateSelectableTriggerPages() {
      if (this.record.platform === "WEB") {
        delete this.selectable_jump_types["NATIVE"];
      } else if (this.record.platform === "ALL") {
        delete this.selectable_jump_types["NATIVE"];
      }
    },
    initTriggerPageParams(item) {
      // debugger
      item.trigger_page_params = [];
      item.page_type = null;
      item.page_op = null;
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.dialog_push_test = false;
          this.dialog_template = false;
          done();
        })
        .catch((_) => {});
    },
    download_template() {
      let url = "/api/operation/email-push/template";
      this.$download_from_url(url, "email-push-template.xlsx");
    },
    before_upload(file) {
      let name = file.name;
      if (
        !name.endsWith(".xlsx") &&
        !name.endsWith(".xls") &&
        !name.endsWith("csv")
      ) {
        this.$message.error("只能上传excel表格");
        return false;
      }
    },
    upload_success(res, filters_form) {
      if (res?.code === 0) {
        // debugger;
        filters_form.user_whitelist = res.data.items.join();
        this.$message.success(`共导入 ${res.data.total} 条记录`);
      } else {
        this.$message.error(
          `上传失败! (code: ${res?.code}; message: ${res?.message})`
        );
      }
    },
    uploadOneImage(res, cur_lang, file, fileList) {
      if (res?.code === 0) {
        let images = this.contents[cur_lang].images;
        let url = res.data.file_url;
        images.push({ url: url });
        this.record.imageUrl = url;
      } else {
        _.pull(fileList, file);
        this.$message.error(
          `上传失败! (code: ${res?.code}; message: ${res?.message})`
        );
      }
    },
    handleRemoveImage(file) {
      let images = this.contents[this.cur_lang].images;
      // fileList = _.pull(fileList, {'url': file.url})
      images = _.pull(images, file);
    },
    handle_template_search() {
      this.template_filters.page = 1;
      this.template_query();
    },
    handle_template() {
      this.dialog_template = true;
      this.template_filters.title = "";
      this.template_query();
    },
    handle_template_change() {
      this.template_filters.title = null;
      this.template_query();
    },
    template_query() {
      this.template_loading = true;
      this.$axios
        .get("/api/operation/templates", { params: this.template_filters })
        .then((res) => {
          this.template_loading = false;
          if (res && res.data.code === 0) {
            let data = res.data.data;
            this.template_items = data.items;
            this.template_total = data.total;
          } else {
            this.items = [];
            this.$message.error(
              `code: ${res.data?.code}; message: ${res.data?.message}`
            );
          }
        });
    },
    handleCurrentChange(row, column, cell, event) {
      if (column.label === "操作") {
        return;
      }
      this.$confirm(`确认选择?`).then(() => {
        let id = row.id;
        if (!id) {
          return;
        }
        let contents = this.contents;
        let lang_list = Object.keys(this.languages);
        lang_list.forEach((lang) => {
          this.$axios
            .get(`/api/operation/template-content/${id}/langs/${lang}`)
            .then((res) => {
              if (res?.data?.code === 0) {
                let data = res.data.data;
                contents[lang] = {
                  title: data.title,
                  content: data.content,
                  url: data.url || "",
                  images: [],
                  summary: data.summary || "",
                };
              }
            });
        });
        this.dialog_template = false;
      });
    },
    upload_error(err) {
      this.$message.error(`上传失败! (${err})`);
    },
    push_table_data(data) {
      let tmp = Object.values(data.statistic)
      tmp.forEach((item) => {item.target_user_number = data.record.target_user_number})
      tmp.sort((a, b) => this.order.indexOf(a.platform) - this.order.indexOf(b.platform));
      this.statistics_data = tmp
    },
    change_series_platform(plat) {
      this.series_platform = plat
      this.update_chart_options();
    },
    toggleDisplay(dayDisplay = false) {
      this.dayDisplay = dayDisplay;
      this.update_chart_options();
    },
    update_chart_options() {
      let plat = this.series_platform
      let plat_data = this.statistics_map[plat]
      if (!plat_data.series_data) {
        return;
      }
      let seriesOption = {
        pointStart: null,
        pointInterval: 24 * 3600 * 1000,
      };
      let xDateFormat = "%Y-%m-%d";
      let timezoneOffset = 0;
      let titleName = `·${plat} 发送 7天 内打开情况`;
      let seriesData = plat_data.series_data
      let data_list = seriesData._7d_series || [];
      if (!this.dayDisplay) {
        titleName = `·${plat} 发送 24H 内打开情况`;
        data_list = seriesData._24h_series || [];
        seriesOption.pointInterval = 3600 * 1000;
        xDateFormat = "%Y-%m-%d %H:00";
        timezoneOffset = -8 * 60;
      }
      let _this = this;
      let categories = [];
      let click_count_data = [];
      data_list.forEach((e) => {
        if (data_list.indexOf(e) === 0) {
          // seriesOption.pointStart = moment(Number(e.key) * 1000);
          seriesOption.pointStart = e.key * 1000;
        }
        categories.push(e.key * 1000);
        click_count_data.push([e.key * 1000, e.value.click_count]);
      });

      let series = [
        {
          name: "点击量",
          yAxis: 0,
          data: click_count_data,
          page_view: plat_data.page_view,
        },
      ];

      this.chart_options = {
        title: {
          text: titleName,
          align: "left",
          style: {
            color: "#3E576F",
            fontSize: "16px",
          },
        },
        time: {
          timezoneOffset: timezoneOffset, // UTC -> Asia/Shanghai +8
        },
        xAxis: {
          type: "datetime",
          labels: {
            formatter: function () {
              let value = this.value;
              if (categories.indexOf(value) !== -1) {
                return categories.indexOf(value) + 1;
              } else {

              }
            },
          },
          tickInterval: 1,
          crosshair: {
            // 设置准星线样式
            snap: false,
          },
        },
        yAxis: [
          {
            // Primary yAxis
            labels: {
              format: "{value}",
            },
            title: {
              text: "点击量",
            },
            opposite: true,
          },
        ],
        legend: {
          layout: "vertical",
          align: "right",
          x: -50,
          verticalAlign: "top",
          y: 25,
          floating: true,
        },
        tooltip: {
          shared: true,
          xDateFormat: xDateFormat,
          valueDecimals: 2,
          pointFormatter: function () {
            let page_view = this.series.userOptions.page_view;
            let _part = page_view !== 0 ? this.y / page_view : 0;
            return `<b>${this.series.name}</b> <br> 点击量:<b>${
              this.y
            }</b><br>点击率: <b>${_this.$formatPercent(_part, 4)}</b>`;
          },
        },
        plotOptions: {
          series: seriesOption,
        },
        series: series,
      };
    },

    build_activeNames(status, has_content) {
      if (status === "pending" || !has_content) {
        return ["0", "1", "2"];
      } else {
        return ["0", "3"];
      }
    },
    async fmt_read_excel_data(json_data) {
      let character = {
        lang: "lang",
        content: "内容",
        title: "标题",
        summary: "摘要",
      };
      let arr = [];
      json_data.forEach((item) => {
        let obj = {};
        for (let key in character) {
          if (!character.hasOwnProperty(key)) break;
          let v = item[character[key]];
          obj[key] = v;
        }
        arr.push(obj);
      });
      return arr;
    },
    async write_data_to_from(data_arr) {
      data_arr.forEach((item) => {
        let lang = item.lang.toUpperCase();
        this.contents[lang].title = item.title;
        this.contents[lang].content = item.content;
        this.contents[lang].summary = item.summary;
      });
      this.$message.success("上传成功");
    },
    async handle_update_template(file) {
      let dataBinary = await readFile(file.raw);
      let workBook = XLSX.read(dataBinary, { type: "binary", cellDates: true });
      let workSheet = workBook.Sheets[workBook.SheetNames[0]];
      let json_data = XLSX.utils.sheet_to_json(workSheet);
      //   console.log(json_data); //这里已经能拿到转换后的json
      let data_arr = await this.fmt_read_excel_data(json_data);
      await this.write_data_to_from(data_arr);
    },
    handleImgUpload (blobInfo, success, failure) {
      let formData = new FormData();
      formData.set('auto_zip', 'WEBP')
      formData.set('img', blobInfo.blob())
      this.$axios.post('/api/upload/image', formData).then(res => {
        if (res.data.code === 0) {
          success(res.data.data.file_url)
        } else {
          failure(`code: ${res.data?.code}; message: ${res.data?.message}`);
        }
      }).catch(res => {
        failure('error');
      })

    },
  },
  mounted() {
    tinymce.init({});
    this.get_data();
  },
  data() {
    return {
            isOldHistory: false,
      content_style: [],
      pop_position: [],
      dialog_template: false,
      mode: null,
      timerange: [],
      record: {},
      contents: {},
      langs: {},
      platforms: {},
      trigger_pages: {},
      frequencies: {},
      filter_types: {},
      jump_pages: [],
      target_pages: {},
      markets: [],
      margin_markets: [],
      assets: [],
      margin_assets: [],
      perpetual_markets: [],
      perpetual_assets: [],
      jump_types: {},
      param_types: {},
      page_ops: { IN: "in" },
      selectable_jump_types: {},
      uploading: false,
      upload_tip: null,
      series_data: {},
      template_items: [],
      template_filters: {
        business: "POPUP_WINDOW",
        enabled: true,
        title: null,
        page: null,
        limit: 10,
      },
      template_total: 0,
      template_loading: false,

      tagGroups: [],
      dialogUserGroupMap: {
        dialogUserGroup: false,
        dialogUserGroupTotal: 0,
        dialogUserGroupItems: [],
        group_types: {},
        dialogUserGroupLoading: false,
        dialogUserGroupFilters: {
          name: null,
          page: null,
          limit: 10,
        },
      },
      order: ["ALL", "WEB", "APP", "ANDROID", "IOS"],
      series_platform: "ALL",
      statistics_data: [],
      statistics_map: {},
      dayDisplay: false,
      chart_options: {
        series: [
          {
            name: null,
            data: null,
          },
        ],
      },
      jump_action: null,
      jump_show: false,
      jump_action_show: false,
      jump_data: {},
      DIALOG_CREATION: "CREATION",
      DIALOG_EDIT: "EDIT",
      contents_visible: false,
      languages: {},
      cur_lang: null,

      activeNames: ["0", "1", "2"],
      disabled: false,
      is_copy: false,
      submit_loading: false,

      init: {
        skin_url: "/tinymce/lightgray",
        height: 300,
        content_style: "body { line-height: 2; } p { margin: 0px 0; }",
        plugins:
          "link lists image code table colorpicker textcolor wordcount contextmenu",
        toolbar:
          "bold italic underline strikethrough | fontsizeselect | forecolor backcolor | alignleft aligncenter alignright alignjustify | bullist numlist | outdent indent blockquote | undo redo | link unlink image code | removeformat",
        branding: false,
        images_upload_handler: (blobInfo, success, failure) => {
          this.handleImgUpload(blobInfo, success, failure);
        },
      },
    };
  },
  watch: {
    timerange(val) {
      this.record.started_at = val && val[0] ? val[0].getTime() / 1000 : null;
      this.record.ended_at = val && val[1] ? val[1].getTime() / 1000 : null;
    },
  },
};
</script>
