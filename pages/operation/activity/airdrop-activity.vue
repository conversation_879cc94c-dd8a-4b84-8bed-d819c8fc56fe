<template>
  <el-container>
    <el-main>
      <h2 style="font-family: 'Microsoft YaHei',<PERSON><PERSON>,sans-serif;">
        空投活动>活动管理
      </h2>

      <el-form :inline="true" :model="filters">
        <el-form-item label="空投类型">
          <el-select clearable filterable v-model="filters.airdrop_mode" @change="get_data" placeholder="<ALL>">
            <el-option v-for="(name, key) in airdrop_mode_dict" :key="key" :label="name" :value="key"> </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="空投标签">
          <el-select clearable filterable v-model="filters.label_type" @change="get_data" placeholder="<ALL>">
            <el-option v-for="(name, key) in labels_dict" :key="key" :label="name" :value="key"> </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="空投币种" style="margin-left: 20px;">
          <el-select clearable filterable v-model="filters.asset" @change="get_data" placeholder="<ALL>"
            style="width: 120px">
            <el-option v-for="asset in asset_list" :key="asset" :label="asset" :value="asset"> </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="进行状态" style="margin-left: 20px;">
          <el-select clearable filterable v-model="filters.active_status" @change="get_data" placeholder="<ALL>"
            style="width: 120px">
            <el-option v-for="(active_status_name, key) in active_status_dict" :key="key" :label="active_status_name"
              :value="key">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="显示状态" style="margin-left: 20px;">
          <el-select clearable filterable v-model="filters.status" @change="get_data" placeholder="<ALL>"
            style="width: 120px">
            <el-option v-for="(status_name, key) in status_dict" :key="key" :label="status_name" :value="key">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item style="margin-left: 40px;">
          <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
            <el-button icon="el-icon-refresh-left" circle @click="handle_page_refresh"></el-button>
          </el-tooltip>
        </el-form-item>

        <el-form-item style="margin-left: 40px;">
          <el-link href="/operation/activity/airdrop-activity-detail?id=0" target="_blank" :underline="false">
            <el-tooltip content="添加" placement="right" :open-delay="500" :hide-after="2000">
              <el-button type="primary" icon="el-icon-plus" circle></el-button>

            </el-tooltip>
          </el-link>
        </el-form-item>
      </el-form>

      <el-table :data="items" style="width: 100%">
        <el-table-column prop="id" label="ID">
          <template slot-scope="scope">
            <el-tooltip :content="'activity_id: ' + scope.row.activity_id" placement="top">
              <span>{{ scope.row.id }}</span>
            </el-tooltip>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="活动名称"> </el-table-column>

        <el-table-column prop="label_type" label="标签" :formatter="row => labels_dict[row.label_type]">
        </el-table-column>


        <el-table-column prop="airdrop_mode" label="空投类型" :formatter="row => airdrop_mode_dict[row.airdrop_mode]">
        </el-table-column>

        <el-table-column prop="joinuser_count" label="报名人数">
          <template slot-scope="scope">
            <el-link class="no-underline" :underline="false" type="primary" target="_blank"
              :href="scope.row.joinuser_detail_link">
              {{ scope.row.joinuser_count }}
            </el-link>
          </template>
        </el-table-column>

        <el-table-column prop="total_count" label="空投人数"> </el-table-column>
        <el-table-column prop="rewards" label="每人可领取"> </el-table-column>
        <el-table-column prop="lock_day" label="冻结天数"> </el-table-column>

        <el-table-column prop="start_time" label="开始时间" :formatter="row => $formatDate(row.start_time)">
        </el-table-column>

        <el-table-column prop="end_time" label="结束时间" :formatter="row => $formatDate(row.end_time)"> </el-table-column>

        <el-table-column prop="active_status" label="进行状态" :formatter="row => active_status_dict[row.active_status]">
        </el-table-column>

        <el-table-column prop="status" label="开奖状态" :formatter="row => format_lottery_status(row)"> </el-table-column>

        <el-table-column prop="status" label="显示状态" :formatter="row => format_status(row)"> </el-table-column>

        <el-table-column prop="audit_remark" label="备注" show-overflow-tooltip></el-table-column>

        <el-table-column label="操作" width="180">
          <template slot-scope="scope">
            <span style="margin-left: 10px;">
              <el-link :href="'/operation/activity/airdrop-activity-detail?id=' + scope.row.id" target="_blank"
                :underline="false">
                <el-tooltip content="编辑">
                  <el-button size="mini" type="primary" icon="el-icon-edit" circle></el-button>
                </el-tooltip>
              </el-link>
            </span>

            <el-button size="small" @click="handleOnOffline(scope.row, true)" type="success"
              v-if="scope.row.status == 'OFFLINE' && format_datetime(scope.row.start_time) > Date.now()">上架</el-button>


            <el-button size="mini" @click="handleOnOffline(scope.row, false)" type="danger"
              v-if="scope.row.status == 'ONLINE'">下架</el-button>

            <el-button size="mini" @click="handleCreateAudit(scope.row)" type="warning"
              v-if="['DRAFT', 'REJECTED'].includes(scope.row.status)">提交审核</el-button>

            <el-button size="mini" @click="handleAuditClick(scope.row)" type="success"
              v-if="scope.row.status === 'PENDING'">审核</el-button>

          </template>
        </el-table-column>
      </el-table>

      <el-pagination :current-page.sync="filters.page" :page-size.sync="filters.limit"
        @size-change="handle_limit_change" @current-change="handle_page_change" :page-sizes="[50, 25]"
        :hide-on-single-page="true" layout="total, sizes, prev, pager, next, jumper" :total="total">
      </el-pagination>

      <el-backtop></el-backtop>

      <!-- 审核处理对话框 -->
      <el-dialog title="审核处理" :visible.sync="auditDialog.visible" width="40%" :close-on-click-modal="false"
        :close-on-press-escape="false">
        <el-form :model="auditDialog.form" ref="auditForm" label-width="100px">
          <el-form-item label="审核结果:" required>
            <el-radio-group v-model="auditDialog.form.status">
              <el-radio label="OFFLINE">通过</el-radio>
              <el-radio label="REJECTED">拒绝</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="审核备注">
            <el-input v-model="auditDialog.form.remark" type="textarea" :rows="4" placeholder="备注" maxlength="100"
              show-word-limit>
            </el-input>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="auditDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmitAudit">确定</el-button>
        </span>
      </el-dialog>

      <!-- WebAuthn组件 -->
      <UserWebAuthn ref="UserWebAuthn" :operation_type="operation_type"></UserWebAuthn>

    </el-main>
  </el-container>
</template>

<script>
import moment from 'moment';
const DIALOG_CREATION = 'creation';
const DIALOG_EDIT = 'edit';
const base_url = '/api/activity/airdrop/activity';
import UserSearch from "../../../components/user/UserSearch";
import UserWebAuthn from "@/components/UserWebAuthn";
export default {
  components: { UserSearch, UserWebAuthn },
  methods: {
    notice(title, message) {
      this.$alert(message, title, {
        confirmButtonText: '确定',
      });
    },
    reset_page() {
      this.filters.page = 1;
    },
    res_success_notice(res) {
      let title = '提交成功';
      this.notice(title, title);
    },
    res_fail_notice(res) {
      let title = '提交失败';
      let message = `(code: ${res.data?.code}; message: ${res.data?.message}; data: ${res.data?.data})`;
      this.notice(title, message);
    },
    res_error_notice(error) {
      let title = '提交失败';
      let message = `(code: ${error.response.status}; message: ${error.message}; data: ${res.data?.data})`;
      this.notice(title, message);
    },
    handle_limit_change() {
      this.reset_page();
      this.get_data();
    },
    handle_page_change() {
      this.get_data();
    },
    handle_page_refresh() {
      this.reset_page();
      this.get_data();
    },
    get_data() {
      this.loading = true;
      this.$axios.get(base_url, { params: this.filters }).then(res => {
        this.loading = false;
        if (res?.data?.code === 0) {
          let data = res.data.data;
          this.items = data.items;
          this.total = data.total;
          let extra = data.extra;
          this.languages = extra.languages;
          this.airdrop_mode_dict = extra.airdrop_mode_dict;
          this.labels_dict = extra.labels;
          this.status_dict = extra.status_dict;
          this.active_status_dict = extra.active_status_dict;
          this.asset_list = extra.assets;
          this.add_joinuser_detail_link();
        } else {
          this.items = [];
          this.total = 0;
          this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
        }
      });
    },
    add_joinuser_detail_link() {
      this.items.forEach(e => {
        if (e.airdrop_mode == 'REALTIME') {
          e.joinuser_detail_link = '/operation/activity/airdrop-activity-realtime-history?id=' + e.id;
        } else {
          e.joinuser_detail_link = '/operation/activity/airdrop-activity-random-history?id=' + e.id;
        }
        e.history_output_link = '/operation/activity/mining-history-output?activity_id=' + e.id;
      });
    },
    handleOnOffline(row, is_online) {
      let new_status = is_online ? 'ONLINE' : 'OFFLINE';
      this.$confirm(is_online ? '确认上架？' : '确认下架？')
        .then(_ => {
          this.$axios
            .patch(base_url, { id: row.id, status: new_status })
            .then(res => {
              if (res?.data?.code === 0) {
                this.res_success_notice(res);
                this.get_data();
              } else {
                this.res_fail_notice(res);
              }
            })
            .catch(e => {
              this.res_error_notice(e);
            });
        })
        .catch(_ => { });
    },
    format_status(row) {
      if (this.format_datetime(row.start_time) >= Date.now() && row.status === 'OFFLINE') {
        return '未上架'
      }
      if (row.status === 'OFFLINE') {
        return '已下架'
      }
      if (row.status === 'DRAFT' || row.status === 'PENDING' || row.status === 'REJECTED') {
        return '/'
      }
      return this.status_dict[row.status]
    },
    format_lottery_status(row) {
      if (row.lottery_status === true && row.airdrop_mode === 'RANDOM') {
        return '已开奖'
      }
      if (row.lottery_status === false && row.airdrop_mode === 'RANDOM') {
        return '未开奖'
      }
      return '/'
    },
    format_datetime(datetime) {
      return Number(datetime * 1000)
    },
    handleCreateAudit(row) {
      this.$confirm(`确认提交审核 ${row.id} ${row.name}？`)
        .then(_ => {
          this.$axios
            .patch(base_url, { id: row.id, status: 'PENDING' })
            .then(res => {
              if (res?.data?.code === 0) {
                this.res_success_notice(res);
                this.get_data();
              } else {
                this.res_fail_notice(res);
              }
            })
            .catch(e => {
              this.res_error_notice(e);
            });
        })
        .catch(_ => {
        });
    },

    // 修改审核按钮点击事件
    handleAuditClick(row) {
      this.currentAuditRow = row;
      this.auditDialog.visible = true;
    },

    // 提交审核
    handleSubmitAudit() {
      if (!this.auditDialog.form.status) {
        this.$message.error('请选择审核结果');
        return;
      }

      // 关闭审核表单
      this.auditDialog.visible = false;

      // 执行WebAuthn验证和审核
      this.handleWebAuthn(() => {
        this.handleAudited(this.currentAuditRow);
      });
    },

    async handleWebAuthn(success_callback) {
      const UserWebAuthn = this.$refs["UserWebAuthn"];
      await UserWebAuthn.run();
      await UserWebAuthn.handleWebAuthn().then(() => {
        if (UserWebAuthn.success) {
          this.headers["WebAuthn-Token"] = UserWebAuthn.webauthn_token;
          if (success_callback) {
            success_callback();
          }
          return true;
        } else {
          this.$message.error("WebAuthn校验失败!");
          return false;
        }
      }).catch(err => {
        this.$message.error(`WebAuthn校验失败! ${err}`);
        return false;
      });
    },

    handleAudited(row) {
      let url = base_url + `/${row.id}/audit`;
      let auditData = {
        status: this.auditDialog.form.status,
        audit_remark: this.auditDialog.form.remark
      };

      this.$axios
        .post(url, auditData, { headers: this.headers })
        .then((res) => {
          if (res.data.code === 0) {
            this.get_data();
            this.$message.success("审核成功！");
            // 重置当前审核行
            this.currentAuditRow = null;
          } else {
            this.$message.error(
              `code: ${res.data.code}; message: ${res.data.message}`
            );
          }
        })
        .catch((err) => {
          this.$message.error(`审核失败! (${err})`);
        });
    },
  },
  created() {
    this.DIALOG_CREATION = DIALOG_CREATION;
    this.DIALOG_EDIT = DIALOG_EDIT;

    let query_types = {
      airdrop_mode: String,
      asset: String,
    };
    let url_query = this.$route.query;
    Object.keys(url_query).forEach(key => {
      if (key in this.filters) {
        let value = url_query[key];
        if (key === 'page' || key === 'limit') {
          value = Number(value);
        }
        if (key in query_types) {
          value = query_types[key](value);
        }
        this.filters[key] = value;
      }
    });
    this.$watch('filters', {
      handler: function () {
        let query = {};
        Object.assign(query, this.filters);
        Object.keys(query).forEach(key => !query[key] && delete query[key]);
        this.$router.replace({ query: query });
      },
      deep: true,
    });
  },
  mounted() {
    this.filters.id = this.$route.query.id;
    this.get_data();
  },
  computed: {
    headers() {
      return {
        'AUTHORIZATION': this.$cookies.get('admin_token'),
        'WebAuthn-Token': this.$refs["UserWebAuthn"] !== undefined ? this.$refs["UserWebAuthn"].webauthn_token : "",
        'Operate-Type': this.operation_type,
      }
    },
  },
  data() {
    return {
      filters: {
        id: null,
        airdrop_mode: null,
        asset: null,
        page: 1,
        limit: 50,
      },
      items: [],
      total: 0,
      airdrop_mode_dict: {},
      labels_dict: {},
      status_dict: {},
      active_status_dict: {},
      asset_list: [],
      languages: [],
      loading: true,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now();
        },
      },
      // 添加新的数据属性
      currentAuditRow: null,
      operation_type: "AIRDROP_AUDIT",
      confirmDialog: {
        visible: false
      },
      auditDialog: {
        visible: false,
        form: {
          status: 'PENDING',
          remark: ''
        }
      },
    };
  },
};
</script>

<style>
.img-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.img-uploader .el-upload:hover {
  border-color: #409eff;
}

.img-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.hide .el-upload--picture-card {
  display: none;
}
</style>
