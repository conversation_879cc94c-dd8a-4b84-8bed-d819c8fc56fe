<template>
  <el-container>
    <el-main>
      <h2 style="font-family: 'Microsoft YaHei',<PERSON>l,sans-serif;">
        活动日历管理
      </h2>

      <el-form :inline="true" :model="filters">

        <el-form-item label="进行状态" style="margin-left: 20px;">
          <el-select clearable filterable v-model="filters.active_status" @change="get_data" placeholder="<ALL>" style="width: 120px">
            <el-option v-for="(active_status_name, key) in active_status_dict" :key="key" :label="active_status_name" :value="key">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="显示状态" style="margin-left: 20px;">
          <el-select clearable filterable v-model="filters.status" @change="get_data" placeholder="<ALL>" style="width: 120px">
            <el-option v-for="(status_name, key) in status_dict" :key="key" :label="status_name" :value="key"> </el-option>
          </el-select>
        </el-form-item>

        <el-form-item style="margin-left: 40px;">
          <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
            <el-button icon="el-icon-refresh-left" circle @click="handle_page_refresh"></el-button>
          </el-tooltip>
        </el-form-item>

        <el-form-item style="margin-left: 40px;">
          <el-tooltip content="添加" placement="right" :open-delay="500" :hide-after="2000">
            <el-button type="primary" icon="el-icon-plus" circle @click="handle_creation"></el-button>
          </el-tooltip>
        </el-form-item>
      </el-form>

      <el-table :data="items" style="width: 100%">
        <el-table-column prop="id" label="ID"> </el-table-column>

        <el-table-column prop="name" label="名称"> </el-table-column>

        <el-table-column prop="end_time" label="上架时间" :formatter="row => $formatDate(row.online_time)"> </el-table-column>
        <el-table-column prop="start_time" label="开始时间" :formatter="row => $formatDate(row.start_time)"> </el-table-column>
        <el-table-column prop="end_time" label="结束时间" :formatter="row => $formatDate(row.end_time)"> </el-table-column>
        <el-table-column prop="end_time" label="下架时间" :formatter="row => $formatDate(row.offline_time)"> </el-table-column>
        <el-table-column prop="active_status" label="状态" :formatter="row => active_status_dict[row.active_status]"> </el-table-column>
        <el-table-column label="更新用户"
                       prop="updated_by"
                       show-overflow-tooltip>
          <template slot-scope="scope">
            <el-link :href="'/users/user-details?id=' + scope.row.updated_by"
                    type="primary"
                    target="_blank"
                    :underline="false"
                    style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
              {{ scope.row.updated_user_email }}
            </el-link>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="180">
          <template slot-scope="scope">
            <el-button
              size="small"
              @click="handleOnOffline(scope.row, true)"
              type="success"
              v-if="scope.row.status === 'OFFLINE'"
            >上架</el-button
            >

            <el-button size="mini" @click="handleOnOffline(scope.row, false)" type="danger"
            >删除</el-button
            >
            <span style="margin-left: 10px;">
              <el-tooltip content="编辑">
                <el-button
                  size="mini"
                  @click="handle_edit(scope.row)"
                  type="primary"
                  icon="el-icon-edit"
                  circle
                ></el-button>
              </el-tooltip>
            </span>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        :current-page.sync="filters.page"
        :page-size.sync="filters.limit"
        @size-change="handle_limit_change"
        @current-change="handle_page_change"
        :page-sizes="[50, 25]"
        :hide-on-single-page="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>

      <el-dialog
        :title="dialog_type == DIALOG_CREATION ? '添加项目' : '编辑项目'"
        :visible.sync="dialog_visible"
        destroy-on-close
        width="90%"
        :before-close="handle_close"
      >
        <el-form :model="form_data" ref="form_data" label-width="80px">

          <el-form-item label="活动名称" prop="name" required>
            <el-col :span="4">
              <el-input style="width:180px" v-model="form_data.name" placeholder="活动名称">
              </el-input>
            </el-col>

              <el-col :span="2">
              <el-upload
                ref="upload"
                :action="`/api/activity/calendar/template-upload`"
                name="batch-upload"
                :show-file-list="false"
                :headers="{ AUTHORIZATION: $cookies.get('admin_token') }"
                :on-success="importSuccess"
                :on-error="importError"
                accept=".xlsx"
              >
                <el-button type="primary">上传</el-button>
              </el-upload>
            </el-col>
            <el-col :span="2">
              <el-button type="primary"
                         @click="handleDownloadTemplate">下载模板</el-button>
            </el-col>
          </el-form-item>

          <el-form-item label="活动类型" prop="activity_type" required>
            <el-select filterable v-model="form_data.activity_type" placeholder="<ALL>">
              <el-option v-for="(title, key) in activity_type_dict" :key="key" :label="title" :value="key"> </el-option>
            </el-select>
          </el-form-item>

          <el-form-item prop="online_time" label="上架时间" required>
            <el-date-picker
              v-model="form_data.online_time"
              type="datetime"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="timestamp"
            >
            </el-date-picker>
            <el-row>
              <span>UTC时间：{{ $formatUTCDate(form_data.online_time) }}</span>
            </el-row>
          </el-form-item>

          <el-form-item prop="start_time" label="开始时间" required>
            <el-date-picker
              v-model="form_data.start_time"
              type="datetime"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="timestamp"
            >
            </el-date-picker>
            <el-row>
              <span>UTC时间：{{ $formatUTCDate(form_data.start_time) }}</span>
            </el-row>
          </el-form-item>

          <el-form-item prop="end_time" label="结束时间" required>
            <el-date-picker
              v-model="form_data.end_time"
              type="datetime"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="timestamp"
            >
            </el-date-picker>
            <el-row>
              <span>UTC时间：{{ $formatUTCDate(form_data.end_time) }}</span>
            </el-row>
          </el-form-item>

          <el-form-item prop="offline_time" label="下架时间" required>
            <el-date-picker
              v-model="form_data.offline_time"
              type="datetime"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="timestamp"
            >
            </el-date-picker>
            <el-row>
              <span>UTC时间：{{ $formatUTCDate(form_data.offline_time) }}</span>
            </el-row>
          </el-form-item>

          <el-form-item style="width: 300px" label="跳转链接" prop="name" required>
            <el-input style="width:360px" v-model="form_data.url" placeholder="跳转链接">
            </el-input>
          </el-form-item>

          <el-form-item>
            <el-button type="success" @click="save_help_announcement_url">
              填充帮助中心/公告中心多语言链接
            </el-button>
          </el-form-item>

          <el-form-item label="" required>
            <el-tabs type="card" v-model="default_detail_tab">
              <template v-for="(detail, index) in form_data.details">
                <el-tab-pane :label="detail.display" :name="detail.lang" :value="detail.lang" :key="index + '_detail'">

                  <el-form-item label="跳转链接" prop="name">
                    <el-input style="width:360px" v-model="detail.url" placeholder="跳转链接">
                    </el-input>
                  </el-form-item>

                  <el-form-item label="活动名称" prop="name">
                    <el-input style="width:360px" v-model="detail.title" placeholder="活动名称">
                    </el-input>
                  </el-form-item>

                </el-tab-pane>
              </template>
            </el-tabs>
          </el-form-item>
        </el-form>

        <span v-if="form_data.active_status !== 'FINISHED'" slot="footer" class="dialog-footer">
          <el-button @click="handle_close">取 消</el-button>
          <el-button type="primary" @click="handle_submit">确 定</el-button>
        </span>
      </el-dialog>

      <el-backtop></el-backtop>
    </el-main>
  </el-container>
</template>

<script>
  import moment from 'moment';
  const DIALOG_CREATION = 'creation';
  const DIALOG_EDIT = 'edit';
  const base_url = '/api/activity/calendar/activity';
  import UserSearch from "../../../components/user/UserSearch";

  export default {
    components: {
      UserSearch,
    },
    methods: {
      notice(title, message) {
        this.$alert(message, title, {
          confirmButtonText: '确定',
        });
      },
      res_success_notice(res) {
        let title = '提交成功';
        this.notice(title, title);
      },
      res_fail_notice(res) {
        let title = '提交失败';
        let message = `(code: ${res.data?.code}; message: ${res.data?.message}; data: ${res.data?.data})`;
        this.notice(title, message);
      },
      res_error_notice(error) {
        let title = '提交失败';
        let message = `(code: ${error.response.status}; message: ${error.message}; data: ${res.data?.data})`;
        this.notice(title, message);
      },
      reset_page() {
        this.filters.page = 1;
      },
      handle_limit_change() {
        this.reset_page();
        this.get_data();
      },
      handle_page_change() {
        this.get_data();
      },
      handle_page_refresh() {
        this.reset_page();
        this.get_data();
      },
      get_data() {
        this.loading = true;
        this.$axios.get(base_url, { params: this.filters }).then(res => {
          this.loading = false;
          if (res?.data?.code === 0) {
            let data = res.data.data;
            this.items = data.items;
            this.total = data.total;
            let extra = data.extra;
            this.languages = extra.languages;
            this.status_dict = extra.status_dict;
            this.activity_type_dict = extra.activity_type_dict;
            this.active_status_dict = extra.active_status_dict;
            this.asset_list = extra.assets;
            this.country_list = extra.country_list;
          } else {
            this.items = [];
            this.total = 0;
            this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
          }
        });
      },
      handle_creation() {
        this.form_data = {
          details: _.cloneDeep(this.languages),
        };
        this.dialog_type = this.DIALOG_CREATION;
        this.dialog_visible = true;
      },
      handle_edit(row) {
        this.form_data = _.clone(row);
        this.dialog_type = this.DIALOG_EDIT;
        this.dialog_visible = true;
        this.form_data.start_time = this.form_data.start_time * 1000;
        this.form_data.end_time = this.form_data.end_time * 1000;
        this.form_data.online_time = this.form_data.online_time * 1000;
        this.form_data.offline_time = this.form_data.offline_time * 1000;
      },
      handleOnOffline(row, is_online) {
        this.$confirm('确认删除？')
          .then(_ => {
            this.$axios.delete(base_url + '/' + row.id)
              .then(res => {
                if (res?.data?.code === 0) {
                  this.res_success_notice(res);
                  this.form_data = {};
                  this.get_data();
                } else {
                  this.res_fail_notice(res);
                }
              })
              .catch(e => {
                this.res_error_notice(e);
              });
          })
          .catch(_ => {});
      },
      handle_close() {
        this.$confirm('确认关闭？')
          .then(_ => {
            this.dialog_visible = false;
            this.form_data = {};
            done();
          })
          .catch(_ => {
          });
      },
      handle_submit() {
        this.$refs['form_data'].validate(valid => {
          if (!valid) {
            this.$alert('校验失败请修改', '校验失败请修改', {
              confirmButtonText: '确定',
            });
            return false;
          } else {
            if (this.form_data.name.length > 50) {
              this.$alert('活动名称长度不能超过50，请重新检查')
              return false;
            }
            let check_lang_title = true
            let check_lang_message = ''
            this.form_data.details.forEach(d => {
              if (d.title && d.title.length > 50) {
                check_lang_title = false
                check_lang_message = d.display + '活动名称长度不能超过50，请重新检查'
                return
              }
            });
            if (!check_lang_title) {
              this.$alert(check_lang_message)
              return
            }

            this.submit_validate_data();
          }
        });
      },
      handleDownloadTemplate() {
        let url = "/api/activity/calendar/template";
        this.$download_from_url(url, 'calendar-template.xlsx')
      },
      importError(err, file, fileList) {
        this.$alert("上传失败", "错误")
      },
      importSuccess(response, file, fileList) {
        if (response.code !== 0) {
          this.$alert(response.message, '错误'
          ).then(() => {
          }).catch(() => {
          });
          return;
        }
        this.$alert('已成功上传，请检查修后的翻译', '成功');

        let tans_data = {}

        response.data.forEach((e) => {
          tans_data[e.lang] = e.title
        })
        this.form_data.details.forEach((e) => {
          let _lang = e.lang
          let _title = tans_data[_lang]
          if (_title) {
            this.$set(e, 'title', _title)
          }

        });
      },
      process_form_data() {
        var new_form_data = _.cloneDeep(this.form_data);

        new_form_data.details.forEach(d => {
          if (d.display) {
            delete d.display;
            delete d.file_list;
            if (d.title) {
              d.url = d.url ? d.url : this.form_data.url
            }
          }
        });

        return new_form_data;
      },
      submit_validate_data() {
        let method = 'post';
        if (this.dialog_type !== DIALOG_CREATION) {
          method = 'put';
        }
        var new_form_data = this.process_form_data();
        this.$axios[method](base_url, new_form_data)
          .then(res => {
            if (res?.data?.code === 0) {
              this.form_data = {};
              this.dialog_visible = false;
              this.res_success_notice(res);
              this.get_data();
            } else {
              this.res_fail_notice(res);
            }
          })
          .catch(e => {
            console.log(e);
            this.res_error_notice(e);
          });
      },
      save_help_announcement_url(){
        let url = this.form_data.url;
        let is_help_url = url.indexOf("https://support.coinex.com/") == 0;
        let is_ann_url = url.indexOf("https://announcement.coinex.com/") == 0;
        if (!is_help_url && !is_ann_url){
          this.$message.error(`链接不是帮助中心/公告中心域名`);
          return
        }

        let help_lang_map = {
          // 后端lang: 帮助中心url lang path
          AR_AE: "ar", // 阿拉伯语
          EN_US: "en-us", // 英语
          ES_ES: "es",  // 西班牙语
          FA_IR: "fa",  // 波斯语
          RU_KZ: "ru",  // 俄语
          ZH_HANS_CN: "zh-cn",  // 简体中文
        };
        // https://support.coinex.com/hc/zh-cn/articles/360025067274
        // https://support.coinex.com/hc/articles/360025067274
        if(is_help_url){
          let pattern = ""
          if (url.indexOf("hc/articles") != -1){
            pattern = /hc\/articles/;
          }else{
            pattern = /hc\/(.*)\/articles/;
          }
          let lang_list = Object.keys(help_lang_map);
          this.form_data.details.forEach(row => {
            let url_lang = help_lang_map[row.lang];
            if (url_lang) {
              let new_url = url.replace(pattern, `hc/${url_lang}/articles`);
              row.url = new_url
            }
            //
            // this.save_content_url(lang, new_url);
          });
          return
        }
        //
        let ann_lang_map = {
          // 后端lang: 公告中心url lang path
          AR_AE: "ar", // 阿拉伯语
          DE_DE: "de",  // 德语
          EN_US: "en-us", // 英语
          ES_ES: "es",  // 西班牙语
          FA_IR: "fa",  // 波斯语
          FR_FR: "fr",  // 法语
          ID_ID: "id-id",  // 印尼语
          JA_JP: "ja",  // 日语
          KO_KP: "ko-kr",  // 韩语
          PT_PT: "pt",  // 葡萄牙语
          RU_KZ: "ru",  // 俄语
          TH_TH: "th",  // 泰语
          TR_TR: "tr",  // 土耳其语
          VI_VN: "vi-vn",  // 越南语
          ZH_HANS_CN: "zh-cn",  // 简体中文
          ZH_HANT_HK: "zh-tw", // 繁体中文
        };
        // https://announcement.coinex.com/hc/en-us/articles/8512448633108
        if(is_ann_url){
          let pattern = ""
          if (url.indexOf("hc/articles") != -1){
            pattern = /hc\/articles/;
          }else{
            pattern = /hc\/(.*)\/articles/;
          }
          let lang_list = Object.keys(ann_lang_map);
          lang_list.forEach(lang => {
            // todo fix
            let url_lang = ann_lang_map[lang];
            let new_url = url.replace(pattern, `hc/${url_lang}/articles`);
            this.save_content_url(lang, new_url);
          });
          return
        }

      },
    },
    created() {
      this.DIALOG_CREATION = DIALOG_CREATION;
      this.DIALOG_EDIT = DIALOG_EDIT;

      let url_query = this.$route.query;
      Object.keys(url_query).forEach(key => {
        if (key in this.filters) {
          let value = url_query[key];
          if (key === 'page' || key === 'limit') {
            value = Number(value);
          }
          this.filters[key] = value;
        }
      });
      this.$watch('filters', {
        handler: function() {
          let query = {};
          Object.assign(query, this.filters);
          Object.keys(query).forEach(key => !query[key] && delete query[key]);
          this.$router.replace({ query: query });
        },
        deep: true,
      });
    },
    mounted() {
      this.get_data();
    },
    data() {
      return {
        filters: {
          page: 1,
          limit: 50,
        },
        items: [],
        total: 0,
        status_dict: {},
        active_status_dict: {},
        activity_type_dict: {},
        asset_list: [],
        country_list: [],
        languages: [],
        default_detail_tab: 'EN_US',
        form_data: {},
        dialog_type: null,
        cur_lang: null,
        dialog_visible: false,
        loading: true,
        upload_form: {
          type: null
        },
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() < Date.now() - 86400000;
          },
        },
      };
    },
  };
</script>

<style>
  .img-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .img-uploader .el-upload:hover {
    border-color: #409eff;
  }
  .img-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
  .hide .el-upload--picture-card {
    display: none;
  }
</style>
