<template>
  <div class="table-data">
    <el-row type="flex" justify="space-between" align="middle">
      <el-col :span="22">
        <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
          {{ is_create ? "创建" : "修改" }}商家活动
        </h2>
      </el-col>
    </el-row>

    <el-form :model="form"
             label-width="200px"
             ref="form"
             v-loading="loading">

        <el-form-item label="交易区" required prop="asset">
        <el-select
          :disabled="is_disabled"
          filterable
          v-model="form.asset"
          style="width: 150px"
          placeholder="请选择交易区"
        >
          <el-option
            v-for="k in assets"
            :key="k"
            :label="k"
            :value="k"
          >
          </el-option>
        </el-select>
      </el-form-item>


      <el-form-item label="法币" required prop="fiat">
        <el-select
          :disabled="is_disabled"
          placeholder="请选择"
          filterable
          v-model="form.fiat"
          style="width: 150px"
        >
          <el-option
            v-for="k in fiats"
            :key="k"
            :label="k"
            :value="k"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="活动名称" required prop="name" style="width: 500px">
        <el-input v-model="form.name" :disabled="is_disabled"></el-input>
      </el-form-item>

      <el-form-item label="报名起止时间" required>
        <el-date-picker
          :disabled="is_disabled"
          v-model="form.apply_date_range"
          type="datetimerange"
          range-separator="至"
          start-placeholder="∞"
          end-placeholder="∞"
          :picker-options="pickerOptions"
          style="width: 400px;"
        >
        </el-date-picker>
        <el-row>
          <span>UTC：{{ $formatUTCDate(form.apply_date_range[0]) }} 至 {{ $formatUTCDate(form.apply_date_range[1]) }}</span>
        </el-row>
      </el-form-item>

      <el-form-item label="活动起止时间" required>
        <el-date-picker
          :disabled="is_disabled"
          v-model="form.start_date_range"
          type="datetimerange"
          range-separator="至"
          start-placeholder="∞"
          end-placeholder="∞"
          :picker-options="pickerOptions"
          style="width: 400px;"
        >
        </el-date-picker>
        <el-row>
          <span>UTC：{{ $formatUTCDate(form.start_date_range[0]) }} 至 {{
              $formatUTCDate(form.start_date_range[1])
            }}</span>
        </el-row>
      </el-form-item>

      <el-form-item label="每日有效积分时间(UTC)" required>
        <el-input-number :step="1" :min="0" :max="23" placeholder="0-23" :controls="false"
                         v-model="form.valid_start_hour" :disabled="is_disabled"></el-input-number>
        <span style="margin: 0 10px 0 10px">至</span>
        <el-input-number :step="1" :min="1" :max="24" placeholder="1-24" :controls="false"
                         v-model="form.valid_end_hour" :disabled="is_disabled"></el-input-number>
      </el-form-item>

      <el-form-item label="活动奖励形式" required prop="reward_type">
        <el-select
          :disabled="is_disabled"
          filterable
          v-model="form.reward_type"
          style="width: 150px"
        >
          <el-option
            v-for="(k, v) in reward_types"
            :key="v"
            :label="k"
            :value="v"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="奖励代币" required prop="reward_asset" :disabled="is_disabled">
        <el-select
          :disabled="is_disabled"
          filterable
          v-model="form.reward_asset"
          style="width: 150px"
        >
          <el-option
            v-for="k in reward_assets"
            :key="k"
            :label="k"
            :value="k"
          >
          </el-option>
        </el-select>
      </el-form-item>


      <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
        参与门槛
      </h2>

      <el-form-item label="法币" required prop="fiat">
        <el-select
          :disabled="is_disabled"
          filterable
          v-model="form.fiat"
          style="width: 150px"
          placeholder="请选择法币"
        >
          <el-option
            v-for="k in fiats"
            :key="k"
            :label="k"
            :value="k"
          >
          </el-option>
        </el-select>

        <el-select
          filterable
          multiple
          v-model="form.pay_channel_ids"
          :disabled="is_create && !form.fiat"
          style="width: 300px"
        >
          <el-option
            v-for="k in fiat_pay_channels[form.fiat]"
            :key="k"
            :label="get_channel_name(k)"
            :value="k"
          >
          </el-option>
        </el-select>
      </el-form-item>


      <el-form-item label="活动广告单限额" prop="pay_channel">
        <el-input-number :step="1" :min="0" :disabled="!edit_able"
                         v-model="form.min_limit"></el-input-number>
        <span style="margin: 0 10px 0 10px">至</span>
        <el-input-number :step="1" :min="0" :disabled="!edit_able"
                         v-model="form.max_limit"></el-input-number>
        <div class="el-icon-info" style="margin-left: 10px">
          0表示无限额
        </div>
      </el-form-item>
      <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
        活动奖励
      </h2>

      <el-form-item label="买币区每日奖池" prop="name" style="width: 500px">
        <el-input-number :step="1" :min="0" v-model="form.buy_reward" :disabled="is_disabled"></el-input-number>
      </el-form-item>

      <el-form-item label="卖币区每日奖池" prop="name" style="width: 500px" :disabled="is_disabled">
        <el-input-number :step="1" :min="0" v-model="form.sell_reward" :disabled="is_disabled"></el-input-number>
      </el-form-item>
      <el-form-item label="奖励锁定时长(天)" required prop="name" style="width: 500px" :disabled="is_disabled">
        <el-input-number :step="1" :min="0" :max="14" v-model="form.reward_lock_day" :disabled="is_disabled"></el-input-number>
        <span style="margin: 0 10px 0 10px">（最长14天）</span>
      </el-form-item>

      <el-form-item label="奖励瓜分规则" required>
        <el-form-item>
          <el-button
            type="primary"
            @click="add_config(form.rank_rate_list, empty_rank)"
            circle
            :disabled="is_disabled"
            icon="el-icon-plus"
          ></el-button>
        </el-form-item>
        <div v-for="(item, index) in form.rank_rate_list" :key="index">
          <el-row
            :gutter="20"
            type="flex"
            style="text-align: center; margin-bottom: 22px; max-width: 1000px"
          >
            <el-col :span="1" style="width: 80px">第</el-col>
            <el-col :span="3" style="display: flex;">
              <el-form-item required>
                <el-input-number :step="1" :min="1" :max="100" :precision="0" placeholder="0-100" :controls="false"
                                 style="width: 100px;"
                                 v-model="item.rank_min"
                                 :disabled="is_disabled"
                ></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="1" style="display: flex;">到</el-col>
            <el-col :span="3" style="display: flex;">
              <el-form-item required>
                <el-input-number :step="1" :min="1" :max="100" :precision="0" placeholder="0-100" :controls="false"
                                 style="width: 100px;"
                                 v-model="item.rank_max"
                                 :disabled="is_disabled"
                ></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="2" style="width: 100px">名瓜分奖励</el-col>
            <el-col :span="3" style="display: flex;">
              <el-form-item required>
                <el-input-number :step="1" :min="1" :max="100" :precision="0" placeholder="0-100" :controls="false"
                                 style="width: 100px;"
                                 v-model="item.rank_amount"
                                 :disabled="is_disabled"
                ></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="1" style="width: 20px;">%</el-col>
            <el-col :span="1">
              <el-button
                type="danger"
                @click="remove_config(form.rank_rate_list, index)"
                circle
                :disabled="is_disabled"
                icon="el-icon-minus"
              ></el-button>
            </el-col>
          </el-row>
        </div>
      </el-form-item>

      <el-form-item label="完单奖励倍率" required>
        <el-form-item>
          <el-tooltip placement="right" :open-delay="500">
            <div slot="content">
              <p>奖励倍率是根据用户完单率所处的分级给予不同的倍率</p>
              <p>每天结算的时候，额外得到积分，公式如下</p>
              <p>商家每日分区奖励积分 = (∑挂单得分) × 完单奖励倍率</p>
            </div>
            <i class="el-icon-question"></i>
          </el-tooltip>
          <el-button
            type="primary"
            @click="add_config(form.completion_rate_list, empty_rank)"
            circle
            :disabled="is_disabled"
            icon="el-icon-plus"
          ></el-button>
        </el-form-item>
        <div v-for="(item, index) in form.completion_rate_list"  :key="index">
          <el-row
            :gutter="10"
            type="flex"
            style="text-align: center; margin-bottom: 22px; max-width: 1000px"
          >
            <el-col :span="1" style="width: 80px"> 完单率</el-col>
            <el-col :span="3" style="display: flex;">
              <el-form-item required>
                <el-input-number :step="1" :min="0" :max="100" :precision="0" placeholder="0-100" :controls="false"
                                 style="width: 100px;"
                                 v-model="item.rank_min"
                                 :disabled="is_disabled"
                ></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="1" style="display: flex;"> %到</el-col>
            <el-col :span="3" style="display: flex;">
              <el-form-item required>
                <el-input-number :step="1" :min="0" :max="100" :precision="0" placeholder="0-100" :controls="false"
                                 style="width: 100px;"
                                 v-model="item.rank_max"
                                 :disabled="is_disabled"
                ></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="2" style="width: 100px"> %奖励倍率</el-col>
            <el-col :span="3" style="display: flex;">
              <el-form-item required>
                <el-input-number :step="1" :min="0" :max="10" :precision="2" placeholder="0-10" :controls="false"
                                 style="width: 100px;"
                                 v-model="item.rank_amount"
                                 :disabled="is_disabled"
                ></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="1" style="width: 20px"></el-col>
            <el-col :span="1">
              <el-button
                type="danger"
                @click="remove_config(form.completion_rate_list, index)"
                circle
                :disabled="is_disabled"
                icon="el-icon-minus"
              ></el-button>
            </el-col>
          </el-row>
        </div>
      </el-form-item>

      <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
        多语言配置
      </h2>

      <el-form-item prop="image_key" label="活动图片" class="dialog-caption" required>
        <el-upload
          :disabled="is_disabled"
          class="avatar-uploader"
          ref="upload-icon"
          :show-file-list="false"
          action="/api/upload/image"
          accept=".jpg, .jpeg, .png, .tiff"
          :headers="headers"
          :before-upload="(file) => beforeUpload(file, 200)"
          name="img"
          :on-success="(res) => onSuccess(res)">
          <img v-if="form.file_url" :src="form.file_url" class="avatar">
          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
        </el-upload>
      </el-form-item>

      <MessageEditor
        :messageConfig="messageConfig"
        :contents="form.lang_map"
        :languages="languages"
        :template_filters="template_filters"
        :disabled="is_disabled"
      ></MessageEditor>

      <div style="margin-top: 20px">
        <el-button type="primary" @click="save_form" :disabled="!edit_able">
          保存
        </el-button>
      </div>
    </el-form>
  </div>
</template>


<script>

import MessageEditor from "@/components/MessageEditor.vue";

const post_url = '/api/p2p/mer-act'

export default {
  components: {MessageEditor},
  methods: {
    get_data(new_id = undefined) {
      let act_id = this.$route.query.act_id;
      if (new_id) {
        act_id = new_id
      }
      this.is_create = act_id === '0'
      this.loading = true;
      this.$axios.get(`${post_url}/${act_id}`).then(res => {
        this.loading = false;
        if (res?.data?.code === 0) {
          let data = res.data.data;
          let item = data.item;
          let extra = data.extra;
          this.languages = extra.languages;
          let form = this.form;
          if (!this.is_create) {
            Object.assign(form, item)
            form.apply_date_range = [new Date(item.apply_start_at * 1000), new Date(item.apply_end_at * 1000)];
            form.start_date_range = [new Date(item.start_at * 1000), new Date(item.end_at * 1000)];
            this.is_disabled = form.start_date_range[0] <= new Date().getTime()
            this.edit_able = form.start_date_range[1] > new Date().getTime()
            this.show_completion_config(form.completion_rate_list)
            this.show_rank_config(form.rank_rate_list)
          } else {
            this.add_config(form.completion_rate_list, this.empty_completion)
            this.add_config(form.rank_rate_list, this.empty_rank)
          }
          this.reward_types = extra.reward_types;
          this.reward_assets = extra.reward_assets;
          this.assets = extra.assets;
          this.fiats = extra.fiats;
          this.pay_channels = extra.pay_channels;
          this.fiat_pay_channels = extra.fiat_pay_channels;
          form.lang_map = this.init_lang_map(item.lang_map)
          this.$forceUpdate()
        } else {
          this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
        }
      });
    },
    show_completion_config(list) {
      list.forEach(item => {
        item.rank_min = Number(item.rank_min) * 100
        item.rank_max = Number(item.rank_max) * 100
      })
    },
    submit_completion_config(list) {
      list.forEach(item => {
        item.rank_min = String(item.rank_min / 100)
        item.rank_max = String(item.rank_max / 100)
        item.rank_amount = String(item.rank_amount)
      })
    },
    show_rank_config(list) {
      list.forEach(item => {
        item.rank_amount = Number(item.rank_amount) * 100
      })
    },
    submit_rank_config(list) {
      list.forEach(item => {
        item.rank_amount = String(item.rank_amount / 100)
      })
    },
    check_completion_config(list) {
      let name = "完单奖励倍率"
      if (!this.check_config_base(list, name)) {
        return false
      }
      for (let i = 0; i < list.length; i++) {
        let item = list[i]
        if (i !== list.length - 1 && item.rank_min < list[i + 1].rank_max) {
          this.$message.error(`${name} 第 ${i + 1} 条和 第 ${i + 2} 条规则存在交叉，请检查`);
          return false
        }
      }
      return true
    },
    check_rank_config(list) {
      let name = "奖励瓜分规则"
      if (!this.check_config_base(list, name)) {
        return false
      }
      let tmp_amount = 0
      for (let i = 0; i < list.length; i++) {
        let item = list[i]
        tmp_amount += item.rank_amount
        if (i !== list.length - 1 && item.rank_max >= list[i + 1].rank_min) {
          this.$message.error(`${name} 第 ${i + 1} 条和 第 ${i + 2} 条规则存在交叉，请检查`);
          return false
        }
      }
      if (tmp_amount !== 100) {
        this.$message.error("奖励瓜分规则总和必须等于100%");
        return false
      }
      return true
    },
    check_config_base(list, name) {
      if (!list || list.length === 0) {
        this.$message.error(`最少要有一条 ${name}`);
        return false
      }
      for (let i = 0; i < list.length; i++) {
        let item = list[i]
        if (!item.rank_max || !item.rank_amount) {
          this.$message.error(`${name} 第 ${i + 1} 条规则不完整的规则，请填写完整`);
          return false
        }
        if (item.rank_min > item.rank_max) {
          this.$message.error(`${name} 规则最小值不能大于最大值`);
          return false
        }
      }
      return true
    },
    add_config(list, item) {
      list.push(_.cloneDeep(item))
    },
    remove_config(list, index) {
      list.splice(index, 1);
    },
    get_channel_name(k) {
      return this.pay_channels[k]
    },
    save_data() {
      let tmp_form = _.cloneDeep(this.form)
      let act_id = this.$route.query.act_id;
      if (!this.check_form(tmp_form)) {
        return
      }
      tmp_form.apply_start_at = tmp_form.apply_date_range[0].getTime()
      tmp_form.apply_end_at = tmp_form.apply_date_range[1].getTime()
      tmp_form.start_at = tmp_form.start_date_range[0].getTime()
      tmp_form.end_at = tmp_form.start_date_range[1].getTime()
      tmp_form.lang_map = this.format_submit_data(tmp_form.lang_map)
      this.submit_completion_config(tmp_form.completion_rate_list)
      this.submit_rank_config(tmp_form.rank_rate_list)
      this.submit_lang_map(tmp_form.lang_map)
      if (act_id === '0') {
        this.loading = true;
        this.$axios.post(`${post_url}/list`, tmp_form).then(res => {
          this.loading = false;
          if (res?.data?.code === 0) {
            let data = res.data.data;
            this.$router.replace({query: {act_id: data.act_id}});
            this.get_data(data.act_id)
            this.$message.success("保存成功!");
          } else {
            this.$message.error(`保存失败! (code: ${res.data?.code}; message: ${res.data?.message})`);
          }
        }).catch(err => {
          this.loading = false;
          this.$message.error(`提交失败! (${err})`);
        });
      } else {
        this.loading = true;
        this.$axios.put(`${post_url}/${act_id}`, tmp_form).then(res => {
          this.loading = false;
          if (res?.data?.code === 0) {
            this.$message.success("保存成功!");
          } else {
            this.$message.error(`保存失败! (code: ${res.data?.code}; message: ${res.data?.message})`);
          }
        }).catch(err => {
          this.loading = false;
          this.$message.error(`保存失败! (${err})`);
        });
      }
    },
    save_form() {
      this.save_data();
    },
    check_form(form) {
      if (!(form.valid_end_hour && form.valid_start_hour < form.valid_end_hour)) {
        this.$message.error("每日有效积分时间，且有效积分时间起始时间不能大于结束时间");
        return false
      }
      if (!(form.apply_date_range[0] && form.apply_date_range[1] && form.apply_date_range[0] < form.apply_date_range[1])) {
        this.$message.error("报名时间必填，且报名起始时间不能大于结束时间");
        return false
      }
      if (!(form.start_date_range[0] && form.start_date_range[1] && form.start_date_range[0] < form.start_date_range[1])) {
        this.$message.error("报名时间必填，且活动起始时间不能大于活动开始时间");
        return false
      }
      if (form.apply_date_range[0] > form.start_date_range[0]) {
        this.$message.error("报名起止时间不能大于活动开始时间");
        return false
      }
      if (form.apply_date_range[1] > form.start_date_range[1]) {
        this.$message.error("报名结束时间不能大于活动结束时间");
        return false
      }
      if (!form.buy_reward && !form.sell_reward) {
        this.$message.error("买币区奖励和卖币区奖励至少填写一个");
        return false
      }
      if (!form.buy_reward && !form.sell_reward) {
        this.$message.error("买币区奖励和卖币区奖励至少填写一个");
        return false
      }
      if (!form.reward_asset) {
        this.$message.error("奖励代币不能为空");
        return false
      }
      if (!form.asset) {
        this.$message.error("交易区不能为空");
        return false
      }
      if (!form.fiat) {
        this.$message.error("法币不能为空");
        return false
      }
      if (form.pay_channel_ids.length === 0) {
        this.$message.error("支付渠道不能为空");
        return false
      }
      if (!form.reward_type) {
        this.$message.error("活动奖励形式不能为空");
        return false
      }
      if (!form.file_url) {
        this.$message.error("活动图片不能为空");
        return false
      }
      if (form.min_limit !== 0 && form.max_limit !== 0 && form.max_limit <= form.min_limit) {
        this.$message.error("广告单限额最大值需大于最小值");
        return false
      }
      let tmp_map = _.cloneDeep(form.lang_map)
      for (let lang in tmp_map) {
        let item = tmp_map[lang]
        if (this.is_empty(item)) {
          delete tmp_map[lang]
        } else if (!item.title || !item.content) {
          this.$message.error(`${this.languages[lang]} 多语言配置信息不完整`);
          return false
        }
      }
      // 检查英文在 new_map 中
      if (!tmp_map["EN_US"]) {
        this.$message.error("英文多语言必须填写");
        return false
      }
      if (!this.check_completion_config(form.completion_rate_list)) {
        return false
      }
      if (!this.check_rank_config(form.rank_rate_list)) {
        return false
      }
      return true
    },
    is_empty(obj) {
      return Object.values(obj).every(value => value === "");
    },
    format_submit_data(lang_map) {
      let new_map = _.cloneDeep(lang_map)
      for (let lang in lang_map) {
        if (!lang_map[lang].title) {
          delete new_map[lang]
        }
      }
      return new_map
    },

    init_lang_map(_map = null) {
      let lang_list = Object.keys(this.languages);
      if (!lang_list.includes(this.cur_lang)) {
        this.messageConfig.cur_lang = lang_list[0];
      }
      let tmp_map = _map ? _.cloneDeep(_map) : {}
      for (let key in this.languages) {
        let item = tmp_map[key]
        tmp_map[key] = {
          title: item ? item.display_name : "",
          content: item ? item.desc : "",
        }
      }
      return tmp_map
    },

    beforeUpload(file) {
      let ext = file.name.substring(file.name.lastIndexOf('.') + 1)
      const isLt2M = file.size / 1024 / 1024 < 10    //这里做文件大小限制
      let ext_list = ["jpeg", "png", "jpg"]
      let ext_ret = ext_list.includes(ext)
      if (!ext_ret) {
        this.$message({
          message: `上传文件只能是 ${ext_list}!`,
          type: 'warning'
        });
      }
      if (!isLt2M) {
        this.$message({
          message: '上传文件大小不能超过 10MB!',
          type: 'warning'
        });
      }
      return ext_ret && isLt2M
    },

    onSuccess(res) {
      this.form.file_url = res.data.file_url;
      this.form.image_key = res.data.file_key;
      this.$forceUpdate()
    },

    submit_lang_map(lang_map) {
      // 将对象的 title 属性转换为 display_name, content 属性转换为 desc
      for (let lang in lang_map) {
        let item = lang_map[lang]
        lang_map[lang] = {
          display_name: item.title,
          desc: item.content,
        }
      }
    }
  },
  mounted() {
    this.get_data();
  },
  computed: {
    headers() {
      return {
        'AUTHORIZATION': this.$cookies.get('admin_token')
      }
    },
  },
  data() {
    return {
      messageConfig: {
        extr_params: "",
        has_title: true,
        use_editor: false,
        save_url: "",
        cur_lang: null,
        has_test: false,
        no_save: true,
        title_label: "活动名称",
        content_label: "活动标语"
      },
      template_filters: {
        business: "P2P_MER_ACT",
        enabled: true,
        title: null,
        page: null,
        limit: 10,
      },
      form: {
        name: '',
        apply_date_range: [],
        start_date_range: [],
        valid_start_hour: 0,
        valid_end_hour: 24,
        reward_type: 'COIN',
        reward_asset: '',
        fiat: '',
        asset: '',
        pay_channel: '',
        buy_reward: '',
        sell_reward: '',
        reward_lock_day: 7,
        min_limit: 0,
        max_limit: 0,
        lang_map: {},
        completion_rate_list: [],
        rank_rate_list: [],
        file_url: '',
        image_key: '',
      },
      empty_completion: {
        rank_min: 0,
        rank_max: 0,
        rank_amount: 0
      },
      empty_rank: {
        rank_min: 0,
        rank_max: 0,
        rank_amount: 0
      },
      is_disabled: false,
      edit_able: true,
      act_id: 0,
      reward_types: {},
      reward_assets: [],
      languages: {},
      fiats: [],
      fiat_pay_channels: {},
      pay_channels: {},
      cur_lang: '',
      loading: false,
      is_create: true,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now();
        },
      },
    }
  }
}
</script>



<style>
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
  .avatar {
    max-width: 160px;
    max-height: 160px;
  }
  .el-icon-info{
    display: block;
    color: gray;
    font-size: 12px;
  }
  .dialog-caption{
    font-weight: bold;
  }
  .condition .c-item{
    margin-left: 15px;
  }
</style>
