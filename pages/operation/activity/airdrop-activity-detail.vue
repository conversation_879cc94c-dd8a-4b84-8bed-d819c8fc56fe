<template>
  <div class="table-data">
    <el-row type="flex" justify="space-between" align="middle">
      <el-col :span="22">
        <h2 style="font-family: 'Microsoft YaHei', Arial, sans-serif">
          {{ action === DIALOG_CREATION ? "添加" : "修改" }}空投活动
        </h2>
      </el-col>
    </el-row>
    <el-collapse v-model="activeNames">
      <el-form :model="form_data" ref="form_data" label-width="200px" :disabled="disable_all_edit">
        <el-collapse-item name="11" style="margin-bottom: 20px; margin-top: 10px">
          <template slot="title">
            <h2>基本信息</h2>
          </template>

          <el-form-item label="活动名称" prop="name" required>
            <el-input style="width:300px" v-model="form_data.name" placeholder="活动名称">
            </el-input>
          </el-form-item>

          <span v-if="action === DIALOG_CREATION">
            <el-form-item label="空投类型" prop="airdrop_mode" required>
              <el-select filterable v-model="form_data.airdrop_mode" placeholder="<ALL>">
                <el-option v-for="(title, key) in airdrop_mode_dict" :key="key" :label="title" :value="key"> </el-option>
              </el-select>
            </el-form-item>
          </span>

          <span v-else>
            <el-form-item prop="airdrop_mode" label="空投类型" required>
              {{ airdrop_mode_dict[form_data.airdrop_mode] }}
            </el-form-item>
          </span>

          <el-form-item label="经费来源" required>
            <el-select
                clearable
                filterable
                v-model="form_data.funding_source"
                placeholder="请选择"
                :disabled="disable_edit"
            >
                <el-option
                    v-for="(value, key) in funding_sources"
                    :key="key"
                    :label="value"
                    :value="key"
                >
                </el-option>
            </el-select>
          </el-form-item>

          <el-form-item prop="start_time" label="开始时间" required>
            <el-date-picker v-model="form_data.start_time" :disabled="disable_edit" type="datetime"
              format="yyyy-MM-dd HH:mm:ss" value-format="timestamp" :picker-options="pickerOptions"
              placeholder="只支持日期开始">
            </el-date-picker>
            <el-row>
              <span>UTC时间：{{ $formatUTCDate(form_data.start_time) }}</span>
            </el-row>
          </el-form-item>

          <el-form-item prop="end_time" label="结束时间" required>
            <el-date-picker v-model="form_data.end_time" :disabled="disable_edit" type="datetime"
              format="yyyy-MM-dd HH:mm:ss" value-format="timestamp" :picker-options="pickerOptions"
              placeholder="只支持日期结束">
            </el-date-picker>
            <el-tooltip placement="right" :open-delay="500">
              <div slot="content">
                <p>结束时间后1天为随机空投完成抽签时间，注意避开非工作日</p>
              </div>
              <i class="el-icon-question"></i>
            </el-tooltip>
            <el-row>
              <span>UTC时间：{{ $formatUTCDate(form_data.end_time) }}</span>
            </el-row>
          </el-form-item>

          <el-form-item prop="label_type" label="标签" required>
            <el-select clearable filterable v-model="form_data.label_type" placeholder="<ALL>" :disabled="disable_edit"
              @change="handleLabelTypeChange">
              <el-option v-for="(name, key) in labels_dict" :key="key" :label="name" :value="key"> </el-option>
            </el-select>
            <el-tooltip placement="right" :open-delay="500">
              <div slot="content">
                <p>标签为币种，空投奖励仅可配置一种币种，前端展示活动名称为当前币种；</p>
                <p>标签为其他，空投奖励支持多种组合。</p>
              </div>
              <i class="el-icon-question"></i>
            </el-tooltip>
          </el-form-item>

          <el-form-item label="空投人数" prop="total_count" required>
            <el-input type='number' style="width:300px" v-model="form_data.total_count" :disabled="disable_edit"
              placeholder="请输入整数">
            </el-input>
          </el-form-item>

          <el-form-item label="每人可领预估价值" required>
            <el-tooltip placement="right" :open-delay="500">
              <div slot="content">
                <p>若配置=，前端直接展示，比如 XXX USDT</p>
                <p>若配置≈，前端展示 ≈ ，比如≈XXX USDT</p>
              </div>
              <i class="el-icon-question"></i>
            </el-tooltip>
            <el-select v-model="form_data.estimate_type" :disabled="disable_edit" placeholder="请选择">
              <el-option v-for="(name, key) in estimate_type_dict"
                :key="key" :label="name" :value="key">
              </el-option>
            </el-select>
            <el-input-number style="width:300px" v-model="form_data.amount"
              :disabled="disable_edit" placeholder="请输入" :precision="8" :controls="false">
            </el-input-number>
            <el-select clearable filterable v-model="form_data.asset"
              :disabled="disable_edit"
              placeholder="请选择">
              <el-option v-for="asset in asset_list" :key="asset" :label="asset" :value="asset"> </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="学习了解更多内容 链接" prop="more_reward_url">
            <el-input style="width:450px" v-model="form_data.more_reward_url" placeholder="选填(满足url格式)">
            </el-input>
          </el-form-item>
        </el-collapse-item>

        <el-collapse-item name="12" style="margin-bottom: 20px; margin-top: 20px">
          <template slot="title">
            <h2>空投奖励</h2>
          </template>
          <el-form-item>
            <el-dropdown @command="handle_reward_creation" :disabled="disable_edit">
              <el-button plain>新增奖励类型<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-for="v, k in reward_type_dict" :command="k" :key="k">
                  {{ v }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </el-form-item>

          <div class="row-item" v-for="condition in reward_conditions" :key="`asset-reward-${condition}`">
            <el-divider v-if="condition == 'ASSET'"></el-divider>
            <el-form-item prop="ASSET" label="币种奖励" v-if="condition == 'ASSET'" required>

              <el-form v-loading="loading">
                <el-row type="flex" justify="space-between" align="middle">
                  <el-col :span="2">
                    <el-tooltip content="新增币种奖励" placement="right" :open-delay="500" :hide-after="2000">
                      <el-button icon="el-icon-plus" circle size="mini"
                                @click="handleAddAssetReward" :disabled="disable_edit"></el-button>
                    </el-tooltip>
                  </el-col>
                  <el-col :span="2">
                    <el-button type="danger" icon="el-icon-delete"
                      circle size="mini"
                      @click="handle_deletion_reward_condition(condition)" :disabled="disable_edit">
                    </el-button>
                  </el-col>
                  <el-col :span="22"></el-col>
                </el-row>
                <div v-for="(item, index) in form_data.asset_rewards" :key="index">
                  <el-form-item
                    :inline="true"
                    required
                  >
                    <el-form-item
                      :label="`币种${index+1}`"
                      prop="asset">
                      <el-select clearable filterable v-model="item.asset" :disabled="disable_edit"
                        placeholder="请选择" @change="handleAssetRewardChange(index, item.asset)">
                        <el-option v-for="asset in asset_list" :key="asset" :label="asset" :value="asset"> </el-option>
                      </el-select>
                      <el-button @click="deleteAssetRewardItem(item, index)" type="danger" icon="el-icon-delete"
                       circle size="mini" :disabled="disable_edit">
                      </el-button>
                    </el-form-item>
                    <el-form-item label="每人可领" prop="amount">
                      <el-input-number style="width:300px" v-model="item.amount" :disabled="disable_edit"
                        placeholder="最多支持8位小数" :precision="8" :controls="false">
                      </el-input-number>
                      <el-row>
                        <span>空投总量 {{ get_total_amount(item.amount) }} {{ item.asset }}</span>
                      </el-row>
                    </el-form-item>
                  </el-form-item>
                </div>
              </el-form>

            </el-form-item>

            <el-form-item label="冻结天数" prop="lock_day" required  v-if="condition == 'ASSET'">
              <el-input-number  style="width:300px" :disabled="disable_edit" v-model="form_data.lock_day"
                placeholder="天数" :precision="0">
                <i slot="suffix" style="font-style:normal;margin-right: 10px;">天 </i>
              </el-input-number>
            </el-form-item>
          </div>

          <div class="row-item" v-for="condition in reward_conditions" :key="`coupon-reward-${condition}`">
            <el-divider v-if="condition == 'COUPON'"></el-divider>
            <el-form-item prop="COUPON" label="卡券奖励" v-if="condition == 'COUPON'">

              <el-form :model="form_data.coupon_rewards" v-loading="loading">
                <el-row type="flex" justify="space-between" align="middle">
                  <el-col :span="2">
                    <el-button type="danger" icon="el-icon-delete"
                      circle size="mini"
                      @click="handle_deletion_reward_condition(condition)" :disabled="disable_edit">
                    </el-button>
                  </el-col>
                  <el-col :span="22"></el-col>

                </el-row>
                <el-row type="flex" justify="space-between" align="middle">
                  <el-col :span="8">
                    <el-button type="primary"
                              @click="handleAddCouponReward" :disabled="disable_edit">
                      关联卡券
                    </el-button>
                    <el-tooltip placement="right" :open-delay="500">
                      <div slot="content">
                        <p>每张卡券只能关联1个空投活动，而同个空投活动可以关联多张不同类型卡券</p>
                        <p>推荐配置：卡券发放14天 + 7天领取有效期 </p>
                        <p>限时空投：活动期间卡券需有效，卡券发放时间 ≤ 活动开始时间 < 活动结束时间 ≤ 卡券发放结束时间(卡券发放时间+有效期)</p>
                        <p>随机空投：发放前保证卡券有效，卡券发放时间 ≤ 空投结束时间 < 空投结束时间+3天 ≤ 卡券发放结束时间（卡券发放时间+领取有效期）</p>
                      </div>
                      <i class="el-icon-question"></i>
                    </el-tooltip>
                  </el-col>
                </el-row>
                <div v-for="(item, index) in form_data.coupon_rewards" :key="index">
                  <el-form-item
                    :inline="true"
                    required
                  >
                    <el-form-item
                      :label="`卡券${index+1}`"
                      prop="coupon">
                      <template slot-scope="scope">
                          <span>
                            {{ item.apply_id }} / {{ item.title }} / {{ item.coupon_id }} / {{ coupon_types[item.coupon_type] }} / {{ item.value }} / {{ item.value_type }} / {{ item.remark }}
                          </span>
                          <el-button @click="deleteCouponRewardItem(item, index)" circle size="mini" type="danger" icon="el-icon-delete" :disabled="disable_edit"></el-button>
                      </template>
                    </el-form-item>
                  </el-form-item>
                </div>

                <el-form-item label="每人可领" v-if="form_data.coupon_rewards.length !== 0">
                  <el-input type='number' style="width:300px" v-model="coupon_amount" :disabled="true">
                  </el-input>
                  <el-row>
                    <span>空投总量 {{ get_coupon_total_amount() }}</span>
                  </el-row>
                </el-form-item>
              </el-form>

            </el-form-item>
          </div>

          <div class="row-item" v-for="condition in reward_conditions" :key="`equity-reward-${condition}`">
            <el-divider v-if="condition == 'EQUITY'"></el-divider>
            <el-form-item prop="EQUITY" label="权益奖励" v-if="condition == 'EQUITY'">
              <el-form :model="form_data" v-loading="loading">
                <el-row type="flex" justify="space-between" align="middle">
                  <el-col :span="2">
                    <el-button type="danger" icon="el-icon-delete"
                      circle size="mini"
                      @click="handle_deletion_reward_condition(condition)" :disabled="disable_edit">
                    </el-button>
                  </el-col>
                  <el-col :span="22"></el-col>

                </el-row>
                <el-row type="flex" justify="space-between" align="middle">
                  <el-col :span="8">
                      <EquitySelector v-model="currentEquity" @input="handleEquityChange()" :disabled="disable_edit"/>
                  </el-col>
                </el-row>
                <div v-for="(item, index) in form_data.equity_rewards" :key="`equity-${index}`">
                  <el-form-item
                    :inline="true"
                    required
                  >
                    <el-form-item
                      :label="`权益${index+1}`"
                      prop="equity">
                      <el-input
                        style="width: 500px"
                        :value="getEquityDisplayText(item)"
                        :disabled="true"
                        placeholder="权益信息">
                      </el-input>
                      <el-button
                        @click="deleteEquityRewardItem(item, index)"
                        circle
                        size="mini"
                        type="danger"
                        icon="el-icon-delete"
                        :disabled="disable_edit">
                      </el-button>
                    </el-form-item>
                  </el-form-item>
                </div>

                <el-form-item label="每人可领" v-if="form_data.equity_rewards.length !== 0">
                  <el-input type='number' style="width:300px" v-model="equity_amount" :disabled="true">
                  </el-input>
                  <el-row>
                    <span>空投总量 {{ get_equity_total_amount() }}</span>
                  </el-row>
                </el-form-item>
              </el-form>
            </el-form-item>
          </div>

        </el-collapse-item>
        <el-collapse-item name="13" style="margin-bottom: 20px; margin-top: 20px">
          <template slot="title">
            <h2>参与条件</h2>
          </template>
            <el-form v-model="form_data" :disabled="disable_edit" label-width="200px">
            <el-form-item>
              <el-dropdown @command="handle_filter_creation">
                <el-button plain>新增筛选条件<i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item v-for="v, k in condition_keys" :command="k" :key="k">
                    {{ v }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </el-form-item>
            <div class="row-item" v-for="condition in select_conditions" :key="condition">
              <el-form-item prop="KYC" label="KYC条件" v-if="condition == 'KYC'">
                <el-radio-group v-model="form_data.KYC" :disabled="disable_edit">
                  <template>
                    <el-radio v-for="(key, value) in kyc_condition_type_dict" :key="key" :label="key">{{ value }}</el-radio>
                  </template>
                </el-radio-group>
                <el-button type="danger" icon="el-icon-delete" circle size="mini" style="margin-left: 10px;"
                  @click="handle_deletion_condition(condition)" :disabled="disable_edit"></el-button>
              </el-form-item>

              <el-form-item prop="VIP" label="VIP条件" v-if="condition == 'VIP'">
                <el-radio-group v-model="form_data.VIP" :disabled="disable_edit">
                  <template>
                    <el-radio v-for="(key, value) in vip_condition_type_dict" :key="key" :label="key">{{ value }}</el-radio>
                  </template>
                </el-radio-group>
                <el-button type="danger" icon="el-icon-delete" circle size="mini" style="margin-left: 10px;"
                  @click="handle_deletion_condition(condition)"></el-button>
              </el-form-item>

              <el-form-item label="是否持有币种" prop="NEED_HOLDING" v-if="condition == 'HOLDING'">
                <el-select filterable v-model="form_data.NEED_HOLDING" placeholder="请选择持有是否持有币种币种">
                  <el-option v-for="name in holding_list" :key="name.value" :label="name.label" :value="name.value">
                  </el-option>
                </el-select>
                <el-select clearable filterable v-model="form_data.ASSET_HOLDING" placeholder="请选择持有币种">
                  <el-option v-for="asset in asset_list" :key="asset" :label="asset" :value="asset"> </el-option>
                </el-select>
                <el-button type="danger" icon="el-icon-delete" circle size="mini" style="margin-left: 10px;"
                  @click="handle_deletion_condition(condition)"></el-button>
              </el-form-item>

              <el-form-item prop="TRADE_BUSINESS_TYPE" label="时间类型" v-if="condition == 'TRADE_VALUE'">
                <el-radio-group v-model="form_data.TRADE_BUSINESS_TYPE">
                  <template>
                    <el-radio v-for="(key, value) in trade_business_type_dict" :key="key" :label="key">{{ value
                    }}</el-radio>
                  </template>
                </el-radio-group>
              </el-form-item>

              <el-form-item prop="TRADE_TYPE" label="业务范围" v-if="condition == 'TRADE_VALUE'">
                <el-checkbox-group v-model="TRADE_TYPE_RANGE">
                  <el-checkbox v-for="(v, k) in trade_type_dict" :label="k" :key="k"
                               @change="val => handle_type_range(k, val)">
                    {{ v }}
                  </el-checkbox>
                  <el-button type="danger" icon="el-icon-delete" circle size="mini" style="margin-left: 10px;"
                    @click="handle_deletion_condition(condition)"></el-button>
                </el-checkbox-group>
              </el-form-item>

              <el-form-item label="累积交易额" v-if="condition == 'TRADE_VALUE'">
                <el-select filterable v-model="form_data.TRADE_DAY_RANGE">
                  <el-option v-for="name in trade_day_range_list" :key="name.value" :label="name.label" :value="name.value">
                  </el-option>
                </el-select>
                <el-select filterable v-model="form_data.TRADE_OP_TYPE">
                  <el-option v-for="name in trade_value_op_list" :key="name.value" :label="name.label" :value="name.value">
                  </el-option>
                </el-select>

                <el-input style="width:180px" v-model="form_data.TRADE_VALUE" placeholder="金额（USD）">
                </el-input> USD

                <el-button type="danger" icon="el-icon-delete" circle size="mini" style="margin-left: 10px;"
                  @click="handle_deletion_condition(condition)"></el-button>
              </el-form-item>

              <el-form-item label="总资产" v-if="condition == 'BALANCE_VALUE'">
                <el-select filterable v-model="form_data.BALANCE_OP_TYPE">
                  <el-option v-for="name in balance_value_op_list" :key="name.value" :label="name.label"
                    :value="name.value">
                  </el-option>
                </el-select>
                <el-input style="width:180px" v-model="form_data.BALANCE_VALUE" placeholder="金额（USD）">
                </el-input> USD

                <el-button type="danger" icon="el-icon-delete" circle size="mini" style="margin-left: 10px;"
                  @click="handle_deletion_condition(condition)"></el-button>
              </el-form-item>

              <el-form-item label="注册时间" v-if="condition == 'REGISTERED_VALUE'">
                <el-select filterable v-model="form_data.REGISTERED_OP_TYPE">
                  <el-option v-for="name in registered_value_op_list" :key="name.value" :label="name.label"
                    :value="name.value">
                  </el-option>
                </el-select>
                <el-date-picker v-model="form_data.REGISTERED_VALUE" type="datetime" format="yyyy-MM-dd HH:mm:ss"
                  value-format="timestamp" placeholder="注册时间">
                </el-date-picker>
                <span>UTC时间：{{ $formatUTCDate(form_data.REGISTERED_VALUE) }}</span>
                <el-button type="danger" icon="el-icon-delete" circle size="mini" style="margin-left: 10px;"
                  @click="handle_deletion_condition(condition)"></el-button>
              </el-form-item>

              <el-form-item label="使用过此功能" v-if="condition == 'USED_VALUE'">
                <el-checkbox-group v-model="not_used_list" :min="1" :max="3">
                  <el-checkbox v-for="(v, k) in not_used_dict" :label="k" :key="k">{{ v }}</el-checkbox>
                  <el-button type="danger" icon="el-icon-delete" circle size="mini" style="margin-left: 10px;"
                    @click="handle_deletion_condition(condition)"></el-button>
                </el-checkbox-group>
              </el-form-item>

            </div>
          </el-form>
        </el-collapse-item>


    <el-collapse-item name="14" style="margin-bottom: 20px; margin-top: 20px">
      <template slot="title">
        <h2>学习内容</h2>
      </template>
      <el-form-item label="标题" required v-if="form_data.label_type == 'OTHER'">
        <el-col :span="2">
          <el-upload
              action
              :key="Date.now()"
              ref="upload"
              name="batch-upload"
              :auto-upload="false"
              :show-file-list="false"
              :on-change="handle_update_template"
              accept=".xlsx"
            >
              <el-button type="primary" @click="$refs.upload.submit()">上传</el-button>
            </el-upload>
        </el-col>
        <el-col :span="1">
          <el-button type="primary" @click="dowloadTemplate('title')">下载模板</el-button>
        </el-col>
      </el-form-item>


      <el-form-item v-if="form_data.label_type == 'OTHER'">
        <AITranslateTab
          v-if="loaded"
          :langs="langs"
          :contents="form_data.details"
          :translateAttrNames="['title']"
          business="AIRDROP"
          :businessId="airdrop_detail_id ? airdrop_detail_id.toString() : null"
          :showAsync="false"
          @cur-lang-change="handleCurLangChange"
          style="margin-left: 100px;"
        >
          <template v-slot="{ lang, content }">
            <el-input type="textarea" :rows="3" placeholder="请输入标题" maxlength="512"
              v-model="form_data.details[lang].title"
            >
            </el-input>
          </template>
        </AITranslateTab>

      </el-form-item>
      <el-form-item prop="info_url" label="详情" v-if="form_data.label_type == 'OTHER'">
        <el-input v-model="form_data.info_url" :disabled="disable_edit" placeholder="请输入URL链接"></el-input>
      </el-form-item>

      <el-form-item prop="assets" label="币种" v-if="form_data.label_type == 'OTHER'">
        <el-select filterable clearable multiple placeholder="请选择" v-model="form_data.assets" style="width:500px" :disabled="disable_edit">
          <el-option v-for="asset in asset_list" :key="asset" :label="asset" :value="asset"> </el-option>
        </el-select>
      </el-form-item>

      <AITranslateTab
          v-if="loaded"
          :langs="langs"
          :contents="form_data.details"
          :translateAttrNames="['summary', 'introductions']"
          business="AIRDROP"
          :businessId="airdrop_detail_id ? airdrop_detail_id.toString() : null"
          :showAsync="false"
          :showExport="true"
          @cur-lang-change="handleCurLangChange"
          @export="export_translation"
          style="margin-left: 100px;"
        >
          <template v-slot="{ lang, content }">
            <el-form-item label="项目简介" required>
              <el-col :span="2">
                <el-upload
                  action
                  ref="upload"
                  name="batch-upload"
                  :auto-upload="false"
                  :show-file-list="false"
                  :on-change="handle_update_summary_template"
                  accept=".xlsx"
                >
                  <el-button type="primary" @click="$refs.upload.submit()">上传</el-button>
                </el-upload>
            </el-col>
            <el-col :span="3">
              <el-button type="primary" @click="dowloadTemplate('summary')">下载模板</el-button>
                </el-col>
            </el-form-item>
            <el-form-item>
                  <el-input type="textarea" :rows="8" placeholder="请输入简介" style="padding-bottom: 30px;" maxlength="512"
                    v-model="form_data.details[lang].summary">
                  </el-input>
                  <span>项目图片</span>
                  <el-upload
                    class="img-uploader"
                    accept=".jpg, .jpeg, .png"
                    action="/api/upload/image"
                    list-type="picture-card"
                    :limit="1"
                    name="img"
                    :on-preview="handlePictureCardPreview"
                    :ref="`upload-${lang}`"
                    :file-list="form_data.details[lang].file_list"
                    :headers="{'AUTHORIZATION': $cookies.get('admin_token')}"
                    :on-success="res => upload_success(form_data.details[lang], res)"
                    :on-remove="res => upload_remove(form_data.details[lang], res)"
                    >
                    <i class="el-icon-plus"></i>
                    <div slot="tip" class="el-upload__tip">
                        只能上传jpeg/png文件，且大小不超过1Mb
                    </div>
                  </el-upload>
                  <el-input type="textarea" v-model="form_data.details[lang].video_url" :rows="1" placeholder="请输入视频链接"
                    style="padding-bottom: 30px;"></el-input>
            </el-form-item>
            <el-form-item label="项目介绍" required>
                  <div :class="lang">
                    <Editor v-model="form_data.details[lang].introductions" :options="options"></Editor>
                  </div>
            </el-form-item>
          </template>
        </AITranslateTab>
    </el-collapse-item>


    <el-collapse-item name="15" style="margin-bottom: 20px; margin-top: 20px">
      <template slot="title">
        <h2>答题</h2>
      </template>
      <el-row></el-row>
      <el-form-item label="答题信息">
        <el-col :span="2">
          <el-upload ref="upload" :action="`/api/activity/airdrop/question-upload`" name="batch-upload"
            :show-file-list="false" :headers="{ AUTHORIZATION: $cookies.get('admin_token') }"
            :on-success="importSuccess" :on-error="importError" accept=".xlsx">
            <el-button type="primary" @click="import_question_configs(default_detail_tab)">上传</el-button>
          </el-upload>
        </el-col>
        <el-col :span="3">
          <el-button type="primary" @click="handleDownloadTemplate">下载模板</el-button>
        </el-col>
        <el-col :span="3">
          <el-button type="primary" @click="create_add_question_config(default_detail_tab)"
            icon="el-icon-plus"></el-button>
        </el-col>
      </el-form-item>

      <el-form-item>
        <AITranslateTab
          v-if="loaded"
          :langs="langs"
          :contents="form_data.question_configs"
          :translateAttrNames="['question', 'A', 'B', 'C', 'answer_analysis']"
          business="AIRDROP_QUESTION"
          :businessIdAttr="'id'"
          :showAsync="false"
          :showExport="true"
          @cur-lang-change="handleCurLangChange"
          @export="export_question_translation"
        >
          <template v-slot="{ lang, content }">
            <template v-for="(config, index) in content" required>
              <el-row :gutter="5">
                <el-form-item label="问题描述">
                  <el-input type="textarea" v-model="config.question" :rows="1" placeholder="问题描述"
                    style="padding-bottom: 30px;"></el-input>
                </el-form-item>
                <el-col :span="4" style="margin-left: 4%;">
                  <el-form-item label="答案A" :prop="config.A" :rules="{
                    message: ' ',
                    trigger: 'blur',
                  }">
                    <el-input style="width:120px" v-model="config.A" placeholder="输入答案"> </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="4" style="margin-left: 4%;">
                  <el-form-item label="答案B" :prop="config.B" :rules="{
                    message: ' ',
                    trigger: 'blur',
                  }">
                    <el-input style="width:120px" v-model="config.B" placeholder="输入答案"> </el-input>
                  </el-form-item>
                </el-col>

                <el-col :span="4" style="margin-left: 4%;">
                  <el-form-item label="答案C" :prop="config.C" :rules="{
                    message: ' ',
                    trigger: 'blur',
                  }">
                    <el-input style="width:120px" v-model="config.C" placeholder="输入答案"> </el-input>
                  </el-form-item>
                </el-col>

                <el-col :span="3" style="margin-left: 16%;">
                  <el-button type="danger" circle style="margin-left: 5px;"
                    @click="create_remove_question_config(content, index)"
                    icon="el-icon-delete"></el-button>
                </el-col>
              </el-row>

              <el-form-item :prop="config.answer" label="正确答案">
                <el-radio-group v-model="config.answer">
                  <template>
                    <el-radio v-for="(value, key) in answer_option_dict" :prop="config.answer" :key="key"
                      :label="key">{{
                        value }}</el-radio>
                  </template>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="解析" style="padding-bottom:50px">
                <el-input v-model="config.answer_analysis" placeholder="输入答案解析"> </el-input>
              </el-form-item>
            </template>
          </template>
        </AITranslateTab>
      </el-form-item>
    </el-collapse-item>
      </el-form>
    </el-collapse>
    <template>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handle_close" :disabled="disable_all_edit">取 消</el-button>
        <el-button type="primary" @click="handle_submit" :disabled="disable_all_edit">确 定</el-button>
      </span>
    </template>
    <el-dialog :visible.sync="dialogVisible">
        <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>

    <el-dialog
      title="请选择关联卡券"
      :visible.sync="dialog_coupon_show"
      :before-close="handleClose"
      width="80%">
      <template>
        <div class="table-data">
          <el-table :data="coupon_items"
                    v-loading="coupon_loading"
                    highlight-current-row
                    @cell-click="handleSelectCoupon"
                    stripe>
            <el-table-column label="发放ID"
                             prop="id"
                             show-overflow-tooltip>
            </el-table-column>

            <el-table-column label="发放标题"
                             prop="title"
                             show-overflow-tooltip>
            </el-table-column>

            <el-table-column
              prop="source"
              label="发放类型"
              :formatter="(row) => `${source_types[row.source]}`"
            >
            </el-table-column>

            <el-table-column
              prop="coupon_type"
              label="卡劵类型"
              :formatter="(row) => `${coupon_types[row.coupon_type]}`"
            >
            </el-table-column>

            <el-table-column prop="value" label="面额">
              <template slot-scope="scope">
                {{ scope.row.value }} {{ scope.row.value_type }}
              </template>
            </el-table-column>

            <el-table-column prop="total_count" label="发放总数"> </el-table-column>

            <el-table-column
              prop="send_at"
              label="发放时间"
              :formatter="(row) => $formatDate(row.send_at)"
            >
            </el-table-column>

            <el-table-column prop="status" label="状态"> </el-table-column>

            <el-table-column prop="remark" label="备注"> </el-table-column>

          </el-table>
          <el-pagination :current-page.sync="template_filters.page"
                         :page-size.sync="template_filters.limit"
                         :page-sizes="[10, 50, 100, 200, 500]"
                         :total="coupon_total"
                         @size-change="couponQuery"
                         @current-change="couponQuery"
                         :hide-on-single-page="true"
                         layout="total, sizes, prev, pager, next, jumper">
          </el-pagination>
        </div>
      </template>
    </el-dialog>


  </div>
</template>

<script>
import Vue from 'vue';
import moment from 'moment';
import XLSX from "xlsx";
import VueClipboard from 'vue-clipboard2';
import { readFile } from "@/plugins/tools";
import AITranslateTab from '@/components/AITranslateTab.vue';
import EquitySelector from '@/components/EquitySelector.vue';
Vue.use(VueClipboard);

require("via-editor/via-editor.css");
require("via-editor/theme.css");
require("via-editor/core.css");

const { Component } = require('via-editor');
const base_url = '/api/activity/airdrop/activity';
export default {
  components: {
    "Editor": Component,
    AITranslateTab,
    EquitySelector
  },
  methods: {
    questionSortLangs() {
      let originalArray = Object.keys(this.langs);
      this.questionDialogTranslate.sources = this.questionDialogTranslate.sources.slice().sort((a, b) => {
        return originalArray.indexOf(a) - originalArray.indexOf(b);
      });
      this.questionDialogTranslate.selectedTargets = this.questionDialogTranslate.selectedTargets.slice().sort((a, b) => {
        return originalArray.indexOf(a) - originalArray.indexOf(b);
      });
    },

    handleEquityChange() {
      let equity = JSON.parse(JSON.stringify(this.currentEquity))
      if (this.form_data.equity_rewards.length >= 5) {
          this.$message.error(`奖励卡券数量已达上线!`);
          return;
        }
        for (let i in this.form_data.equity_rewards) {
          let obj = this.form_data.equity_rewards[i];
          let equity_id = obj['equity_id'];
          if (equity_id === equity.id) {
            this.$message.error(`请勿重复选择关联权益!`);
            return;
          }
        }
      this.form_data.equity_rewards.push({
        equity_id: equity.id,
        cost_amount: equity.cost_amount,
        cost_asset: equity.cost_asset,
        extra_data: equity.extra_data,
        remark: equity.remark,
        type: equity.type,
      })
    },

    getEquityDisplayText(item) {
      console.log(item);
      if (!item) return '';

      const parts = [
        item.equity_id,
        item.type,
        item.extra_data?.cashback_scope || '',
        `${item.cost_amount}${item.cost_asset}`,
        item.extra_data?.cashback_asset || '',
        `${item.extra_data?.effective_days || ''}日`,
        item.remark || ''
      ].filter(part => part);

      return parts.join(' / ');
    },

    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    handle_deletion_condition(condition) {
      this.select_conditions = this.select_conditions.filter(t => t != condition)
      if (condition == 'KYC') {
        this.form_data.KYC = "FALSE"
      } else if (condition == "VIP") {
        this.form_data.VIP = "VIP0"
      } else if (condition == "HOLDING") {
        this.form_data.NEED_HOLDING = "NOT_LIMITED"
        this.form_data.ASSET_HOLDING = ""
      } else if (condition == "TRADE_VALUE") {
        this.form_data.TRADE_OP_TYPE = "NOT_LIMITED"
        this.form_data.TRADE_VALUE = 0
        this.TRADE_TYPE_RANGE = []
      } else if (condition == "BALANCE_VALUE") {
        this.form_data.BALANCE_OP_TYPE = "NOT_LIMITED"
        this.form_data.BALANCE_VALUE = 0
      } else if (condition == "REGISTERED_VALUE") {
        this.form_data.REGISTERED_OP_TYPE = "NOT_LIMITED"
      } else if (condition == "USED_VALUE") {
        this.form_data.USED_VALUE = []
        this.not_used_list = []
      }
    },
    handle_filter_creation(key) {
      if (this.select_conditions.includes(key)) {
        this.$message.error(`筛选条件已存在!`);
        return
      }
      if (key == "KYC") {
        this.form_data.KYC = 'TRUE'
      } else if (key == "VIP") {
        this.form_data.VIP = "VIP1"
      } else if (key == "HOLDING") {
        this.form_data.NEED_HOLDING = "HOLD"
      } else if (key == "TRADE_VALUE") {
        this.form_data.TRADE_DAY_RANGE = 7
        this.form_data.TRADE_VALUE_OP = "GREATER"
        this.form_data.TRADE_OP_TYPE = "GREATER"
        this.form_data.TRADE_BUSINESS_TYPE = "REAL_TIME"
        this.TRADE_TYPE_RANGE = []
      } else if (key == "BALANCE_VALUE") {
        this.form_data.BALANCE_OP_TYPE = "GREATER"
      } else if (key == "REGISTERED_VALUE") {
        this.form_data.REGISTERED_OP_TYPE = "GREATER"
      }
      this.select_conditions.push(key);
    },
    handleLabelTypeChange() {
      if (this.form_data.label_type === 'ASSET') {
        this.handle_deletion_reward_condition("COUPON");
        if (this.form_data.asset_rewards.length > 0) {
          this.form_data.asset_rewards = [this.form_data.asset_rewards[0]];
        }
      }
    },
    handle_deletion_reward_condition(condition) {
      this.reward_conditions = this.reward_conditions.filter(t => t != condition)
      if (condition == 'ASSET') {
        this.form_data.asset_rewards = []
      } else if (condition == "COUPON") {
        this.form_data.coupon_rewards = []
      } else if (condition == "EQUITY") {
        this.form_data.equity_rewards = []
      }
    },
    handle_reward_creation(key) {
      if (this.reward_conditions.includes(key)) {
        this.$message.error(`奖励类型已存在!`);
        return
      }
      if (this.form_data.label_type === 'ASSET') {
        if (key === 'COUPON') {
          this.$message.error(`标签选择币种时，奖励类型只能是币种!`);
          return
        }
      }
      this.reward_conditions.push(key);
    },
    deleteAssetRewardItem(item, index) {
      this.form_data.asset_rewards.splice(index, 1);
    },
    deleteCouponRewardItem(item, index) {
      this.form_data.coupon_rewards.splice(index, 1);
    },
    deleteEquityRewardItem(item, index) {
      this.form_data.equity_rewards.splice(index, 1);
    },
    handleAddAssetReward() {
      if (this.form_data.label_type === 'ASSET') {
        if (this.form_data.asset_rewards.length >= 1) {
          this.$message.error(`标签为币种，空投奖励仅可配置一种币种!`);
          return;
        }
      }
      if (this.form_data.asset_rewards.length >= 10) {
        this.$message.error(`奖励币种数量已达上线!`);
        return;
      }
      this.form_data.asset_rewards.push({
        asset: '',
        amount: 0,
      });
    },
    handleAssetRewardChange(index, asset) {
      for (let i in this.form_data.asset_rewards) {
        if (index === Number(i)) {
          continue;
        }
        let obj = this.form_data.asset_rewards[i];
        let already_asset = obj['asset'];
        if (asset === already_asset) {
          this.form_data.asset_rewards[index]['asset'] = '';
          this.$message.error(`请勿重复选择奖励币种!`);
          return;
        }
      }
    },
    handleAddCouponReward() {
      this.dialog_coupon_show = true;
      this.couponQuery();
    },
    handleSelectCoupon(row, column, cell, event) {
      this.$confirm(`确认选择?`).then(() => {
        let id = row.id;
        if (!id) {
          return;
        }
        if (this.form_data.coupon_rewards.length >= 5) {
          this.$message.error(`奖励卡券数量已达上线!`);
          return;
        }
        for (let i in this.form_data.coupon_rewards) {
          let obj = this.form_data.coupon_rewards[i];
          let apply_id = obj['apply_id'];
          if (apply_id === id) {
            this.$message.error(`请勿重复选择关联卡券!`);
            return;
          }
        }
        this.form_data.coupon_rewards.push({
          apply_id: id,
          title: row.title,
          coupon_id: row.coupon_id,
          coupon_type: row.coupon_type,
          value: row.value,
          value_type: row.value_type,
          remark: row.remark,
        });
        this.dialog_coupon_show = false;
      });
    },
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          this.dialog_coupon_show = false;
          done();
        })
        .catch(_ => {
        });
    },
    couponQuery() {
      this.coupon_loading = true;

      this.$axios.get('/api/coupon/apply/list', {params: this.template_filters}).then(
        res => {
          this.coupon_loading = false;
          if (res && res.data.code === 0) {
            let data = res.data.data;
            this.coupon_items = data.items;
            this.coupon_total = data.total;
            this.coupon_types = data.coupon_types;
            this.source_types = data.source_types;
          } else {
            this.coupon_items = [];
            this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
          }
        }
      );
    },
    get_total_amount(amount) {
      if (!this.form_data.total_count) {
        return '-';
      }
      if (!amount) {
        return '-';
      }
      let total_amount = Number(this.form_data.total_count) * Number(amount);
      return total_amount;
    },
    get_coupon_total_amount() {
      if (!this.form_data.total_count) {
        return '-';
      }
      if (!this.coupon_amount) {
        return '-';
      }
      if (this.form_data.coupon_rewards.length === 0) {
        return '-';
      }
      let total_amount = Number(this.form_data.total_count) * Number(this.coupon_amount);
      return total_amount;
    },
    get_equity_total_amount() {
      if (!this.equity_amount) {
        return '-';
      }
      if (this.form_data.equity_rewards.length === 0) {
        return '-';
      }
      let total_amount = Number(this.form_data.total_count || 0) * Number(this.equity_amount);
      return total_amount;
    },
    notice(title, message) {
      this.$alert(message, title, {
        confirmButtonText: '确定',
      });
    },
    res_success_notice(res) {
      let title = '提交成功';
      this.notice(title, title);
    },
    res_fail_notice(res) {
      let title = '提交失败';
      let message = `(code: ${res.data?.code}; message: ${res.data?.message}; data: ${res.data?.data})`;
      this.notice(title, message);
    },
    res_error_notice(error) {
      let title = '提交失败';
      let message = `(code: ${error.response.status}; message: ${error.message}; data: ${res.data?.data})`;
      this.notice(title, message);
    },
    handle_number_input(field, place) {
      place = place || 8;
      let val = this.form_data[field];
      if (val === undefined || val === '') {
        return;
      }
      // /^\d*(\.?\d{0,8})/g
      var re = new RegExp('^\\d*(\.?\\d{0,' + place + '})', 'g');
      let res = String(val).match(re)[0];
      this.form_data[field] = res;
    },
    to_utc_date(date_str) {
      return moment.utc(date_str, 'YYYY-MM-DD').format('YYYY-MM-DD HH:mm:ss');
    },
    get_data() {
      let id = this.$route.query.id
      this.loading = true
      this.airdrop_detail_id = id
      this.action = this.DIALOG_EDIT
      if (id === "0") {
        this.action = this.DIALOG_CREATION
      }
      this.$axios.get(`${base_url}/${id}`).then(res => {
        if (res?.data?.code === 0) {
          let data = res.data.data
          this.languages = data.languages
          this.languages.forEach(d => {
            this.$set(this.question_configs, d.lang, [])
            this.$set(this.introduction_configs, d.lang, [])
          });
          this.airdrop_mode_dict = data.airdrop_mode_dict;
          this.labels_dict = data.labels;
          this.status_dict = data.status_dict;
          this.active_status_dict = data.active_status_dict;
          this.condition_keys = data.condition_key_dict;
          this.not_used_dict = data.not_used_dict;
          this.asset_list = data.assets;
          this.funding_sources = data.funding_sources;
          this.estimate_type_dict = data.estimate_type_dict;
          this.reward_type_dict = data.reward_type_dict;
          this.coupon_types = data.coupon_types;
          this.langs = data.langs;
          this.trade_type_dict = data.trade_type_dict;
          if (id === "0") {
            this.handle_creation();
          } else {
            this.handle_edit(data.activity);
          }
        } else {
          this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
        }
        this.loading = false
        this.loaded = true
      });

    },
    create_add_question_config(lang) {
      if (!this.form_data.question_configs[lang]) {
        this.$set(this.form_data.question_configs, lang, [])
      }
      this.form_data.question_configs[lang].push({
        A: '',
        B: '',
        C: '',
        answer: 'A',
        question: '',
        answer_analysis: '',
      })
    },
    create_add_introduction_config(lang) {
      this.form_data.introduction_configs[lang].push({
        content: '',
        title: '',
      })
    },
    create_remove_question_config(details, index) {
      details.splice(parseInt(index), 1);
    },
    create_remove_introduction_config(details, index) {
      details.splice(parseInt(index), 1);
    },
    import_question_configs(lang) {
      this.cur_lang = lang
    },
    upload_success(detail, res) {
      detail.cover_url = res.data.file_url;
      detail.cover = res.data.file_key;
      detail.hideUploadAdd = true;
      detail.file_list = [{ name: '', url: res.data.file_url }];
    },
    upload_remove(detail, res) {
      detail.cover = '';
      detail.cover_url = '';
      detail.hideUploadAdd = false;
      detail.file_list = [];
    },
    handle_creation() {
      this.form_data = {
        gift_type: "ASSET",
        airdrop_mode: 'RANDOM', // default selected
        VIP: 'VIP0', // default selected
        TRADE_BUSINESS_TYPE: 'REAL_TIME', // default selected
        KYC: 'FALSE', // default selected
        NEED_HOLDING: 'NOT_LIMITED', // default selected
        ASSET_HOLDING: '', // default selected
        TRADE_DAY_RANGE: 7, // default selected
        TRADE_VALUE_OP: 'GREATER', // default selected
        TRADE_OP_TYPE: 'NOT_LIMITED', // default selected
        BALANCE_OP_TYPE: 'NOT_LIMITED', // default selected
        TRADE_VALUE: 0, // default selected
        BALANCE_VALUE: 0, // default selected
        REGISTERED_OP_TYPE: 'NOT_LIMITED', // default selected
        USED_VALUE: [],
        details: this.languages.reduce((acc, lang) => {
          acc[lang.lang] = {lang:lang.lang, title:'', introductions:'', summary:''};
          return acc;
        }, {}),
        question_configs: this.question_configs,
        introduction_configs: this.introduction_configs,
        assets: [],
        info_url: "",
        funding_source: null,
        asset_rewards: [],
        coupon_rewards: [],
        equity_rewards: [],
      };

      this.dialog_type = this.DIALOG_CREATION;
      this.dialog_visible = true;
    },

    handle_edit(row) {
      let condition_dict = row.condition_dict;
      delete row['condition_dict']
      this.form_data = { ..._.clone(row), ...condition_dict };
      this.form_data.gift_type = "ASSET";
      if (this.form_data.hasOwnProperty("USED_VALUE")) {
        this.not_used_list = this.form_data['USED_VALUE']
      }
      if (this.form_data.hasOwnProperty("TRADE_VALUE")) {
        this.TRADE_TYPE_RANGE = this.form_data['TRADE_TYPE_RANGE'] || []
      }
      if (this.form_data.active_status == 'FINISHED') {
        this.disable_all_edit = true
      }
      if (this.disable_all_edit || this.form_data.status == 'ONLINE') {
        this.disable_edit = true
      }
      this.select_conditions = Object.keys(condition_dict);
      this.reward_conditions = [];
      if (this.form_data.asset_rewards.length > 0) {
        this.reward_conditions.push("ASSET");
      }
      if (this.form_data.coupon_rewards.length > 0) {
        this.reward_conditions.push("COUPON");
      }
      if (this.form_data.equity_rewards.length > 0) {
        this.reward_conditions.push("EQUITY");
      }
      this.dialog_type = this.DIALOG_EDIT;
      this.dialog_visible = true;
      this.form_data.start_time = this.form_data.start_time * 1000;
      this.form_data.end_time = this.form_data.end_time * 1000;
      // 将 details 数组转换为以 lang 为 key 的字典
      if (Array.isArray(this.form_data.details)) {
        const detailsDict = {};
        this.form_data.details.forEach(detail => {
          detailsDict[detail.lang] = detail;
        });
        this.form_data.details = detailsDict;
      }
  },
    handleOnOffline(row, is_online) {
      let new_status = is_online ? 'ONLINE' : 'OFFLINE';
      this.$confirm(is_online ? '确认上架？' : '确认下架？')
        .then(_ => {
          this.$axios
            .patch(base_url, { id: row.id, status: new_status })
            .then(res => {
              if (res?.data?.code === 0) {
                this.res_success_notice(res);
                this.form_data = {};
                this.get_data();
              } else {
                this.res_fail_notice(res);
              }
            })
            .catch(e => {
              this.res_error_notice(e);
            });
        })
        .catch(_ => { });
    },
    handle_close() {
      this.$confirm(`是否退出当前页面?`).then(() => {
        window.opener = null;
        window.open("about:blank", "_top").close();
      });
    },
    handle_type_range(k, value) {
      let all = "ALL"
      if (value === true) {
        if (k === all) {
          if (this.TRADE_TYPE_RANGE.length !== 1) {
            this.$message.error(`请选取消其他范围，再勾选全部`);
            _.pull(this.TRADE_TYPE_RANGE, all)
          }
        } else {
          if (this.TRADE_TYPE_RANGE.includes(all)) {
            this.$message.error(`请选取消全部，再勾选其他范围`);
            _.pull(this.TRADE_TYPE_RANGE, k)
          }
        }
      }
    },
    handle_submit() {
      this.submit_validate_data()
    },
    importSuccess(response, file, fileList) {
      if (response.code !== 0) {
        this.$alert(response.message, '错误'
        ).then(() => {
        }).catch(() => {
        });
        return;
      }
      this.$alert('成功上传' + response.data.length + '条记录', '成功');

      this.$set(this.form_data.question_configs, this.cur_lang, [])

      response.data.forEach((e) => {
        this.form_data.question_configs[this.cur_lang].push({
          A: e.A === null ? '' : String(e.A),
          B: e.B === null ? '' : String(e.B),
          C: e.C === null ? '' : String(e.C),
          answer: e.answer === null ? '' : String(e.answer),
          question: e.question === null ? '' : String(e.question),
          answer_analysis: e.answer_analysis === null ? '' : String(e.answer_analysis),
        });
      });
    },
    importError(err, file, fileList) {
      this.$alert("上传失败", "错误")
    },
    handleDownloadTemplate() {
      let url = "/api/activity/airdrop/template";
      this.$download_from_url(url, 'airdrop-template.xlsx')
    },
    fmt_introduction(fileInfo) {
      var result = ""
      if (fileInfo.title1) {
        result += `<p style="font-size: 18px; font-weight: bold;">${fileInfo.title1}</p>`
      }
      if (fileInfo.content1) {
        result += `<p>${fileInfo.content1}</p>`
      }
      if (fileInfo.title2) {
        result += `<p  style="font-size: 18px; font-weight: bold;">${fileInfo.title2}</p>`
      }
      if (fileInfo.content2) {
        result += `<p>${fileInfo.content2}</p>`
      }
      if (fileInfo.title3) {
        result += `<p style="font-size: 18px; font-weight: bold;">${fileInfo.title3}</p>`
      }
      if (fileInfo.content3) {
        result += `<p>${fileInfo.content3}</p>`
      }
      if (fileInfo.title4) {
        result += `<p style="font-size: 18px; font-weight: bold;">${fileInfo.title4}</p>`
      }
      if (fileInfo.content4) {
        result += `<p>${fileInfo.content4}</p>`
      }
      return result
    },
    async write_data_to_from(data_arr, file_type) {
      let langMapper = {}
      data_arr.forEach((item) => {
        let lang = item.lang.toUpperCase();
        if (file_type == "title") {
          langMapper[lang] = {
            title: item.title
          }
        } else if (file_type == "summary") {
          langMapper[lang] = {
            summary: item.summary,
            title1: item.title1,
            content1: item.content1,
            title2: item.title2,
            content2: item.content2,
            title3: item.title3,
            content3: item.content3,
            title4: item.title4,
            content4: item.content4,
          }
        }
      });

      Object.values(this.form_data.details).forEach((detail) => {
          var fileInfo = langMapper[detail.lang]
        if (!fileInfo) return;
        if (file_type == "title") {
          this.$set(detail, 'title', fileInfo.title);
        } else if (file_type == "summary") {
          this.$set(detail, 'summary', fileInfo.summary);
          this.$set(detail, 'introductions', this.fmt_introduction(fileInfo));
        }
      })

      this.$message.success("上传成功");
    },

    async handle_update_template(file) {
      let dataBinary = await readFile(file.raw);
      let workBook = XLSX.read(dataBinary, { type: "binary", cellDates: true });
      let workSheet = workBook.Sheets[workBook.SheetNames[0]];
      let json_data = XLSX.utils.sheet_to_json(workSheet);
      let data_arr = await this.fmt_read_excel_data(json_data, "title");
      await this.write_data_to_from(data_arr, "title");
    },
    async handle_update_summary_template(file) {
      let dataBinary = await readFile(file.raw);
      let workBook = XLSX.read(dataBinary, { type: "binary", cellDates: true });
      let workSheet = workBook.Sheets[workBook.SheetNames[0]];
      let json_data = XLSX.utils.sheet_to_json(workSheet);
      let data_arr = await this.fmt_read_excel_data(json_data, "summary");
      await this.write_data_to_from(data_arr, "summary");
    },
    async fmt_read_excel_data(json_data, file_type) {
      let character = {
        lang: "lang"
      };
      if (file_type == 'title') {
        character['title'] = "标题"
      }
      if (file_type == "summary") {
        character['summary'] = "一句话简介"
        character['title1'] = "项目介绍-标题1"
        character['content1'] = "项目介绍-内容1"
        character['title2'] = "项目介绍-标题2"
        character['content2'] = "项目介绍-内容2"
        character['title3'] = "项目介绍-标题3"
        character['content3'] = "项目介绍-内容3"
        character['title4'] = "项目介绍-标题4"
        character['content4'] = "项目介绍-内容4"
      }
      let arr = [];
      json_data.forEach((item) => {
        let obj = {};
        for (let key in character) {
          if (!character.hasOwnProperty(key)) break;
          let v = item[character[key]];
          if (!v) {
            obj[key] = "";
            continue
          }
          obj[key] = v.replace(/\r?\n/g, "<br />");
        }
        arr.push(obj);
      });
      return arr;
    },
    dowloadTemplate(template_type) {
      let arr = [];
      this.languages.forEach(lang => {
        let arr_item = {
          语言: lang.display,
          lang: lang.lang.toLocaleLowerCase(),
        };
        if (template_type == "title") {
          arr_item['标题'] = ""
         }
        if (template_type == "summary") {
          arr_item['一句话简介'] = ''
          arr_item['项目介绍-标题1'] = ''
          arr_item['项目介绍-内容1'] = ''
          arr_item['项目介绍-标题2'] = ''
          arr_item['项目介绍-内容2'] = ''
          arr_item['项目介绍-标题3'] = ''
          arr_item['项目介绍-内容3'] = ''
          arr_item['项目介绍-标题4'] = ''
          arr_item['项目介绍-内容4'] = ''
        }
        arr.push(arr_item);
      })
      // 将json数据变为sheet数据
      let sheet = XLSX.utils.json_to_sheet(arr);
      // 新建表格
      let book = XLSX.utils.book_new();
      // 在表格中插入一个sheet
      XLSX.utils.book_append_sheet(book, sheet, "sheet1");
      // 通过xlsx的writeFile方法将文件写入
      XLSX.writeFile(book, `${template_type}_template.xlsx`);
    },
    process_form_data() {
      var new_form_data = _.cloneDeep(this.form_data);

      // 将 details 字典转换回数组
      new_form_data.details = Object.values(new_form_data.details);
      console.log('process_form_data')
      console.log(new_form_data.details)
      new_form_data.details.forEach(d => {
          delete d.display;
          delete d.cover_url;
          delete d.hideUploadAdd;
          delete d.file_list;
      });
      new_form_data.coupon_rewards.forEach(d => {
        delete d.title;
        delete d.coupon_id;
        delete d.coupon_type;
        delete d.value;
        delete d.value_type;
        delete d.remark;
      });

      new_form_data.equity_rewards.forEach(d => {
        delete d.cost_asset;
        delete d.cost_amount;
        delete d.extra_data;
        delete d.remark;
        delete d.type;
      });
      // 兼容老数据没有新增字段，赋予默认值
      if (!new_form_data.KYC) {
        new_form_data.KYC = "FALSE"
      }
      if (!new_form_data.VIP) {
        new_form_data.VIP = "VIP0"
      }
      if (!new_form_data.NEED_HOLDING) {
        new_form_data.NEED_HOLDING = "NOT_LIMITED"
        new_form_data.ASSET_HOLDING = ""
      }
      if (!new_form_data.TRADE_VALUE_OP) {
        // todo 这个字段没有用
        new_form_data.TRADE_VALUE_OP = 'GREATER'
      }
      if (!new_form_data.TRADE_DAY_RANGE) {
        new_form_data.TRADE_DAY_RANGE = 7
      }
      if (!new_form_data.TRADE_OP_TYPE) {
        new_form_data.TRADE_OP_TYPE = 'NOT_LIMITED'
      }
      if (!new_form_data.TRADE_VALUE) {
        new_form_data.TRADE_VALUE = 0
        new_form_data.TRADE_BUSINESS_TYPE = "REAL_TIME"
        new_form_data.TRADE_TYPE_RANGE = []
      }
      if (!new_form_data.BALANCE_OP_TYPE) {
        new_form_data.BALANCE_OP_TYPE = 'NOT_LIMITED'
      }
      if (!new_form_data.BALANCE_VALUE) {
        new_form_data.BALANCE_VALUE = 0
      }
      if (!new_form_data.REGISTERED_VALUE) {
        new_form_data.REGISTERED_OP_TYPE =  'NOT_LIMITED'
      }
      if (!new_form_data.info_url) {
        new_form_data.info_url = ''
      }
      if (this.form_data.lock_day === '') {
        this.form_data.lock_day = 0
      }
      new_form_data['USED_VALUE'] = this.not_used_list
      new_form_data['TRADE_TYPE_RANGE'] = this.TRADE_TYPE_RANGE

      return new_form_data;
    },
    submit_validate_data() {
      let method = 'post';
      if (this.dialog_type !== this.DIALOG_CREATION) {
        method = 'put';
      }
      let check_success = true
      Object.values(this.form_data.details).forEach((detail) => {
        if (detail.title && detail.title.length > 100) {
          this.$message.error(`语言(${detail.lang})的标题长度超过不能100`);
          check_success = false
        }
      })


      if (!check_success) {
        return
      }

      if (this.form_data.asset_rewards.length > 0) {
        if (this.form_data.lock_day === '') {
          this.$message.error(`请输入冻结天数`);
          return;
        }
      }

      let errorMessages = [];

      Object.entries(this.form_data.question_configs).forEach(([lang, questions]) => {
        questions.forEach((question, index) => {
          if (question.question && question.question.length > 256) {
            errorMessages.push(`语言(${lang})的第${index + 1}题的题目长度不能超过256`);
          }

          if (question.A && question.A.length > 256) {
            errorMessages.push(`语言(${lang})的第${index + 1}题的选项A长度不能超过256`);
          }

          if (question.B && question.B.length > 256) {
            errorMessages.push(`语言(${lang})的第${index + 1}题的选项B长度不能超过256`);
          }

          if (question.C && question.C.length > 256) {
            errorMessages.push(`语言(${lang})的第${index + 1}题的选项C长度不能超过256`);
          }

          if (question.answer && question.answer.length > 256) {
            errorMessages.push(`语言(${lang})的第${index + 1}题的答案长度不能超过256`);
          }

          if (question.answer_analysis && question.answer_analysis.length > 512) {
            errorMessages.push(`语言(${lang})的第${index + 1}题的解析长度不能超过512`);
          }
        });
        if (questions.length < 3) {
          errorMessages.push(`语言(${lang})的题目数量必须大于等于3`);
        }
      });

      if (errorMessages.length > 0) {
        this.$message({
          dangerouslyUseHTMLString: true,
          message: `<span>${errorMessages.join('<br /><br />')}</span>`,
          type: 'error',
          showClose: true
        });
        return;
      }

      var new_form_data = this.process_form_data();
      this.disable_btn = true
      this.$axios[method](base_url, new_form_data)
        .then(res => {
          if (res?.data?.code === 0) {
            // this.form_data = {};
            // this.dialog_visible = false;
            // let data = res.data.data;
            // this.$router.replace({query: {id: data.id}});
            // this.$route.query.id = data.id;
            this.res_success_notice(res);
            // this.get_data();
            this.$router.push("/operation/activity/airdrop-activity");
          } else {
            this.res_fail_notice(res);
          }
        })
        .catch(e => {
          console.log(e);
          this.res_error_notice(e);
        }).finally(() => { this.disable_btn = false })
    },
    format_lottery_status(row) {
      if (row.lottery_status === true && row.airdrop_mode === 'RANDOM') {
        return '已开奖'
      }
      if (row.lottery_status === false && row.airdrop_mode === 'RANDOM') {
        return '未开奖'
      }
      return '/'
    },
    format_datetime(datetime) {
      return Number(datetime * 1000)
    },
    handleCurLangChange(lang) {
      this.default_detail_tab = lang
    },
    export_translation() {
      if (['pending', 'error'].includes(this.contTransStr)) {
        this.$message.error("未翻译完毕/翻译存在错误，请重新完成翻译后再导出")
        return
      }
      const XLSX = require('xlsx');
      let wb = XLSX.utils.book_new();
      let ws_data = []
      Object.entries(this.form_data.details).forEach(([key, item]) => {
        if (item.summary || item.title) {
          let intro_obj = this.parse_introduction(item.introductions)
          let obj = {
            '语言': this.langs[item.lang],
            'lang': item.lang,
          }
          console.log(this.langs)
          console.log(`item.lang:${item.lang}`, obj)
          if (this.form_data.label_type !== 'ASSET') {
            obj['标题'] = item.title
          }
          Object.assign(obj, {
            '一句话简介': item.summary,
            '项目介绍-标题1': intro_obj.title1,
            '项目介绍-内容1': intro_obj.content1,
            '项目介绍-标题2': intro_obj.title2,
            '项目介绍-内容2': intro_obj.content2,
            '项目介绍-标题3': intro_obj.title3,
            '项目介绍-内容3': intro_obj.content3,
            '项目介绍-标题4': intro_obj.title4,
            '项目介绍-内容4': intro_obj.content4,
          })
          ws_data.push(obj)
        }
      })
      let sheet = XLSX.utils.json_to_sheet(ws_data);
      XLSX.utils.book_append_sheet(wb, sheet);
      if (ws_data.length !== 0) {
        XLSX.writeFile(wb, `内容导出.xlsx`);
      } else {
        this.$message.error("当前所有语言内容数据为空")
      }
    },
    export_question_translation() {
      if (['pending', 'error'].includes(this.qnTransStr)) {
        this.$message.error("未翻译完毕/翻译存在错误，请重新完成翻译后再导出")
        return
      }
      console.log(this.form_data.question_configs)
      const XLSX = require('xlsx');
      let wb = XLSX.utils.book_new();
      Object.entries(this.form_data.question_configs).forEach(([key, value]) => {
        if (value.length > 0) {
          let ws_data = []
          for (let item of value) {
            ws_data.push({
              '题目': item.question,
              'A': item.A,
              'B': item.B,
              'C': item.C,
              '答案': item.answer,
              '解析': item.answer_analysis,
            })
          }
          let sheet = XLSX.utils.json_to_sheet(ws_data);
          XLSX.utils.book_append_sheet(wb, sheet, key);
        }
      });
      if (wb.SheetNames.length !== 0) {
        XLSX.writeFile(wb, `题目导出.xlsx`);
      } else {
        this.$message.error("当前所有语言数据为空")
      }
    },
    parse_introduction(htmlString) {
      if (!htmlString) {
        return {}
      }
      const parser = new DOMParser();
      const doc = parser.parseFromString(htmlString, 'text/html');

      const result = {};

      // 查找每一个 <p> 标签，提取内容
      const paragraphs = doc.querySelectorAll('p');

      paragraphs.forEach((p, index) => {
        const isBold = p.style.fontWeight === 'bold'; // 检查是否是标题
        if (isBold) {
          // 计算 title 的名称
          const titleKey = `title${Math.floor(index / 2) + 1}`;
          result[titleKey] = p.textContent;
        } else {
          // 计算 content 的名称
          const contentKey = `content${Math.floor(index / 2) + 1}`;
          result[contentKey] = p.textContent;
        }
      });
      return result;
    }
  },
  mounted() {
    this.get_data();
  },
  computed: {
    options: function () {
      return {
        image: {
          size: {
            max: 5000, // KB
            error: '图片大小不能大于5000KB',
          },
          accepts: ['.png', '.jpg', '.jpeg'],
          upload: (file) => {
            return new Promise((resolve) => {
              let formdata = new FormData()
              formdata.set('img', file)
              let response = { code: 0, data: "", message: "成功" }
              this.$axios.post('/api/upload/image', formdata).then(res => {
                if (res.data.code === 0) {
                  response.data = res.data.data.file_url;
                  resolve(response);
                } else {
                  response.code = res.data?.code;
                  response.message = res.data?.message;
                  resolve(response)
                }
              }).catch(res => {
                response.code = -1;
                response.message = "error";
                resolve(response)
              })
            });
          },
        },
        video: {
          validateUrl(url) {
            return {
              valid: true,
              message: 'ok',
            };
          },
        },
        container: {
          className: this.contents
        },
        quill: { bounds: '.el-tabs__content', debug: 'error'},
      }
    },
  },
  data() {
    return {
      airdrop_detail_id: null,
      filters: {
        airdrop_mode: null,
        asset: null,
        page: 1,
        limit: 50,
      },
      template_filters: {
        dynamic_user_type: 'AIRDROP_ACTIVITY',
        sort_name: 'send_at',
        exclude_coupon_type: 'CASHBACK_FEE',
        page: 1,
        limit: 10
      },
      disable_edit: false,
      disable_all_edit: false,
      DIALOG_CREATION: 'creation',
      DIALOG_EDIT: 'edit',
      action: this.DIALOG_CREATION,
      activeNames: ["11", "12", "13", "14", "15"],
      items: [],
      total: 0,
      coupon_amount: 1,
      equity_amount: 1,
      dialog_coupon_show: false,
      coupon_items: [],
      coupon_total: 0,
      coupon_loading: false,
      coupon_types: {},
      source_types: {},
      dialogVisible: false,
      funding_sources: {},
      airdrop_mode_dict: {},
      not_used_list: [],
      TRADE_TYPE_RANGE: [],
      labels_dict: {},
      status_dict: {},
      active_status_dict: {},
      asset_list: [],
      question_configs: {},
      introduction_configs: {},
      condition_keys: {},
      not_used_dict: {},
      trade_type_dict: {},
      estimate_type_dict: {},
      reward_type_dict: {},
      reward_conditions: [],
      select_conditions: [],
      currentEquity: null,
      kyc_condition_type_dict: { '需要完成KYC': 'TRUE' },
      holding_list: [{ label: '持有过', value: 'HOLD' }, { label: '未持有过', value: 'NOT_HOLD' }],
      vip_condition_type_dict: { 'VIP1及以上': 'VIP1', 'VIP2及以上': 'VIP2', 'VIP3及以上': 'VIP3', 'VIP4及以上': 'VIP4', 'VIP5及以上': 'VIP5' },
      trade_business_type_dict: { '实时更新': 'REAL_TIME', '活动开始前': 'BEFORE_START' },
      trade_day_range_list: [{ label: '7天', value: 7 }, { label: '15天', value: 15 }, { label: '30天', value: 30 },],
      trade_value_op_list: [{ label: '≥', value: 'GREATER' }, { label: '<', value: 'LESS' }],
      balance_value_op_list: [{ label: '≥', value: 'GREATER' }, { label: '<', value: 'LESS' }],
      registered_value_op_list: [{ label: '>', value: 'GREATER' }, { label: '<', value: 'LESS' }],
      answer_option_dict: { 'A': 'A', 'B': 'B', 'C': 'C' },
      languages: [],
      default_detail_tab: 'EN_US',
      form_data: {
        details: {},
        question_configs: {},
      },
      langs: {},
      dialog_type: null,
      dialogImageUrl: "",
      cur_lang: null,
      dialog_visible: false,
      disable_btn: false,
      loading: true,
      loaded: false,
      upload_form: {
        type: null
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now();
        },
      },
    };
  },
};
</script>

<style>
.img-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.img-uploader .el-upload:hover {
  border-color: #409eff;
}
.img-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.hide .el-upload--picture-card {
  display: none;
}

.via-editor {
  width: 85%!important;
}

.via-editor .ql-bubble .ql-icon-picker .ql-picker-item {
  width: 100%;
}

.ql-toolbar {
  z-index: 999999;
}

.equity-display-text {
  font-family: 'Courier New', monospace;
  background-color: #f5f7fa;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}
</style>
