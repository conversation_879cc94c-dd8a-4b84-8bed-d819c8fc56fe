<template>
    <div class="table-data">
        <h2 style="font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif">
            交易排名>活动管理
        </h2>

        <el-form :inline="true" :model="search_data">
            <el-form-item label="活动类型">
                <el-select clearable v-model="search_data.type">
                    <el-option
                        v-for="(value, key) in types"
                        :key="key"
                        :label="value"
                        :value="key"
                    >
                    </el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="奖励币种">
                <el-select
                    clearable
                    filterable
                    v-model="search_data.gift_asset"
                    placeholder="币种名称"
                >
                    <el-option
                        v-for="item in $store.state.spot.info.asset_list"
                        :key="item"
                        :label="item"
                        :value="item"
                    >
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="进行状态">
                <el-select clearable v-model="search_data.state">
                    <el-option
                        v-for="(value, key) in states"
                        :key="key"
                        :label="value"
                        :value="key"
                    >
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="显示状态">
                <el-select clearable v-model="search_data.status">
                    <el-option
                        v-for="(value, key) in status_map"
                        :key="key"
                        :label="value"
                        :value="key"
                    >
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-tooltip
                    content="刷新"
                    placement="right"
                    :open-delay="500"
                    :hide-after="2000"
                >
                    <el-button
                        icon="el-icon-refresh-left"
                        circle
                        @click="get_data"
                    ></el-button>
                </el-tooltip>
            </el-form-item>
        </el-form>

        <div class="col-md-12">
            <div class="pull-left">
                <el-row>共 {{ total }} 个</el-row>
            </div>
            <div class="pull-right">
                <el-tooltip
                    content="添加"
                    placement="right"
                    :open-delay="500"
                    :hide-after="2000"
                >
                    <el-button
                        type="primary"
                        icon="el-icon-plus"
                        circle
                        @click="handleCreate"
                    ></el-button>
                </el-tooltip>
            </div>
        </div>

        <el-table :data="items" v-loading="loading" stripe>
            <el-table-column
              prop="id"
              label="ID">
              <template slot-scope="scope">
                <el-tooltip :content="'activity_id: ' + scope.row.activity_id" placement="top">
                  <span>{{ scope.row.id }}</span>
                </el-tooltip>
              </template>
            </el-table-column>

            <el-table-column prop="title" label="标题"> </el-table-column>

            <el-table-column
                prop="type"
                label="活动类型"
                :formatter="(row) => types[row.type]"
            >
            </el-table-column>

            <el-table-column prop="gift_asset" label="奖励币种">
            </el-table-column>

            <el-table-column prop="join_user_count" label="达标人数/报名人数">
                <template slot-scope="scope">
                    <el-link
                        :href="`/operation/activity/trade-users?trade_activity_id=${scope.row.id}`"
                        type="primary"
                        target="_blank"
                        :underline="false"
                        style="
                            width: 100%;
                            font-weight: normal;
                            display: inline-block;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                        "
                    >
                    {{ scope.row.qualified_user_count }} / {{ scope.row.join_user_count }}
                    </el-link>
                </template>
            </el-table-column>
            <el-table-column prop="trade_user_count" label="交易人数"> </el-table-column>
            <el-table-column label="送币余量/总数量">
                <template slot-scope="scope">
                    <el-tooltip
                        class="item"
                        effect="dark"
                        :content="
                            '剩余' +
                            scope.row.left_amount +
                            scope.row.gift_asset
                        "
                        placement="bottom-end"
                    >
                        <el-link
                            :href="
                                '/operation/activity/gift-history?activity_id=' +
                                scope.row.activity_id +
                                '&asset=' +
                                scope.row.gift_asset
                            "
                            type="primary"
                            target="_blank"
                            :underline="false"
                            style="
                                width: 100%;
                                font-weight: normal;
                                display: inline-block;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                white-space: nowrap;
                            "
                        >
                            {{ scope.row.left_amount }} /
                            {{ scope.row.gift_amount }}
                        </el-link>
                    </el-tooltip>
                </template>
            </el-table-column>

            <el-table-column
                prop="start_time"
                label="开始时间"
                :formatter="
                    (row) => $formatDate(row.start_time, 'YYYY-MM-DD HH:mm')
                "
            >
            </el-table-column>

            <el-table-column
                prop="end_time"
                label="结束时间"
                :formatter="
                    (row) => $formatDate(row.end_time, 'YYYY-MM-DD HH:mm')
                "
            >
            </el-table-column>

            <el-table-column prop="state" label="进行状态"> </el-table-column>

            <el-table-column prop="status" label="显示状态">
                <template slot-scope="scope">
                    {{
                        scope.row.status == "FINISHED"
                            ? "已上架"
                            : status_map[scope.row.status]
                    }}
                </template>
            </el-table-column>

            <el-table-column prop="operation" label="操作">
                <template slot-scope="scope">
                    <template v-if="scope.row.status === 'ONLINE'">

                        <el-tooltip
                            content="编辑"
                            placement="right"
                            :open-delay="500"
                            :hide-after="2000"
                        >
                            <el-button
                                size="mini"
                                type="primary"
                                icon="el-icon-edit"
                                circle
                                @click="handleEdit(scope.row)"
                            ></el-button>
                        </el-tooltip>
                        <el-tooltip
                            content="暂停"
                            placement="right"
                            :open-delay="500"
                            :hide-after="2000"
                        >
                            <el-button
                                size="mini"
                                type="warning"
                                icon="el-icon-close"
                                circle
                                @click="handleSuspend(scope.row)"
                            ></el-button>
                        </el-tooltip>
                        <el-tooltip
                            content="删除"
                            placement="right"
                            :open-delay="500"
                            :hide-after="2000"
                        >
                            <el-button
                                size="mini"
                                type="warning"
                                icon="el-icon-delete"
                                circle
                                @click="handleDelete(scope.row)"
                            ></el-button>
                        </el-tooltip>
                    </template>
                    <template v-else-if="scope.row.status === 'PENDING'">
                        <el-tooltip
                            content="编辑/查看"
                            placement="right"
                            :open-delay="500"
                            :hide-after="2000"
                        >
                            <el-button
                                size="mini"
                                type="primary"
                                icon="el-icon-edit"
                                circle
                                @click="handleEdit(scope.row)"
                            ></el-button>
                        </el-tooltip>
                        <el-tooltip
                            content="开启"
                            placement="right"
                            :open-delay="500"
                            :hide-after="2000"
                        >
                            <el-button
                                size="mini"
                                type="success"
                                icon="el-icon-success"
                                circle
                                @click="handleReopen(scope.row)"
                            ></el-button>
                        </el-tooltip>
                        <el-tooltip
                            content="删除"
                            placement="right"
                            :open-delay="500"
                            :hide-after="2000"
                        >
                            <el-button
                                size="mini"
                                type="warning"
                                icon="el-icon-delete"
                                circle
                                @click="handleDelete(scope.row)"
                            ></el-button>
                        </el-tooltip>
                    </template>
                    <template v-else>
                        <el-tooltip
                            content="查看"
                            placement="right"
                            :open-delay="500"
                            :hide-after="2000"
                        >
                            <el-button
                                size="mini"
                                type="primary"
                                icon="el-icon-edit"
                                circle
                                @click="handleEdit(scope.row)"
                            ></el-button>
                        </el-tooltip>
                    </template>
                </template>
            </el-table-column>
        </el-table>

        <el-pagination
            :current-page.sync="search_data.page"
            :page-size.sync="search_data.limit"
            :page-count.sync="page_count"
            @current-change="get_data"
            @size-change="get_data"
            :page-sizes="[10, 20]"
            :hide-on-single-page="true"
            layout="total, sizes, prev, pager, next, jumper"
        >
        </el-pagination>

        <el-backtop></el-backtop>

        <el-dialog
            :title="
                action === DIALOG_CREATION
                    ? '添加交易送币配置'
                    : '编辑/查看交易送币配置'
            "
            :visible.sync="show"
            v-if="show"
            width="70%"
        >
            <el-form :model="submit_data" ref="submit_data" label-width="250px">
                <el-form-item label="活动类型" required>
                    <el-select
                        v-model="submit_data.type"
                        :disabled="edit_disabled"
                        @change="clear_markets_and_type"
                    >
                        <el-option
                            v-for="(v, k) in types"
                            :key="k"
                            :label="v"
                            :value="k"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="交易市场" required>
                    <el-row
                :gutter="0"
                type="flex"
                :key="index"
                style="text-align: center; margin-bottom: 22px"
              >
                    <el-col :span="5">
                    <el-select
                        v-if="
                            submit_data.type === 'SPOT_TRADE' ||
                            submit_data.type === 'SPOT_NET_BUY'
                        "
                        clearable
                        filterable
                        multiple
                        v-model="submit_data.markets"
                        @change="clear_type_and_exclude_markets"
                        placeholder="交易对列表"
                        :disabled="start_disabled"
                    >
                        <el-option
                            v-for="item in $store.state.spot.info.market_list.concat($store.state.spot.info.pending_market_lis)"
                            :key="item"
                            :label="item"
                            :value="item"
                        >
                        </el-option>
                    </el-select>
                    <el-select
                        v-else
                        clearable
                        filterable
                        multiple
                        v-model="submit_data.markets"
                        @change="clear_type_and_exclude_markets"
                        placeholder="交易对列表"
                        :disabled="start_disabled"
                    >
                        <el-option
                            v-for="item in $store.state.perpetual.info.market_list"
                            :key="item"
                            :label="item"
                            :value="item"
                        >
                        </el-option>
                    </el-select>
                </el-col>
                <el-col :span="5">
                    <el-select
                        clearable
                        filterable
                        v-model="submit_data.market_type"
                        @change="delete submit_data.markets"
                        placeholder="市场类型"
                        :disabled="start_disabled"
                    >
                        <el-option v-if="submit_data.type !== 'SPOT_NET_BUY'"
                            key="ALL"
                            label="全部市场"
                            value="ALL"
                        >
                        </el-option>
                        <el-option v-if="
                            submit_data.type === 'PERPETUAL_TRADE' ||
                            submit_data.type === 'PERPETUAL_INCOME' ||
                            submit_data.type === 'PERPETUAL_INCOME_RATE'
                        "
                            key="DIRECT_PERPETUAL"
                            label="全部正向合约市场"
                            value="DIRECT_PERPETUAL"
                        >
                        </el-option>
                        <el-option v-if="
                            submit_data.type === 'PERPETUAL_TRADE' ||
                            submit_data.type === 'PERPETUAL_INCOME' ||
                            submit_data.type === 'PERPETUAL_INCOME_RATE'
                        "
                            key="INVERSE_PERPETUAL"
                            label="全部反向合约市场"
                            value="INVERSE_PERPETUAL"
                        >
                        </el-option>
                    </el-select>
                </el-col>
                <el-col :span="1" v-if="submit_data.market_type"> 除</el-col>
                <el-col :span="4" v-if="submit_data.market_type">
                        <el-select
                            clearable
                            filterable
                            multiple
                            v-model="submit_data.exclude_markets"
                            @change="delete submit_data.markets"
                            placeholder="交易对列表"
                            :disabled="start_disabled"
                            v-if="['SPOT_TRADE', 'SPOT_NET_BUY'].includes(submit_data.type)"
                        >
                        <el-option
                            v-for="item in $store.state.spot.info.market_list.concat($store.state.spot.info.pending_market_lis)"
                            :key="item"
                            :label="item"
                            :value="item"
                        >
                        </el-option>
                        </el-select>

                        <el-select
                        v-else
                        clearable
                        filterable
                        multiple
                        v-model="submit_data.exclude_markets"
                        @change="delete submit_data.markets"
                        placeholder="交易对列表"
                        :disabled="start_disabled"
                    >
                        <el-option
                            v-for="item in $store.state.perpetual.info.market_list"
                            :key="item"
                            :label="item"
                            :value="item"
                        >
                        </el-option>
                    </el-select>

                </el-col>
                <el-col :span="1"  v-if="submit_data.market_type">以外</el-col>
                </el-row>
                </el-form-item>

                <el-form-item label="开始时间" required>
                    <el-date-picker
                        :disabled="edit_disabled"
                        v-model="submit_data.start_time"
                        type="datetime"

                        placeholder="选择日期"
                    >
                    </el-date-picker>
                    <el-row>
                        <span>UTC时间：{{ $formatUTCDate(submit_data.start_time, 'YYYY-MM-DD HH:mm') }}</span>
                    </el-row>
                </el-form-item>

                <el-form-item label="结束时间" required>
                    <el-date-picker
                        :disabled="edit_disabled"
                        v-model="submit_data.end_time"
                        type="datetime"
                        placeholder="选择日期"
                    >
                    </el-date-picker>
                    <el-row>
                        <span>UTC时间：{{ $formatUTCDate(submit_data.end_time, 'YYYY-MM-DD HH:mm') }}</span>
                    </el-row>
                </el-form-item>

                <el-form-item label="公告链接">
                  <el-input
                    v-model="submit_data.announce_url"
                    :disabled="edit_disabled"
                  ></el-input>
                </el-form-item>

                <el-form-item label="最小交易量限制">
                    <el-input
                        type="number"
                        v-model="submit_data.least_trade_amount"
                        :disabled="edit_disabled"
                    ></el-input>
                </el-form-item>

                <el-form-item label="奖励币种" required>
                    <el-select
                        clearable
                        filterable
                        v-model="submit_data.gift_asset"
                        placeholder="币种名称"
                        :disabled="edit_disabled"
                    >
                        <el-option
                            v-for="item in $store.state.spot.info.asset_list"
                            :key="item"
                            :label="item"
                            :value="item"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="经费来源" required>
                    <el-select
                        clearable
                        filterable
                        v-model="submit_data.funding_source"
                        placeholder="请选择"
                        :disabled="edit_disabled"
                    >
                        <el-option
                            v-for="(value, key) in funding_sources"
                            :key="key"
                            :label="value"
                            :value="key"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="奖励总量" required>
                    <el-input
                        type="number"
                        v-model="submit_data.gift_amount"
                        :disabled="edit_disabled"
                    ></el-input>
                </el-form-item>

                <el-form-item label="奖励规则" required>
                    <el-row>第一部分：排名奖励</el-row>
                    <el-form-item>
                        <el-button
                            type="primary"
                            @click="add_rank_config"
                            circle
                            :disabled="edit_disabled"
                            icon="el-icon-plus"
                        ></el-button>
                    </el-form-item>
                    <template v-for="(item, index) in submit_data.gift_rules">
                        <el-row
                            :gutter="0"
                            type="flex"
                            :key="index"
                            style="text-align: center; margin-bottom: 22px"
                        >
                            <el-col :span="1"> 第 </el-col>
                            <el-col :span="3">
                                <el-form-item required>
                                    <el-input
                                        type="number"
                                        v-model="item.rank_min"
                                        :disabled="edit_disabled"
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="1"> 到 </el-col>
                            <el-col :span="3">
                                <el-form-item required>
                                    <el-input
                                        type="number"
                                        v-model="item.rank_max"
                                        :disabled="edit_disabled"
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="2"> 名送固定 </el-col>
                            <el-col :span="4">
                                <el-form-item required>
                                    <el-input
                                        type="number"
                                        v-model="item.rank_amount"
                                        :disabled="edit_disabled"
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="1"> 个 </el-col>
                            <el-col :span="1">
                                <el-button
                                    type="danger"
                                    @click="remove_rank_config(index)"
                                    circle
                                    :disabled="edit_disabled"
                                    icon="el-icon-minus"
                                ></el-button>
                            </el-col>
                        </el-row>
                    </template>

                    <el-row
                        >第二部分：按交易量瓜分奖励（如果是净买入量活动则按净买入量瓜分）
                        <el-input
                            disabled
                            placeholder="单用户获得奖励=该用户交易量/参与了排名但未领取到排名奖励用户的累计交易量*（总奖励-排名奖励）"
                        ></el-input>
                    </el-row>
                    <el-row>
                        <el-form-item label="单个用户瓜分奖励上限">
                            <el-input
                                :disabled="edit_disabled"
                                type="number"
                                v-model="submit_data.max_split_reward_amount"
                            ></el-input>
                        </el-form-item>
                    </el-row>
                </el-form-item>

                <el-form-item label="活动标题" required>
                    <el-tabs v-model="cur_lang" type="card">
                        <template v-for="(v, k) in languages">
                            <el-tab-pane
                                :key="k"
                                :label="v"
                                :value="k"
                                :name="k"
                            >
                                <el-input
                                    type="textarea"
                                    :autosize="{ minRows: 5, maxRows: 10 }"
                                    show-word-limit
                                    maxlength="256"
                                    v-model="submit_data.titles[k]"
                                ></el-input>
                            </el-tab-pane>
                        </template>
                    </el-tabs>
                </el-form-item>

                <el-form-item>
                    <el-row :gutter="1">
                    <el-col :span="3">
                    <el-upload
                    ref="upload"
                    :action="`/api/operation/activities/trade/title/template-upload`"
                    name="batch-upload"
                    :show-file-list="false"
                    :headers="{ AUTHORIZATION: $cookies.get('admin_token') }"
                    :on-success="importSuccess"
                    :on-error="importError"
                    accept=".xlsx"
                    >
                    <el-button type="primary">上传</el-button>
                    </el-upload>
                    </el-col>
                    <el-col :span="4">
                    <el-button type="primary"
                            @click="handleDownloadTemplate">下载模板</el-button>
                    </el-col>
                </el-row>
                </el-form-item>

                <el-form-item label="活动图片" required>
                    <el-upload
                        class="img-uploader"
                        :show-file-list="false"
                        action="/api/upload/image"
                        name="img"
                        :headers="{
                            AUTHORIZATION: $cookies.get('admin_token'),
                        }"
                        accept=".jpeg, .png"
                        :on-success="
                            (res) => {
                                image_url = res.data.file_url;
                                submit_data.cover = res.data.file_key;
                            }
                        "
                    >
                        <span v-if="image_url">
                            <img
                                :src="image_url"
                                class="img"
                                alt=""
                                width="100%"
                            />
                        </span>
                        <div v-else>
                            <i class="el-icon-plus img-uploader-icon"></i>
                            <div slot="tip" class="el-upload__tip">
                                只能上传jpeg/png文件，且大小不超过3Mb
                            </div>
                        </div>
                    </el-upload>
                </el-form-item>
                <el-form-item label="活动条件" prop="user_group_condition">
                    <UserGroupFilter
                        :filters="submit_data.user_group_condition"
                        :disabled="edit_disabled"
                        :allowed_keys="[
                            'KYC',
                            'VIP_LEVEL',
                            'MARKET_MAKER',
                            'NO_DEAL',
                        ]"
                        ref="user_filter"
                    ></UserGroupFilter>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submit">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import UserGroupFilter from "~/components/UserGroupFilter";

export default {
    components: {
        UserGroupFilter,
    },
    methods: {
        handleDownloadTemplate() {
            let url = "/api/operation/activities/trade/title/template";
            this.$download_from_url(url, 'trade-activities-template.xlsx')
        },
        importError(err, file, fileList) {
            this.$alert("上传失败", "错误")
        },
        importSuccess(response, file, fileList) {
            if (response.code !== 0) {
            this.$alert(response.message, '错误'
            ).then(() => {
            }).catch(() => {
            });
            return;
            }
            this.$alert('已成功上传，请检查翻译', '成功');

            let tans_data = {}

            response.data.forEach((e) => {
            if (e.title){
                tans_data[e.lang] = e.title.toString()
                }

            })
            let en_title = tans_data['EN_US']
            if (!en_title) {
                this.$alert('上传文件中缺少英文标题', '上传文件中缺少英文标题')
                return false;
            }

            for (let lang in this.languages) {
                if (!tans_data[lang]) {
                    tans_data[lang] = en_title
                }
            }
            this.submit_data.titles = tans_data;
        },
        handleSuspend(row) {
            this.$confirm("确定暂停?").then(() => {
                this.handleEditConfirm(row.id, { status: "PENDING" }, "patch");
            });
        },
        handleReopen(row) {
            this.$confirm("确定上架?").then(() => {
                this.handleEditConfirm(row.id, { status: "ONLINE" }, "patch");
            });
        },

        handleDelete(row) {
            this.$confirm("确定下架?").then(() => {
                this.handleEditConfirm(row.id, { status: "DELETED" }, "patch");
            });

        },
        handleCreate() {
            this.action = this.DIALOG_CREATION;
            this.image_url = "",
            this.submit_data = _.clone(this.create_data);
            this.show = true;
        },
        handleEdit(row) {
            this.action = this.DIALOG_EDIT;
            this.submit_data = _.clone(row);
            this.submit_data.start_time = this.submit_data.start_time * 1000;
            this.submit_data.end_time = this.submit_data.end_time * 1000;
            this.image_url = this.submit_data.cover_url;
            this.show = true;
        },
        get_data() {
            this.loading = true;
            this.$axios
                .get("/api/operation/activities/trade/list", {
                    params: this.search_data,
                })
                .then((res) => {
                    if (res.data.code === 0) {
                        let data = res.data.data;
                        let records = data.items;
                        this.status_map = data.statuses;
                        this.states = data.states;
                        this.types = data.types;
                        this.market_types = data.market_types;
                        this.languages = data.langs;
                        this.funding_sources = data.funding_sources;
                        this.items = records.data;
                        this.page_count = records.total_page;
                        this.total = records.total;
                        this.loading = false;
                    } else {
                        this.$message.error(
                            `code: ${res.data?.code}; message: ${res.data?.message}`
                        );
                    }
                })
                .catch((err) => {
                    this.$message.error(`刷新失败! (${err})`);
                });
        },
        submit() {
            this.$refs["submit_data"].validate((valid) => {
                if (!valid) {
                    this.$alert("校验失败请修改", "校验失败请修改", {
                        confirmButtonText: "确定",
                    });
                } else {

                      let required_langs = [
                          "EN_US",
                          "ZH_HANS_CN",
                          "ZH_HANT_HK",
                      ];
                      if (
                          !required_langs.every((v) =>
                              this.submit_data.titles[v]
                          )
                      ) {
                          this.$message.error(
                              "需填写简体中文、繁体中文、英文标题"
                          );
                          return;
                      }
                      if (!this.submit_data.gift_rules || this.submit_data.gift_rules.length == 0) {
                        this.$message.error(
                              "需配置奖励规则"
                          );
                        return;
                      }
                      this.submit_data.gift_rules.forEach((v) => {
                        v['rank_min'] = Number(v['rank_min']);
                        v['rank_max'] = Number(v['rank_max']);
                      });
                      if (!this.submit_data.markets || this.submit_data.markets.length == 0) {
                        if (this.submit_data.type == 'SPOT_NET_BUY') {
                            this.$message.error(
                                "该活动需配置市场"
                            );
                            return;
                        }
                        // ALl Markets
                        this.submit_data.markets = null;
                      }
                      if (!this.submit_data.max_split_reward_amount) {
                        delete this.submit_data.max_split_reward_amount;
                      }
                    if (this.action === this.DIALOG_CREATION) {
                        this.handleCreateConfirm();
                    } else {
                        this.handleEditConfirm(
                            this.submit_data.id,
                            this.submit_data,
                            "put"
                        );
                    }
                }
            });
        },
        handleCreateConfirm() {
            this.loading = true;
            try {
                this.$refs.user_filter.parse_filters();
            } catch (e) {
                this.$message.error(e.message);
                return;
            }
            this.submit_data.user_group_condition = JSON.stringify(
                this.$refs.user_filter.parse_filters()
            );
            let data = _.cloneDeep(this.submit_data);
            let start_time = data.start_time;
            let end_time = data.end_time;
            data.start_time = start_time.getTime();
            data.end_time = end_time.getTime();

            if (this.action === this.DIALOG_CREATION) {
                this.$axios
                    .post("/api/operation/activities/trade/list", data)
                    .then((res) => {
                        if (res.data.code === 0) {
                            this.$message.success("创建成功！");
                            this.loading = false;
                            this.show = false;
                            this.get_data();
                        } else {
                            this.$message.error(
                                `code: ${res.data?.code}; message: ${res.data?.message}`
                            );
                        }
                    })
                    .catch((err) => {
                        this.$message.error(`创建失败! (${err})`);
                    });
            } else {
                this.handleEditConfirm(data.id, data, "put");
            }
        },
        handleEditConfirm(id, edit_data, method) {
            let start_time = new Date(edit_data.start_time);
            let end_time = new Date(edit_data.end_time);
            edit_data.start_time = start_time.getTime();
            edit_data.end_time = end_time.getTime();
            if (method == "put") {
                try {
                    this.$refs.user_filter.parse_filters();
                } catch (e) {
                    this.$message.error(e.message);
                    return;
                }
                edit_data.user_group_condition = JSON.stringify(
                    this.$refs.user_filter.parse_filters()
                );
            }
            this.$axios[method](
                `/api/operation/activities/trade/${id}`,
                edit_data
            )
                .then((res) => {
                    this.get_data();
                    if (res.data.code === 0) {
                        this.$message.success("提交成功!");
                        this.loading = false;
                        this.show = false;
                        this.get_data();
                    } else {
                        this.$message.error(
                            `code: ${res.data?.code}; message: ${res.data?.message}`
                        );
                    }
                })
                .catch((err) => {
                    this.$message.error(`失败! (${err})`);
                });
        },
        add_rank_config() {
            this.submit_data.gift_rules.push(
                _.clone(this.type_config_map["RANK"][0])
            );
        },
        remove_rank_config(index) {
            this.submit_data.gift_rules.splice(index, 1);
        },
        clear_markets_and_type() {
            this.submit_data.markets=[];
            this.submit_data.exclude_markets=[];
            this.submit_data.market_type=null;
        },
        clear_type_and_exclude_markets() {
            this.submit_data.exclude_markets=[];
            this.submit_data.market_type=null;
        }
    },
    mounted() {
        this.$set(this, "cur_lang", "EN_US");
        this.get_data();
    },
    data() {
        return {
            search_data: {
                page: 1,
                limit: 100,
            },
            type_config_map: {
                RANK: [
                    {
                        rank_min: null,
                        rank_max: null,
                        rank_amount: null,
                    },
                ],
            },
            submit_data: null,
            create_data: {
                markets: [],
                gift_asset: null,
                type: "SPOT_TRADE",
                market_type: null,
                least_trade_amount: null,
                announce_url: '',
                max_split_reward_amount: null,
                gift_amount: null,
                start_time: null,
                end_time: null,
                titles: {},
                gift_rules: [],
                cover: null,
                funding_source: null,
                exclude_markets: [],
                cover_url: null,
            },
            status_map: {},
            states: {},
            DIALOG_CREATION: "CREATION",
            DIALOG_EDIT: "EDIT",
            cur_lang: "",
            languages: {},
            action: "",
            show: false,
            items: [],
            types: {},
            market_types: {},
            funding_sources: {},
            image_url: "",
            loading: true,
            page_count: 0,
            total: 0,
        };
    },
    computed: {
        edit_disabled() {
            return !(
                this.submit_data.status === "PENDING" ||
                this.action === this.DIALOG_CREATION
            );
        },
        start_disabled() {
            return !(
                this.submit_data.status === "PENDING" ||
                this.action === this.DIALOG_CREATION ||
                (this.action === this.DIALOG_EDIT && this.submit_data.start_time > new Date())
            );
        },
    },
};
</script>
