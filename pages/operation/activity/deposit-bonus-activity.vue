<template>
  <el-container>
    <el-main>
      <h2 style="font-family: 'Microsoft YaHei',<PERSON>l,sans-serif;">
        充值福利
      </h2>

      <el-form :inline="true" :model="filters">
        <el-form-item label="活动标题">
          <el-input v-model="filters.name" clearable @change="get_data"></el-input>
        </el-form-item>

        <el-form-item label="进行状态" style="margin-left: 20px;">
          <el-select clearable filterable v-model="filters.active_status" @change="get_data" placeholder="<ALL>"
            style="width: 120px">
            <el-option v-for="(active_status_name, key) in active_statuses" :key="key" :label="active_status_name"
              :value="key">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="活动状态" style="margin-left: 20px;">
          <el-select clearable filterable v-model="filters.status" @change="get_data" placeholder="<ALL>"
            style="width: 120px">
            <el-option v-for="(status_name, key) in statuses" :key="key" :label="status_name" :value="key"
              v-if="key !== 'DELETED'">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item style="margin-left: 40px;">
          <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
            <el-button icon="el-icon-refresh-left" circle @click="handle_page_refresh"></el-button>
          </el-tooltip>
        </el-form-item>

        <el-form-item style="margin-left: 40px;">
          <el-link href="/operation/activity/deposit-bonus-activity-detail?id=0" target="_blank" :underline="false">
            <el-tooltip content="添加" placement="right" :open-delay="500" :hide-after="2000">
              <el-button type="primary" icon="el-icon-plus" circle></el-button>

            </el-tooltip>
          </el-link>
        </el-form-item>
      </el-form>

      <el-table :data="items" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID">
          <template slot-scope="scope">
            <el-tooltip :content="'activity_id: ' + scope.row.activity_id" placement="top">
              <span>{{ scope.row.id }}</span>
            </el-tooltip>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="活动标题" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-link :href="`/operation/activity/deposit-bonus-users?activity_id=${scope.row.id}`" type="primary"
              target="_blank" :underline="false" style="
                            width: 100%;
                            font-weight: normal;
                            display: inline-block;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                        ">
              {{ scope.row.name }}
            </el-link>
          </template>
        </el-table-column>

        <el-table-column prop="active_status" label="进行状态" :formatter="row => active_statuses[row.active_status]">
        </el-table-column>

        <el-table-column prop="status" label="活动状态" :formatter="row => statuses[row.status]"></el-table-column>

        <el-table-column label="活动方式" :formatter="row => formatModes(row)"></el-table-column>

        <el-table-column prop="asset" label="充值币种"></el-table-column>

        <el-table-column prop="threshold" label="充值数量"></el-table-column>

        <el-table-column prop="user_count" label="达标人数/报名人数">
          <template slot-scope="scope">
            <el-link :href="`/operation/activity/deposit-bonus-users?activity_id=${scope.row.id}`" type="primary"
              target="_blank" :underline="false" style="
                            width: 100%;
                            font-weight: normal;
                            display: inline-block;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                        ">
              {{ scope.row.satisfied_user_count }} / {{ scope.row.user_count }}
            </el-link>
          </template>
        </el-table-column>

        <el-table-column prop="new_user_count" label="报名新用户"></el-table-column>

        <el-table-column label="奖励币种" :formatter="row => formatGiftAssets(row)"></el-table-column>

        <el-table-column label="送币余量/总数量" width="170px">
          <template slot-scope="scope">
            <div v-for="cfg in scope.row.config_items">
              <div v-for="gift_rule in cfg.gift_rules_display">
                <el-tooltip v-if="gift_rule['gift_type'] === 'ASSET'" class="item" effect="dark" :content="'剩余' +
                  gift_rule.left_amount +
                  gift_rule.gift_asset
                  " placement="bottom-end">
                  <el-link :href="'/operation/activity/gift-history?activity_id=' +
                    cfg.activity_id +
                    '&asset=' +
                    gift_rule.gift_asset
                    " type="primary" target="_blank" :underline="false" style="
                                width: 100%;
                                font-weight: normal;
                                display: inline-block;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                white-space: nowrap;
                            ">
                    {{ gift_rule.left_amount }} / {{ gift_rule.rank_total }}
                  </el-link>
                </el-tooltip>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="奖励卡券" :formatter="row => formatGiftCoupons(row)"></el-table-column>

        <el-table-column label="卡券余量/总数量">
          <template slot-scope="scope">
            <div v-for="cfg in scope.row.config_items">
              <div v-for="gift_rule in cfg.gift_rules_display">
                <el-tooltip v-if="gift_rule['gift_type'] === 'COUPON'" class="item" effect="dark" :content="'剩余' +
                  gift_rule.left_amount
                  " placement="bottom-end">
                  <el-link :href="'/operation/activity/deposit-bonus-users?activity_id=' +
                    scope.row.id
                    " type="primary" target="_blank" :underline="false" style="
                                width: 100%;
                                font-weight: normal;
                                display: inline-block;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                white-space: nowrap;
                            ">
                    {{ gift_rule.left_amount }} / {{ gift_rule.rank_total }}
                  </el-link>
                </el-tooltip>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="奖励权益ID">
          <template slot-scope="scope">
            <div v-for="cfg in scope.row.config_items">
              <div v-for="gift_rule in cfg.gift_rules">
                <span v-if="gift_rule.gift_type === 'EQUITY'">
                  <el-link :href="'/operation/equity-center/cashback/equity-list?equity_id=' +
                    gift_rule.equity_id
                    " type="primary" target="_blank" :underline="false" style="
                                width: 100%;
                                font-weight: normal;
                                display: inline-block;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                white-space: nowrap;
                            ">
                    {{ gift_rule.equity_id }}
                  </el-link>
                </span>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="权益余量/权益总数">
          <template slot-scope="scope">
            <div v-for="cfg in scope.row.config_items">
              <div v-for="gift_rule in cfg.gift_rules_display">
                <el-tooltip v-if="gift_rule['gift_type'] === 'EQUITY'" class="item" effect="dark" :content="'剩余' +
                  gift_rule.left_amount
                  " placement="bottom-end">
                  <el-link :href="'/operation/equity-center/cashback/user-equity-list?business_type=DEPOSIT_BONUS_ACTIVITY&equity_id=' +
                    gift_rule.equity_id
                    " type="primary" target="_blank" :underline="false" style="
                                width: 100%;
                                font-weight: normal;
                                display: inline-block;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                white-space: nowrap;
                            ">
                    {{ gift_rule.left_amount }} / {{ gift_rule.rank_total }}
                  </el-link>
                </el-tooltip>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="start_time" label="开始时间" :formatter="row => $formatDate(row.start_time)">
        </el-table-column>

        <el-table-column prop="end_time" label="结束时间" :formatter="row => $formatDate(row.end_time)"></el-table-column>

        <el-table-column prop="audit_remark" label="备注" show-overflow-tooltip></el-table-column>

        <el-table-column label="操作" width="180">
          <template slot-scope="scope">
            <span style="margin-left: 10px;">
              <el-link :href="'/operation/activity/deposit-bonus-activity-detail?id=' + scope.row.id" target="_blank"
                :underline="false">
                <el-tooltip content="编辑">
                  <el-button size="mini" type="primary" icon="el-icon-edit" circle></el-button>
                </el-tooltip>
              </el-link>
            </span>
            <el-button size="small" @click="handleOnOffline(scope.row, true)" type="success"
              v-if="scope.row.status === 'PENDING'">上架
            </el-button>

            <el-button size="mini" @click="handleOnOffline(scope.row, false)" type="danger"
              v-if="scope.row.status === 'ONLINE'">下架
            </el-button>

            <el-button size="mini" @click="handleDelete(scope.row)" type="danger"
              v-if="['DRAFT', 'CREATED', 'REJECTED'].includes(scope.row.status)">删除
            </el-button>

            <el-button size="mini" @click="handleCreateAudit(scope.row)" type="warning"
              v-if="scope.row.status === 'DRAFT'">提交审核
            </el-button>

            <el-button size="mini" @click="handleAuditClick(scope.row)" type="success"
              v-if="scope.row.status === 'CREATED'">审核
            </el-button>

          </template>
        </el-table-column>
      </el-table>

      <el-pagination :current-page.sync="filters.page" :page-size.sync="filters.limit"
        @size-change="handle_limit_change" @current-change="handle_page_change" :page-sizes="[50, 25]"
        :hide-on-single-page="true" layout="total, sizes, prev, pager, next, jumper" :total="total">
      </el-pagination>

      <el-backtop></el-backtop>

      <!-- 二次确认对话框 -->
      <el-dialog title="确认提交审核?" :visible.sync="confirmDialog.visible" width="30%" :close-on-click-modal="false"
        :close-on-press-escape="false">
        <span>确认要提交审核吗？</span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="confirmDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirmAudit">确定</el-button>
        </span>
      </el-dialog>

      <!-- 审核处理对话框 -->
      <el-dialog title="审核处理" :visible.sync="auditDialog.visible" width="40%" :close-on-click-modal="false"
        :close-on-press-escape="false">
        <el-form :model="auditDialog.form" ref="auditForm" label-width="100px">
          <el-form-item label="审核结果:" required>
            <el-radio-group v-model="auditDialog.form.status">
              <el-radio label="PENDING">通过</el-radio>
              <el-radio label="REJECTED">拒绝</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="审核备注">
            <el-input v-model="auditDialog.form.remark" type="textarea" :rows="4" placeholder="备注" maxlength="100"
              show-word-limit>
            </el-input>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="auditDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmitAudit">确定</el-button>
        </span>
      </el-dialog>

      <!-- WebAuthn组件 -->
      <UserWebAuthn ref="UserWebAuthn" :operation_type="operation_type"></UserWebAuthn>
    </el-main>
  </el-container>
</template>

<script>
import moment from 'moment';

const DIALOG_CREATION = 'creation';
const DIALOG_EDIT = 'edit';
const base_url = '/api/activity/deposit-bonus/activity';
import UserSearch from "@/components/user/UserSearch";
import UserWebAuthn from "@/components/UserWebAuthn";

export default {
  components: { UserSearch, UserWebAuthn },
  methods: {
    notice(title, message) {
      this.$alert(message, title, {
        confirmButtonText: '确定',
      });
    },
    reset_page() {
      this.filters.page = 1;
    },
    res_success_notice(res) {
      let title = '提交成功';
      this.notice(title, title);
    },
    res_fail_notice(res) {
      let title = '提交失败';
      let message = `(code: ${res.data?.code}; message: ${res.data?.message}; data: ${res.data?.data})`;
      this.notice(title, message);
    },
    res_error_notice(error) {
      let title = '提交失败';
      let message = `(code: ${error.response.status}; message: ${error.message}; data: ${res.data?.data})`;
      this.notice(title, message);
    },
    handle_limit_change() {
      this.reset_page();
      this.get_data();
    },
    handle_page_change() {
      this.get_data();
    },
    handle_page_refresh() {
      this.reset_page();
      this.get_data();
    },
    get_data() {
      this.loading = true;
      this.$axios.get(base_url, { params: this.filters }).then(res => {
        this.loading = false;
        if (res?.data?.code === 0) {
          let data = res.data.data;
          this.items = data.items;
          this.total = data.total;
          let extra = data.extra;
          this.active_statuses = extra.active_statuses;
          this.statuses = extra.statuses;
          this.modes = extra.modes;
          this.flags = extra.flags;
          this.gift_types = extra.gift_types;
          this.coupon_details = extra.coupon_details;
          this.coupon_types = extra.coupon_types;
        } else {
          this.items = [];
          this.total = 0;
          this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
        }
      });
    },
    formatModes(row) {
      let modes = [];
      row.config_items.forEach(e => {
        modes.push(this.modes[e.mode]);
      })
      const uniqueArr = [...new Set(modes)];
      return uniqueArr.join('\n')
    },
    formatGiftAssets(row) {
      let assets = [];
      row.config_items.forEach(e => {
        e.gift_rules.forEach(ee => {
          if (ee.gift_type === 'ASSET') {
            assets.push(ee.gift_asset)
          }
        });
      })
      const uniqueArr = [...new Set(assets)];
      return uniqueArr.join('\n') ? uniqueArr.join('\n') : '-'
    },
    formatGiftCoupons(row) {
      let coupons = [];
      row.config_items.forEach(e => {
        e.gift_rules.forEach(ee => {
          if (ee.gift_type === 'COUPON') {
            coupons.push(this.coupon_types[this.coupon_details[ee.coupon_apply_id].coupon_type])
          }
        });
      })
      const uniqueArr = [...new Set(coupons)];
      return uniqueArr.join('\n') ? uniqueArr.join('\n') : '-'
    },
    handleOnOffline(row, is_online) {
      let new_status = is_online ? 'ONLINE' : 'OFFLINE';
      this.$confirm(is_online ? '确认上架？' : '确认下架？')
        .then(_ => {
          this.$axios
            .patch(base_url, { id: row.id, status: new_status })
            .then(res => {
              if (res?.data?.code === 0) {
                this.res_success_notice(res);
                this.get_data();
              } else {
                this.res_fail_notice(res);
              }
            })
            .catch(e => {
              this.res_error_notice(e);
            });
        })
        .catch(_ => {
        });
    },
    handleDelete(row) {
      this.$confirm(`确认删除 ${row.id} ${row.name}？`)
        .then(_ => {
          this.$axios
            .patch(base_url, { id: row.id, status: 'DELETED' })
            .then(res => {
              if (res?.data?.code === 0) {
                this.res_success_notice(res);
                this.get_data();
              } else {
                this.res_fail_notice(res);
              }
            })
            .catch(e => {
              this.res_error_notice(e);
            });
        })
        .catch(_ => {
        });
    },

    handleCreateAudit(row) {
      this.$confirm(`确认提交审核 ${row.id} ${row.name}？`)
        .then(_ => {
          this.$axios
            .patch(base_url, { id: row.id, status: 'CREATED' })
            .then(res => {
              if (res?.data?.code === 0) {
                this.res_success_notice(res);
                this.get_data();
              } else {
                this.res_fail_notice(res);
              }
            })
            .catch(e => {
              this.res_error_notice(e);
            });
        })
        .catch(_ => {
        });
    },

    // 修改审核按钮点击事件
    handleAuditClick(row) {
      this.currentAuditRow = row;
      this.confirmDialog.visible = true;
    },

    // 二次确认后显示审核表单
    handleConfirmAudit() {
      this.confirmDialog.visible = false;
      this.auditDialog.visible = true;
      // 重置表单数据
      this.auditDialog.form = {
        status: 'PENDING',
        remark: ''
      };
    },

    // 提交审核
    handleSubmitAudit() {
      if (!this.auditDialog.form.status) {
        this.$message.error('请选择审核结果');
        return;
      }

      // 关闭审核表单
      this.auditDialog.visible = false;

      // 执行WebAuthn验证和审核
      this.handleWebAuthn(() => {
        this.handleAudited(this.currentAuditRow);
      });
    },

    async handleWebAuthn(success_callback) {
      const UserWebAuthn = this.$refs["UserWebAuthn"];
      await UserWebAuthn.run();
      await UserWebAuthn.handleWebAuthn().then(() => {
        if (UserWebAuthn.success) {
          this.headers["WebAuthn-Token"] = UserWebAuthn.webauthn_token;
          if (success_callback) {
            success_callback();
          }
          return true;
        } else {
          this.$message.error("WebAuthn校验失败!");
          return false;
        }
      }).catch(err => {
        this.$message.error(`WebAuthn校验失败! ${err}`);
        return false;
      });
    },

    handleAudited(row) {
      let url = base_url + `/${row.id}/audit`;
      let auditData = {
        status: this.auditDialog.form.status,
        remark: this.auditDialog.form.remark
      };

      this.$axios
        .post(url, auditData, { headers: this.headers })
        .then((res) => {
          if (res.data.code === 0) {
            this.get_data();
            this.$message.success("审核成功！");
            // 重置当前审核行
            this.currentAuditRow = null;
          } else {
            this.$message.error(
              `code: ${res.data.code}; message: ${res.data.message}`
            );
          }
        })
        .catch((err) => {
          this.$message.error(`审核失败! (${err})`);
        });
    },

  },
  created() {
    this.DIALOG_CREATION = DIALOG_CREATION;
    this.DIALOG_EDIT = DIALOG_EDIT;

    let query_types = {
      airdrop_mode: String,
      asset: String,
    };
    let url_query = this.$route.query;
    Object.keys(url_query).forEach(key => {
      if (key in this.filters) {
        let value = url_query[key];
        if (key === 'page' || key === 'limit') {
          value = Number(value);
        }
        if (key in query_types) {
          value = query_types[key](value);
        }
        this.filters[key] = value;
      }
    });
    this.$watch('filters', {
      handler: function () {
        let query = {};
        Object.assign(query, this.filters);
        Object.keys(query).forEach(key => !query[key] && delete query[key]);
        this.$router.replace({ query: query });
      },
      deep: true,
    });
  },
  mounted() {
    this.filters.id = this.$route.query.id;
    this.get_data();
  },
  data() {
    return {
      filters: {
        id: null,
        page: 1,
        limit: 50,
      },
      items: [],
      total: 0,
      active_statuses: {},
      statuses: {},
      modes: {},
      flags: {},
      gift_types: {},
      coupon_details: {},
      coupon_types: {},
      loading: true,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now();
        },
      },
      // 添加新的数据属性
      currentAuditRow: null,
      operation_type: "DEPOSIT_BONUS_AUDIT",
      confirmDialog: {
        visible: false
      },
      auditDialog: {
        visible: false,
        form: {
          status: 'PENDING',
          remark: ''
        }
      },
    };
  },
  computed: {
    headers() {
      return {
        'AUTHORIZATION': this.$cookies.get('admin_token'),
        'WebAuthn-Token': this.$refs["UserWebAuthn"] !== undefined ? this.$refs["UserWebAuthn"].webauthn_token : "",
        'Operate-Type': this.operation_type,
      }
    },
  }
};
</script>

<style>
.img-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.img-uploader .el-upload:hover {
  border-color: #409eff;
}

.img-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.el-table {
  .cell {
    white-space: pre-line;
  }
}

.hide .el-upload--picture-card {
  display: none;
}
</style>
