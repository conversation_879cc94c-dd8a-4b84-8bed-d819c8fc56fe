<template>
  <div class="table-data" v-loading="loading">
    <el-row type="flex" justify="space-between" align="middle">
      <el-col :span="22">
        <h2 style="font-family: 'Microsoft YaHei', Arial, sans-serif">
          充值福利活动配置
        </h2>
      </el-col>
    </el-row>
    <el-collapse v-model="activeNames">
      <el-form :model="form_data" ref="form_data" label-width="200px">
        <el-collapse-item name="11" style="margin-bottom: 20px; margin-top: 10px">
          <template slot="title">
            <h2>基本信息</h2>
          </template>

          <el-form-item prop="start_time" label="开始时间" required>
            <el-date-picker v-model="form_data.start_time" :disabled="disable_edit" type="datetime"
                            format="yyyy-MM-dd HH:mm:ss" value-format="timestamp" :picker-options="pickerOptions"
                            placeholder="只支持日期开始">
            </el-date-picker>
            <el-row>
              <span>UTC时间：{{ $formatUTCDate(form_data.start_time) }}</span>
            </el-row>
          </el-form-item>

          <el-form-item prop="end_time" label="结束时间" required>
            <el-date-picker v-model="form_data.end_time" :disabled="disable_edit" type="datetime"
                            format="yyyy-MM-dd HH:mm:ss" value-format="timestamp" :picker-options="pickerOptions"
                            placeholder="只支持日期结束">
            </el-date-picker>
            <el-row>
              <span>UTC时间：{{ $formatUTCDate(form_data.end_time) }}</span>
            </el-row>
          </el-form-item>

          <el-form-item label="活动图片" required>
            <el-upload
              class="img-uploader"
              :show-file-list="false"
              action="/api/upload/image"
              name="img"
              :headers="{
                            AUTHORIZATION: $cookies.get('admin_token'),
                        }"
              accept=".jpeg, .png"
              :on-success=handleUpload>
                        <span v-if="form_data.cover_url">
                            <img
                              :src="form_data.cover_url"
                              class="img"
                              alt=""
                              width="100%"
                            />
                        </span>
              <div v-else>
                <i class="el-icon-plus img-uploader-icon"></i>
                <div slot="tip" class="el-upload__tip">
                  图片尺寸支持上传 458 * 224 或 916 * 448
                </div>
                <div slot="tip" class="el-upload__tip">
                  只能上传jpeg/png文件，且大小不超过5Mb
                </div>
              </div>
            </el-upload>
          </el-form-item>

          <el-form-item label="公告链接" prop="zendesk_url">
            <el-input style="width:450px" v-model="form_data.zendesk_url" :disabled="disable_edit_base">
            </el-input>
          </el-form-item>
        </el-collapse-item>

        <el-collapse-item name="12" style="margin-bottom: 20px; margin-top: 20px">
          <template slot="title">
            <h2>活动信息</h2>
          </template>
          <el-form v-model="form_data" :disabled="disable_edit" label-width="200px">
            <el-row>
              <el-col :span="3" class="pull-right">
                <el-button
                  type="primary"
                  @click="addItem(form_data)"
                  icon="el-icon-plus">
                  增加活动
                </el-button>
              </el-col>
            </el-row>
            <div v-for="(cfg, index) in form_data.configs">
              <el-divider v-if="index > 0"></el-divider>
              活动配置{{ index + 1 }}
              <el-form-item label="活动条件">
                <el-dropdown @command="handle_filter_creation">
                  <el-button plain>新增筛选条件<i class="el-icon-arrow-down el-icon--right"></i>
                  </el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item v-for="(v, k) in condition_keys" :command="{key: k, index: index}" :key="k">
                      {{ v }}
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
                <div class="row-item" v-for="(condition, cIdx) in cfg.select_conditions" :key="condition">
                  <el-form-item prop="KYC" label="KYC条件" v-if="condition == 'KYC'">
                    <el-radio-group v-model="cfg.KYC" :disabled="disable_edit" @input="forceUpdate(cfg, index)">
                      <template>
                        <el-radio v-for="(key, value) in kyc_condition_type_dict" :key="key" :label="key">{{
                            value
                          }}
                        </el-radio>
                      </template>
                    </el-radio-group>
                    <el-button type="danger" icon="el-icon-delete" circle size="mini" style="margin-left: 10px;"
                               @click="handle_deletion_condition(cfg, index, condition, cIdx)"
                               :disabled="disable_edit"></el-button>
                  </el-form-item>

                  <el-form-item prop="VIP" label="VIP条件" v-if="condition == 'VIP'">
                    <el-radio-group v-model="cfg.VIP" :disabled="disable_edit" @input="forceUpdate(cfg, index)">
                      <template>
                        <el-radio v-for="(key, value) in vip_condition_type_dict" :key="key" :label="key">{{
                            value
                          }}
                        </el-radio>
                      </template>
                    </el-radio-group>
                    <el-button type="danger" icon="el-icon-delete" circle size="mini" style="margin-left: 10px;"
                               @click="handle_deletion_condition(cfg, index, condition, cIdx)"></el-button>
                  </el-form-item>

                  <el-form-item label="是否持有币种" prop="NEED_HOLDING" v-if="condition == 'HOLDING'">
                    <el-select filterable v-model="cfg.NEED_HOLDING" placeholder="请选择持有是否持有币种币种"
                               @change="forceUpdate(cfg, index)">
                      <el-option v-for="name in holding_list" :key="name.value" :label="name.label" :value="name.value">
                      </el-option>
                    </el-select>
                    <el-select clearable filterable v-model="cfg.ASSET_HOLDING" placeholder="请选择持有币种"
                               @change="forceUpdate(cfg, index)">
                      <el-option v-for="asset in asset_list" :key="asset" :label="asset" :value="asset"></el-option>
                    </el-select>
                    <el-button type="danger" icon="el-icon-delete" circle size="mini" style="margin-left: 10px;"
                               @click="handle_deletion_condition(cfg, index, condition, cIdx)"></el-button>
                  </el-form-item>

                  <el-form-item prop="TRADE_BUSINESS_TYPE" label="时间类型" v-if="condition == 'TRADE_VALUE'">
                    <el-radio-group v-model="cfg.TRADE_BUSINESS_TYPE" @input="forceUpdate(cfg, index)">
                      <template>
                        <el-radio v-for="(key, value) in trade_business_type_dict" :key="key" :label="key">{{
                            value
                          }}
                        </el-radio>
                      </template>
                    </el-radio-group>
                  </el-form-item>

                  <el-form-item prop="TRADE_TYPE" label="业务范围" v-if="condition == 'TRADE_VALUE'">
                    <el-checkbox-group v-model="cfg.TRADE_TYPE_RANGE" @change="forceUpdate(cfg, index)">
                      <el-checkbox v-for="(v, k) in trade_type_dict" :label="k" :key="k"
                                   @change="val => handle_type_range(cfg, k, val)">
                        {{ v }}
                      </el-checkbox>
                      <el-button type="danger" icon="el-icon-delete" circle size="mini" style="margin-left: 10px;"
                                 @click="handle_deletion_condition(cfg, index, condition, cIdx)"></el-button>
                    </el-checkbox-group>
                  </el-form-item>

                  <el-form-item label="累积交易额" v-if="condition == 'TRADE_VALUE'">
                    <el-select filterable v-model="cfg.TRADE_DAY_RANGE" @change="forceUpdate(cfg, index)">
                      <el-option v-for="name in trade_day_range_list" :key="name.value" :label="name.label"
                                 :value="name.value">
                      </el-option>
                    </el-select>
                    <el-select filterable v-model="cfg.TRADE_OP_TYPE" @change="forceUpdate(cfg, index)">
                      <el-option v-for="name in trade_value_op_list" :key="name.value" :label="name.label"
                                 :value="name.value">
                      </el-option>
                    </el-select>

                    <el-input style="width:180px" v-model="cfg.TRADE_VALUE" placeholder="金额（USD）"
                              @input="forceUpdate(cfg, index)">
                    </el-input>
                    USD

                    <el-button type="danger" icon="el-icon-delete" circle size="mini" style="margin-left: 10px;"
                               @click="handle_deletion_condition(cfg, index, condition, cIdx)"></el-button>
                  </el-form-item>

                  <el-form-item label="总资产" v-if="condition == 'BALANCE_VALUE'">
                    <el-select filterable v-model="cfg.BALANCE_OP_TYPE" @change="forceUpdate(cfg, index)">
                      <el-option v-for="name in balance_value_op_list" :key="name.value" :label="name.label"
                                 :value="name.value">
                      </el-option>
                    </el-select>
                    <el-input style="width:180px" v-model="cfg.BALANCE_VALUE" placeholder="金额（USD）"
                              @input="forceUpdate(cfg, index)">
                    </el-input>
                    USD

                    <el-button type="danger" icon="el-icon-delete" circle size="mini" style="margin-left: 10px;"
                               @click="handle_deletion_condition(cfg, index, condition, cIdx)"></el-button>
                  </el-form-item>

                  <el-form-item label="注册时间" v-if="condition == 'REGISTERED_VALUE'">
                    <el-select filterable v-model="cfg.REGISTERED_OP_TYPE" @change="forceUpdate(cfg, index)">
                      <el-option v-for="name in registered_value_op_list" :key="name.value" :label="name.label"
                                 :value="name.value">
                      </el-option>
                    </el-select>
                    <el-date-picker v-model="cfg.REGISTERED_VALUE" type="datetime" format="yyyy-MM-dd HH:mm:ss"
                                    value-format="timestamp" placeholder="注册时间"
                                    @blur="forceUpdate(cfg, index)">
                    </el-date-picker>
                    <span>UTC时间：{{ $formatUTCDate(cfg.REGISTERED_VALUE) }}</span>
                    <el-button type="danger" icon="el-icon-delete" circle size="mini" style="margin-left: 10px;"
                               @click="handle_deletion_condition(cfg, index, condition, cIdx)"></el-button>
                  </el-form-item>

                  <el-form-item label="使用过此功能" v-if="condition == 'USED_VALUE'">
                    <el-checkbox-group v-model="cfg.not_used_list" :min="1" :max="3" @change="forceUpdate(cfg, index)">
                      <el-checkbox v-for="(v, k) in not_used_dict" :label="k" :key="k">{{ v }}</el-checkbox>
                      <el-button type="danger" icon="el-icon-delete" circle size="mini" style="margin-left: 10px;"
                                 @click="handle_deletion_condition(cfg, index, condition, cIdx)"></el-button>
                    </el-checkbox-group>
                  </el-form-item>

                  <el-form-item prop="MARKET_MAKER" label="做市商条件" v-if="condition == 'MARKET_MAKER'">
                    <el-radio-group v-model="cfg.MARKET_MAKER" :disabled="disable_edit"
                                    @input="forceUpdate(cfg, index)">
                      <template>
                        <el-radio v-for="(val, key) in market_maker_dict" :key="key" :label="key" v-if="key !== 'NOT_LIMITED'">{{
                            val
                          }}
                        </el-radio>
                      </template>
                    </el-radio-group>
                    <el-button type="danger" icon="el-icon-delete" circle size="mini" style="margin-left: 10px;"
                               @click="handle_deletion_condition(cfg, index, condition, cIdx)"
                               :disabled="disable_edit"></el-button>
                  </el-form-item>

                  <el-divider v-if="cIdx === cfg.select_conditions.length - 1"></el-divider>
                </div>
              </el-form-item>

              <el-form-item label="充值币种" required>
                <el-select filterable v-model="form_data.asset" @change="clearAssetData" :disabled="index !== 0">
                  <el-option v-for="(name, value) in asset_to_chains"
                             :key="value"
                             :label="value"
                             :value="value">
                  </el-option>
                </el-select>
                <el-select filterable multiple v-model="form_data.chains" placeholder="充值币种公链（多选）"
                           :disabled="index !== 0">
                  <el-option v-for="name in asset_to_chains[form_data.asset]"
                             :key="name"
                             :label="name"
                             :value="name">
                  </el-option>
                </el-select>
                最低充值数量：
                <el-input-number style="width: 20%" v-model="form_data.threshold" placeholder="最低充值数量"
                                 :disabled="index !== 0"></el-input-number>
              </el-form-item>
              <el-form-item label="活动方式" required>
                <el-select filterable v-model="cfg.mode" :disabled="disable_edit">
                  <el-option v-for="(val, key) in modes"
                             :key="key"
                             :label="val"
                             :value="key">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="奖励选项" required>
                <el-button
                  type="primary"
                  @click="addGift(cfg, 'ASSET')"
                  icon="el-icon-plus">
                  奖励币种
                </el-button>
                <!-- <el-button
                  type="primary"
                  @click="addGift(cfg, 'COUPON')"
                  icon="el-icon-plus">
                  奖励卡券
                </el-button> -->
                <el-button
                  type="primary"
                  @click="addGift(cfg, 'EQUITY')"
                  icon="el-icon-plus">
                  奖励权益
                </el-button>
                <div class="row-item" v-for="(gift_rule, rule_idx) in cfg.gift_rules">
                  <el-divider></el-divider>
                  <div v-if="gift_rule.gift_type === 'ASSET'">
                    <el-form-item required>
                      <el-row>
                        <el-col :span="2">
                          奖励币种
                        </el-col>
                        <el-col :span="3">
                          <el-select filterable v-model="gift_rule.gift_asset" :disabled="disable_edit">
                            <el-option v-for="(val, key) in asset_to_chains"
                                       :key="key"
                                       :label="key"
                                       :value="key">
                            </el-option>
                          </el-select>
                        </el-col>
                      </el-row>
                    </el-form-item>
                    <el-form-item v-if="cfg.mode === 'PROPORTION'">
                      <br>
                      <el-row>
                        <el-col :span="2">
                          返现比例
                        </el-col>
                        <el-col :span="3">
                          <el-input v-model="gift_rule.proportion" placeholder="赠送比例0-100"></el-input>
                        </el-col>
                        <el-col :span="3">%&nbsp;&nbsp;{{ gift_rule.gift_asset }}</el-col>
                      </el-row>
                    </el-form-item>
                    <el-form-item required>
                      <br>
                      <el-row>
                        <el-col :span="2">
                          充值排名第
                        </el-col>
                        <el-col :span="3">
                          <el-input v-model="gift_rule.rank_min" @change="calcTotal(gift_rule)"></el-input>
                        </el-col>
                        <el-col :span="1">
                          到
                        </el-col>
                        <el-col :span="3">
                          <el-input v-model="gift_rule.rank_max" @change="calcTotal(gift_rule)"></el-input>
                        </el-col>
                        <el-col :span="2">
                          每人上限
                        </el-col>
                        <el-col :span="3">
                          <el-input v-model="gift_rule.rank_limit" @change="calcTotal(gift_rule)"></el-input>
                        </el-col>
                        <el-col :span="3">
                          个， 活动总额
                        </el-col>
                        <el-col :span="3">
                          <el-input v-model="gift_rule.rank_total" :disabled="true"></el-input>
                        </el-col>
                        <el-col :span="3">
                          个
                        </el-col>
                      </el-row>
                    </el-form-item>
                  </div>
                  <div v-else-if="gift_rule.gift_type === 'EQUITY'">
                    <el-form-item required>
                      <el-row>
                        <el-col :span="2">
                          奖励权益
                        </el-col>
                        <el-col :span="4">
                          <EquitySelector v-model="currentEquity" @input="handleEquityChange(gift_rule)"/>
                        </el-col>
                        <el-col :span="18">
                          <span v-if="gift_rule.equity_id">
                            权益：ID {{ gift_rule.equity_id }}  返现价值{{ selectedEquities[gift_rule.equity_id].cost_amount }} {{ selectedEquities[gift_rule.equity_id].cost_asset }} 返现币种 {{ selectedEquities[gift_rule.equity_id].extra_data.cashback_asset }} 返现比例:{{ selectedEquities[gift_rule.equity_id].extra_data.cashback_ratio * 100 }}%
                          </span>
                        </el-col>
                      </el-row>
                    </el-form-item>
                    <el-form-item required>
                                    <el-row>
                        <el-col :span="2">
                          充值排名第
                        </el-col>
                        <el-col :span="3">
                          <el-input v-model="gift_rule.rank_min" @change="calcTotal(gift_rule)"></el-input>
                        </el-col>
                        <el-col :span="1">
                          到
                        </el-col>
                        <el-col :span="3">
                          <el-input v-model="gift_rule.rank_max" @change="calcTotal(gift_rule)"></el-input>
                        </el-col>
                        <el-col :span="2">
                          活动上限
                        </el-col>
                        <el-col :span="3">
                          <el-input v-model="gift_rule.rank_total" :disabled="true"></el-input>
                        </el-col>
                        <el-col :span="3">
                          张，每人限领
                        </el-col>
                        <el-col :span="3">
                          <el-input v-model="gift_rule.rank_limit" :disabled="true"></el-input>
                        </el-col>
                        <el-col :span="1">
                          张
                        </el-col>
                      </el-row>
                    </el-form-item>
                  </div>
                  <div v-else>
                    <el-form-item required>
                      <el-row>
                        <el-col :span="2">
                          奖励卡券
                        </el-col>
                        <el-col :span="4">
                          <el-button type="primary"
                                     @click="handleAddCouponReward(gift_rule, index)" :disabled="disable_edit">
                            关联卡券
                          </el-button>
                        </el-col>
                        <el-col :span="8">
                          <span v-if="gift_rule.coupon_apply_id > 0">
                            卡券：
                            {{ coupon_types[coupons[gift_rule.coupon_apply_id].coupon_type] }}
                            {{ coupons[gift_rule.coupon_apply_id].value }}
                            {{ coupons[gift_rule.coupon_apply_id].value_type }}
                          </span>
                        </el-col>
                      </el-row>
                    </el-form-item>
                    <el-form-item required>
                      <br>
                      <el-row>
                        <el-col :span="2">
                          充值排名第
                        </el-col>
                        <el-col :span="3">
                          <el-input v-model="gift_rule.rank_min" @change="calcTotal(gift_rule)"></el-input>
                        </el-col>
                        <el-col :span="1">
                          到
                        </el-col>
                        <el-col :span="3">
                          <el-input v-model="gift_rule.rank_max" @change="calcTotal(gift_rule)"></el-input>
                        </el-col>
                        <el-col :span="2">
                          活动上限
                        </el-col>
                        <el-col :span="3">
                          <el-input v-model="gift_rule.rank_total" :disabled="true"></el-input>
                        </el-col>
                        <el-col :span="3">
                          张，每人限领
                        </el-col>
                        <el-col :span="3">
                          <el-input v-model="gift_rule.rank_limit" :disabled="true"></el-input>
                        </el-col>
                        <el-col :span="1">
                          张
                        </el-col>
                      </el-row>
                    </el-form-item>
                  </div>
                  <el-row>
                    <el-col :span="3" class="pull-right">
                      <el-button
                        type="danger"
                        @click="deleteGift(cfg, rule_idx)"
                        icon="el-icon-minus">
                        删除
                      </el-button>
                    </el-col>
                  </el-row>
                </div>
              </el-form-item>
              <el-form-item label="活动标识">
                <el-radio-group v-model="cfg.flag" :disabled="disable_edit">
                  <template v-for="(name, key) in flags">
                    <el-radio :label="key" :key="key">{{ name }}</el-radio>
                  </template>
                </el-radio-group>
              </el-form-item>
              <el-row>
                <el-col :span="3" class="pull-right">
                  <el-button
                    type="danger"
                    @click="deleteItem(form_data, index)"
                    icon="el-icon-minus">
                    删除活动
                  </el-button>
                </el-col>
              </el-row>
            </div>
          </el-form>
        </el-collapse-item>

        <el-collapse-item name="14" style="margin-bottom: 20px; margin-top: 20px">
          <template slot="title">
            <h2>活动标题</h2>
          </template>
          <el-row>
            <el-col :span="2">
              <el-upload
                action
                :key="Date.now()"
                ref="upload"
                name="batch-upload"
                :auto-upload="false"
                :show-file-list="false"
                :on-change="handle_update_template"
                accept=".xlsx"
              >
                <el-button type="primary" @click="$refs.upload.submit()">上传</el-button>
              </el-upload>
            </el-col>
            <el-col :span="1">
              <el-button type="primary" @click="dowloadTemplate('title')">下载模板</el-button>
            </el-col>
          </el-row>
          <el-tabs v-model="cur_lang" tpye="card">
            <template v-for="(name, lang) in languages">
              <el-tab-pane :label="`${name}`" :name="lang" :key="lang">
                <el-form :model="form_data.details" label-width="100px">
                  <el-form-item label="活动标题" required>
                    <el-input v-model="form_data.details[lang].title" :disabled="disable_edit_base"></el-input>
                  </el-form-item>
                  <br/>
                </el-form>
              </el-tab-pane>
            </template>
          </el-tabs>
        </el-collapse-item>
      </el-form>
    </el-collapse>
    <template>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handle_submit" :disabled="disable_edit && disable_edit_base"
                   :loading="btnLoading">保存</el-button>
      </span>
    </template>
    <el-dialog :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>

    <el-dialog
      title="请选择关联卡券"
      :visible.sync="dialog_coupon_show"
      :before-close="handleClose"
      width="80%">
      <template>
        <div class="table-data">
          <el-table :data="coupon_items"
                    v-loading="coupon_loading"
                    highlight-current-row
                    @cell-click="handleSelectCoupon"
                    stripe>
            <el-table-column label="发放ID"
                             prop="id"
                             show-overflow-tooltip>
            </el-table-column>

            <el-table-column label="发放标题"
                             prop="title"
                             show-overflow-tooltip>
            </el-table-column>

            <el-table-column
              prop="source"
              label="发放类型"
              :formatter="(row) => `${source_types[row.source]}`"
            >
            </el-table-column>

            <el-table-column
              prop="coupon_type"
              label="卡劵类型"
              :formatter="(row) => `${coupon_types[row.coupon_type]}`"
            >
            </el-table-column>

            <el-table-column prop="value" label="面额">
              <template slot-scope="scope">
                {{ scope.row.value }} {{ scope.row.value_type }}
              </template>
            </el-table-column>

            <el-table-column prop="total_count" label="发放总数"></el-table-column>

            <el-table-column
              prop="send_at"
              label="发放时间"
              :formatter="(row) => $formatDate(row.send_at)"
            >
            </el-table-column>

            <el-table-column prop="status" label="状态"></el-table-column>

            <el-table-column prop="remark" label="备注"></el-table-column>

          </el-table>
          <el-pagination :current-page.sync="template_filters.page"
                         :page-size.sync="template_filters.limit"
                         :page-sizes="[10, 50, 100, 200, 500]"
                         :total="coupon_total"
                         @size-change="couponQuery"
                         @current-change="couponQuery"
                         :hide-on-single-page="true"
                         layout="total, sizes, prev, pager, next, jumper">
          </el-pagination>
        </div>
      </template>
    </el-dialog>


  </div>
</template>

<script>
import Vue from 'vue';
import moment from 'moment';
import XLSX from "xlsx";
import VueClipboard from 'vue-clipboard2';
import {readFile} from "@/plugins/tools";
import AITranslateTab from '@/components/AITranslateTab.vue';
import EquitySelector from '@/components/EquitySelector.vue';

Vue.use(VueClipboard);

require("via-editor/via-editor.css");
require("via-editor/theme.css");
require("via-editor/core.css");

const {Component} = require('via-editor');
const base_url = '/api/activity/deposit-bonus/activity';
export default {
  components: {
    "Editor": Component,
    AITranslateTab,
    EquitySelector
  },
  methods: {
    questionSortLangs() {
      let originalArray = Object.keys(this.langs);
      this.questionDialogTranslate.sources = this.questionDialogTranslate.sources.slice().sort((a, b) => {
        return originalArray.indexOf(a) - originalArray.indexOf(b);
      });
      this.questionDialogTranslate.selectedTargets = this.questionDialogTranslate.selectedTargets.slice().sort((a, b) => {
        return originalArray.indexOf(a) - originalArray.indexOf(b);
      });
    },

    handleEquityChange(gift_rule) {
      let equity = JSON.parse(JSON.stringify(this.currentEquity))
      if (equity) {
        gift_rule.equity_id = equity.id
      }
      this.selectedEquities[equity.id] = equity
    },

    deleteItem(form, index) {
      if (form.configs.length === 1) {
        this.$message.error("至少需要保留一个活动!");
        return
      }
      form.configs.splice(index, 1);
    },

    addItem(form) {
      form.configs.push({
        gift_rules: [],
        select_conditions: [],
        not_used_list: [],
        TRADE_TYPE_RANGE: [],
        VIP: 'VIP0', // default selected
        TRADE_BUSINESS_TYPE: 'REAL_TIME', // default selected
        KYC: 'FALSE', // default selected
        NEED_HOLDING: 'NOT_LIMITED', // default selected
        ASSET_HOLDING: '', // default selected
        TRADE_DAY_RANGE: 7, // default selected
        TRADE_VALUE_OP: 'GREATER', // default selected
        TRADE_OP_TYPE: 'NOT_LIMITED', // default selected
        BALANCE_OP_TYPE: 'NOT_LIMITED', // default selected
        TRADE_VALUE: 0, // default selected
        BALANCE_VALUE: 0, // default selected
        REGISTERED_OP_TYPE: 'NOT_LIMITED', // default selected
        USED_VALUE: [],
      });
    },
    deleteGift(cfg, index) {
      if (cfg.gift_rules.length === 1) {
        this.$message.error("至少需要保留一个奖励规则!");
        return
      }
      cfg.gift_rules.splice(index, 1);
    },
    addGift(cfg, giftType) {
      if (giftType === 'EQUITY') {
        cfg.gift_rules.push({ gift_type: giftType, rank_limit: 1 });
        this.currentEquity = null
      } else {
        cfg.gift_rules.push({gift_type: giftType});
      }
      console.log(this.selectedEquities)
      console.log(cfg.gift_rules)
    },
    clearAssetData() {
      this.form_data.chains = [];
      this.form_data.threshold = null;
    },
    calcTotal(giftRule) {
      if (giftRule.rank_min && giftRule.rank_max) {
        let total = Number(giftRule.rank_max) - Number(giftRule.rank_min) + 1;
        if (giftRule.rank_limit) {
          total = total * Number(giftRule.rank_limit)
        }
        giftRule.rank_total = total;
      } else {
        giftRule.rank_total = null;
      }
      this.$forceUpdate();
    },

    initDetails() {
      // debugger
      let lang_list = Object.keys(this.languages);
      this.form_data.details = Object.fromEntries(
        lang_list.map((lang) => [
          lang,
          {
            lang: lang,
            title: '',
          },
        ])
      );
    },

    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    forceUpdate(cfg, index) {
      console.log(cfg, index);
      this.$set(this.form_data.configs, index, cfg);
    },
    handle_deletion_condition(cfg, index, condition, idx) {
      // debugger;
      cfg.select_conditions.splice(idx, 1);
      this.$set(this.form_data.configs, index, cfg);
      // cfg.select_conditions = cfg.select_conditions.filter(t => t != condition)
      if (condition == 'KYC') {
        cfg.KYC = "FALSE"
        // this.$set(cfg, 'KYC', 'FALSE')
      } else if (condition == "VIP") {
        cfg.VIP = "VIP0"
      } else if (condition == "HOLDING") {
        cfg.NEED_HOLDING = "NOT_LIMITED"
        cfg.ASSET_HOLDING = ""
      } else if (condition == "TRADE_VALUE") {
        cfg.TRADE_OP_TYPE = "NOT_LIMITED"
        cfg.TRADE_VALUE = 0
        cfg.TRADE_TYPE_RANGE = []
      } else if (condition == "BALANCE_VALUE") {
        cfg.BALANCE_OP_TYPE = "NOT_LIMITED"
        cfg.BALANCE_VALUE = 0
      } else if (condition == "REGISTERED_VALUE") {
        cfg.REGISTERED_OP_TYPE = "NOT_LIMITED"
      } else if (condition == "USED_VALUE") {
        cfg.USED_VALUE = []
        cfg.not_used_list = []
      }
      // this.$forceUpdate();
    },
    handle_filter_creation(obj) {
      // debugger;
      let key = obj.key;
      const data = this.form_data.configs[obj.index];
      if (!data.select_conditions) {
        data.select_conditions = [];
      }
      if (data.select_conditions.includes(obj.key)) {
        this.$message.error(`筛选条件已存在!`);
        return
      }
      if (key == "KYC") {
        data.KYC = 'FALSE';
      } else if (key == "VIP") {
        data.VIP = 'VIP1';
      } else if (key == "HOLDING") {
        data.NEED_HOLDING = "HOLD";
      } else if (key == "TRADE_VALUE") {
        data.TRADE_DAY_RANGE = 7
        data.TRADE_VALUE_OP = "GREATER"
        data.TRADE_OP_TYPE = "GREATER"
        data.TRADE_BUSINESS_TYPE = "REAL_TIME"
        data.TRADE_TYPE_RANGE = []
      } else if (key == "BALANCE_VALUE") {
        data.BALANCE_OP_TYPE = "GREATER"
      } else if (key == "REGISTERED_VALUE") {
        data.REGISTERED_OP_TYPE = "GREATER"
      }
      this.$set(this.form_data.configs, obj.index, data);
      data.select_conditions.push(key);
    },
    handleLabelTypeChange() {
      if (this.form_data.label_type === 'ASSET') {
        this.handle_deletion_reward_condition("COUPON");
        if (this.form_data.asset_rewards.length > 0) {
          this.form_data.asset_rewards = [this.form_data.asset_rewards[0]];
        }
      }
    },
    handle_deletion_reward_condition(condition) {
      this.reward_conditions = this.reward_conditions.filter(t => t != condition)
      if (condition == 'ASSET') {
        this.form_data.asset_rewards = []
      } else if (condition == "COUPON") {
        this.form_data.coupon_rewards = []
      }
    },
    handle_reward_creation(key) {
      if (this.reward_conditions.includes(key)) {
        this.$message.error(`奖励类型已存在!`);
        return
      }
      if (this.form_data.label_type === 'ASSET') {
        if (key === 'COUPON') {
          this.$message.error(`标签选择币种时，奖励类型只能是币种!`);
          return
        }
      }
      this.reward_conditions.push(key);
    },
    deleteAssetRewardItem(item, index) {
      this.form_data.asset_rewards.splice(index, 1);
    },
    deleteCouponRewardItem(item, index) {
      this.form_data.coupon_rewards.splice(index, 1);
    },
    handleAddAssetReward() {
      if (this.form_data.label_type === 'ASSET') {
        if (this.form_data.asset_rewards.length >= 1) {
          this.$message.error(`标签为币种，空投奖励仅可配置一种币种!`);
          return;
        }
      }
      if (this.form_data.asset_rewards.length >= 10) {
        this.$message.error(`奖励币种数量已达上线!`);
        return;
      }
      this.form_data.asset_rewards.push({
        asset: '',
        amount: 0,
      });
    },
    handleAssetRewardChange(index, asset) {
      for (let i in this.form_data.asset_rewards) {
        if (index === Number(i)) {
          continue;
        }
        let obj = this.form_data.asset_rewards[i];
        let already_asset = obj['asset'];
        if (asset === already_asset) {
          this.form_data.asset_rewards[index]['asset'] = '';
          this.$message.error(`请勿重复选择奖励币种!`);
          return;
        }
      }
    },
    handleAddCouponReward(giftRule, cfgIdx) {
      // debugger
      this.dialog_coupon_show = true;
      this.curGiftRule = giftRule;
      this.curCfgIdx = cfgIdx;
      this.couponQuery();
    },
    handleSelectCoupon(row, column, cell, event) {
      let that = this;
      this.$confirm(`确认选择?`).then(() => {
        let id = row.id;
        if (!id) {
          return;
        }
        // debugger;
        let existCouponApplyIds = [];
        that.form_data.configs.forEach((cfg, index) => {
          if (index !== this.curCfgIdx) {
            cfg.gift_rules.forEach(e => {
              if (e.gift_type === 'COUPON' && e.coupon_apply_id) {
                existCouponApplyIds.push(e.coupon_apply_id)
              }
            })
          }
        })
        if (existCouponApplyIds.includes(id)) {
          this.$confirm(`该卡券ID已在另一活动池配置，同一卡券ID无法对同一人发放2张，请确认是否仍然使用这张卡券?`).then(
            () => {
              that._setCouponData(that, row, id);
            }
          )
        } else {
          that._setCouponData(that, row, id);
        }
      });
    },
    _setCouponData(that, row, id) {
      that.curGiftRule.coupon_apply_id = id;
      if (!that.coupons) {
        that.coupons = {}
      }
      that.coupons[id] = {
        apply_id: id,
        title: row.title,
        coupon_id: row.coupon_id,
        coupon_type: row.coupon_type,
        value: row.value,
        value_type: row.value_type,
        remark: row.remark,
      };
      that.dialog_coupon_show = false;
    },
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          this.dialog_coupon_show = false;
          done();
        })
        .catch(_ => {
        });
    },
    couponQuery() {
      this.coupon_loading = true;

      this.$axios.get('/api/coupon/apply/list', {params: this.template_filters}).then(
        res => {
          this.coupon_loading = false;
          if (res && res.data.code === 0) {
            let data = res.data.data;
            this.coupon_items = data.items;
            this.coupon_total = data.total;
            this.coupon_types = data.coupon_types;
            this.source_types = data.source_types;
          } else {
            this.coupon_items = [];
            this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
          }
        }
      );
    },
    get_total_amount(amount) {
      if (!this.form_data.total_count) {
        return '-';
      }
      if (!amount) {
        return '-';
      }
      let total_amount = Number(this.form_data.total_count) * Number(amount);
      return total_amount;
    },
    get_coupon_total_amount() {
      if (!this.form_data.total_count) {
        return '-';
      }
      if (!this.coupon_amount) {
        return '-';
      }
      if (this.form_data.coupon_rewards.length === 0) {
        return '-';
      }
      let total_amount = Number(this.form_data.total_count) * Number(this.coupon_amount);
      return total_amount;
    },
    notice(title, message) {
      this.$alert(message, title, {
        confirmButtonText: '确定',
      });
    },
    res_success_notice(res) {
      let title = '提交成功';
      this.notice(title, title);
    },
    res_fail_notice(res) {
      let title = '提交失败';
      let message = `(code: ${res.data?.code}; message: ${res.data?.message}; data: ${res.data?.data})`;
      this.notice(title, message);
    },
    res_error_notice(error) {
      let title = '提交失败';
      let message = `(code: ${error.response.status}; message: ${error.message}; data: ${res.data?.data})`;
      this.notice(title, message);
    },
    handle_number_input(field, place) {
      place = place || 8;
      let val = this.form_data[field];
      if (val === undefined || val === '') {
        return;
      }
      // /^\d*(\.?\d{0,8})/g
      var re = new RegExp('^\\d*(\.?\\d{0,' + place + '})', 'g');
      let res = String(val).match(re)[0];
      this.form_data[field] = res;
    },
    to_utc_date(date_str) {
      return moment.utc(date_str, 'YYYY-MM-DD').format('YYYY-MM-DD HH:mm:ss');
    },
    get_data() {
      let id = this.$route.query.id
      this.loading = true
      this.airdrop_detail_id = id
      this.action = this.DIALOG_EDIT
      if (id === "0") {
        this.action = this.DIALOG_CREATION
      }
      this.$axios.get(`${base_url}/${id}`).then(res => {
        if (res?.data?.code === 0) {
          let data = res.data.data;
          let extra = data.extra;
          this.languages = extra.languages;
          this.airdrop_mode_dict = extra.airdrop_mode_dict;
          this.labels_dict = extra.labels;
          this.status_dict = extra.status_dict;
          this.active_status_dict = extra.active_status_dict;
          this.condition_keys = extra.conditions;
          this.not_used_dict = extra.not_used_dict;
          this.asset_list = extra.assets;
          this.funding_sources = extra.funding_sources;
          this.estimate_type_dict = extra.estimate_type_dict;
          this.reward_type_dict = extra.reward_type_dict;
          this.coupon_types = extra.coupon_types;
          this.coupons = extra.coupon_details;
          this.langs = extra.langs;
          this.trade_type_dict = extra.trade_type_dict;
          this.market_maker_dict = extra.market_maker_dict;
          this.asset_to_chains = extra.asset_to_chains;
          this.flags = extra.flags;
          this.modes = extra.modes;
          this.selectedEquities = extra.equity_details || {};
          if (id === "0") {
            this.handle_creation();
          } else {
            this.handle_edit(data.activity);
          }
        } else {
          this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
        }
        this.loading = false
        this.loaded = true
      });

    },
    create_add_question_config(lang) {
      if (!this.form_data.question_configs[lang]) {
        this.$set(this.form_data.question_configs, lang, [])
      }
      this.form_data.question_configs[lang].push({
        A: '',
        B: '',
        C: '',
        answer: 'A',
        question: '',
        answer_analysis: '',
      })
    },
    create_add_introduction_config(lang) {
      this.form_data.introduction_configs[lang].push({
        content: '',
        title: '',
      })
    },
    create_remove_question_config(details, index) {
      details.splice(parseInt(index), 1);
    },
    create_remove_introduction_config(details, index) {
      details.splice(parseInt(index), 1);
    },
    import_question_configs(lang) {
      this.cur_lang = lang
    },
    upload_success(detail, res) {
      detail.cover_url = res.data.file_url;
      detail.cover = res.data.file_key;
      detail.hideUploadAdd = true;
      detail.file_list = [{name: '', url: res.data.file_url}];
    },
    upload_remove(detail, res) {
      detail.cover = '';
      detail.cover_url = '';
      detail.hideUploadAdd = false;
      detail.file_list = [];
    },
    handle_creation() {
      this.initDetails();
      this.dialog_type = this.DIALOG_CREATION;
      this.dialog_visible = true;
    },

    handle_edit(row) {
      // debugger;
      this.form_data = {..._.clone(row)};
      if (this.form_data.active_status === 'FINISHED' || this.form_data.status === 'CREATED') {
        this.disable_edit_base = this.disable_edit = true;
      } else if (this.form_data.active_status === 'STARTED') {
        this.disable_edit_base = false;
        this.disable_edit = true;
      } else if (this.form_data.status === 'REJECTED') {
        this.disable_edit_base = this.disable_edit = false;
      } else {
        this.disable_edit_base = this.disable_edit = false;
      }
      this.form_data.configs.forEach(e => {
        e.not_used_list = [];
        if (e.conditions.hasOwnProperty("USED_VALUE")) {
          e.not_used_list = e.conditions['USED_VALUE']
        }
        if (e.conditions.hasOwnProperty("TRADE_VALUE")) {
          e.TRADE_TYPE_RANGE = e.conditions['TRADE_TYPE_RANGE'] || []
        }
        e.select_conditions = Object.keys(e.conditions);
        Object.assign(e, {...e, ...e.conditions});
      })
      // debugger;
      this.dialog_type = this.DIALOG_EDIT;
      this.dialog_visible = true;
      this.form_data.start_time = this.form_data.start_time * 1000;
      this.form_data.end_time = this.form_data.end_time * 1000;
      // console.log(this.form_data);
    },
    handleOnOffline(row, is_online) {
      let new_status = is_online ? 'ONLINE' : 'OFFLINE';
      this.$confirm(is_online ? '确认上架？' : '确认下架？')
        .then(_ => {
          this.$axios
            .patch(base_url, {id: row.id, status: new_status})
            .then(res => {
              if (res?.data?.code === 0) {
                this.res_success_notice(res);
                this.form_data = {};
                this.get_data();
              } else {
                this.res_fail_notice(res);
              }
            })
            .catch(e => {
              this.res_error_notice(e);
            });
        })
        .catch(_ => {
        });
    },
    handle_close() {
      this.$confirm(`是否退出当前页面?`).then(() => {
        window.opener = null;
        window.open("about:blank", "_top").close();
      });
    },
    handle_type_range(cfg, k, value) {
      let all = "ALL"
      if (value === true) {
        if (k === all) {
          if (cfg.TRADE_TYPE_RANGE.length !== 1) {
            this.$message.error(`请先取消其他范围，再勾选全部`);
            _.pull(cfg.TRADE_TYPE_RANGE, all)
          }
        } else {
          if (cfg.TRADE_TYPE_RANGE.includes(all)) {
            this.$message.error(`请先取消全部，再勾选其他范围`);
            _.pull(cfg.TRADE_TYPE_RANGE, k)
          }
        }
      }
    },
    handle_submit() {
      if (!this.disable_edit_base && this.disable_edit) {
        this.$alert('活动进行中，确认更新？', '提示', {
          confirmButtonText: '确认',
          type: 'warning',
        }).then(() => {
          this.submit_validate_data();
        });
      } else {
        this.submit_validate_data();
      }
    },
    importSuccess(response, file, fileList) {
      if (response.code !== 0) {
        this.$alert(response.message, '错误'
        ).then(() => {
        }).catch(() => {
        });
        return;
      }
      this.$alert('成功上传' + response.data.length + '条记录', '成功');

      this.$set(this.form_data.question_configs, this.cur_lang, [])

      response.data.forEach((e) => {
        this.form_data.question_configs[this.cur_lang].push({
          A: e.A === null ? '' : String(e.A),
          B: e.B === null ? '' : String(e.B),
          C: e.C === null ? '' : String(e.C),
          answer: e.answer === null ? '' : String(e.answer),
          question: e.question === null ? '' : String(e.question),
          answer_analysis: e.answer_analysis === null ? '' : String(e.answer_analysis),
        });
      });
    },
    importError(err, file, fileList) {
      this.$alert("上传失败", "错误")
    },
    handleUpload(response, file, fileList) {
      // debugger
      const isEmpty = Object.keys(response.data).length === 0;
      if (isEmpty) {
        this.$alert('上传失败：' + response.message, '错误'
        ).then(() => {
          this.get_data();
        }).catch(() => {
          this.get_data();
        });
      } else {
        this.form_data.cover_url = response.data.file_url;
        this.form_data.cover = response.data.file_key;
      }
    },
    handleDownloadTemplate() {
      let url = "/api/activity/airdrop/template";
      this.$download_from_url(url, 'airdrop-template.xlsx')
    },
    fmt_introduction(fileInfo) {
      var result = ""
      if (fileInfo.title1) {
        result += `<p style="font-size: 18px; font-weight: bold;">${fileInfo.title1}</p>`
      }
      if (fileInfo.content1) {
        result += `<p>${fileInfo.content1}</p>`
      }
      if (fileInfo.title2) {
        result += `<p  style="font-size: 18px; font-weight: bold;">${fileInfo.title2}</p>`
      }
      if (fileInfo.content2) {
        result += `<p>${fileInfo.content2}</p>`
      }
      if (fileInfo.title3) {
        result += `<p style="font-size: 18px; font-weight: bold;">${fileInfo.title3}</p>`
      }
      if (fileInfo.content3) {
        result += `<p>${fileInfo.content3}</p>`
      }
      if (fileInfo.title4) {
        result += `<p style="font-size: 18px; font-weight: bold;">${fileInfo.title4}</p>`
      }
      if (fileInfo.content4) {
        result += `<p>${fileInfo.content4}</p>`
      }
      return result
    },
    async write_data_to_from(data_arr, file_type) {
      let langMapper = {}
      data_arr.forEach((item) => {
        let lang = item.lang.toUpperCase();
        if (file_type == "title") {
          langMapper[lang] = {
            title: item.title.trim()
          }
        }
      });

      Object.values(this.form_data.details).forEach((detail) => {
        var fileInfo = langMapper[detail.lang]
        if (!fileInfo) return;
        if (file_type == "title") {
          this.$set(detail, 'title', fileInfo.title);
        } else if (file_type == "summary") {
          this.$set(detail, 'summary', fileInfo.summary);
          this.$set(detail, 'introductions', this.fmt_introduction(fileInfo));
        }
      })

      this.$message.success("上传成功");
    },

    async handle_update_template(file) {
      let dataBinary = await readFile(file.raw);
      let workBook = XLSX.read(dataBinary, {type: "binary", cellDates: true});
      let workSheet = workBook.Sheets[workBook.SheetNames[0]];
      let json_data = XLSX.utils.sheet_to_json(workSheet);
      let data_arr = await this.fmt_read_excel_data(json_data, "title");
      await this.write_data_to_from(data_arr, "title");
    },
    async handle_update_summary_template(file) {
      let dataBinary = await readFile(file.raw);
      let workBook = XLSX.read(dataBinary, {type: "binary", cellDates: true});
      let workSheet = workBook.Sheets[workBook.SheetNames[0]];
      let json_data = XLSX.utils.sheet_to_json(workSheet);
      let data_arr = await this.fmt_read_excel_data(json_data, "summary");
      await this.write_data_to_from(data_arr, "summary");
    },
    async fmt_read_excel_data(json_data, file_type) {
      let character = {
        lang: "lang"
      };
      if (file_type == 'title') {
        character['title'] = "标题"
      }
      let arr = [];
      json_data.forEach((item) => {
        let obj = {};
        for (let key in character) {
          if (!character.hasOwnProperty(key)) break;
          let v = item[character[key]];
          if (!v) {
            obj[key] = "";
            continue
          }
          obj[key] = v.replace(/\r?\n/g, "<br />");
        }
        arr.push(obj);
      });
      return arr;
    },
    dowloadTemplate(template_type) {
      let arr = [];
      Object.keys(this.languages).forEach(lang => {
        let arr_item = {
          语言: this.languages[lang],
          lang: lang.toLocaleLowerCase(),
        };
        if (template_type == "title") {
          arr_item['标题'] = ""
        }
        arr.push(arr_item);
      })
      // 将json数据变为sheet数据
      let sheet = XLSX.utils.json_to_sheet(arr);
      // 新建表格
      let book = XLSX.utils.book_new();
      // 在表格中插入一个sheet
      XLSX.utils.book_append_sheet(book, sheet, "sheet1");
      // 通过xlsx的writeFile方法将文件写入
      XLSX.writeFile(book, `${template_type}_template.xlsx`);
    },
    process_form_data() {
      var new_form_data = _.cloneDeep(this.form_data);

      // 将 details 字典转换回数组
      new_form_data.details = Object.values(new_form_data.details);
      // console.log('process_form_data')
      // console.log(new_form_data.details)
      new_form_data.configs.forEach(e => {
        // 兼容老数据没有新增字段，赋予默认值
        if (!e.MARKET_MAKER) {
          e.MARKET_MAKER = "NOT_LIMITED"
        }
        if (!e.KYC) {
          e.KYC = "FALSE"
        }
        if (!e.VIP) {
          e.VIP = "VIP0"
        }
        if (!e.NEED_HOLDING) {
          e.NEED_HOLDING = "NOT_LIMITED"
          e.ASSET_HOLDING = ""
        }
        if (!e.TRADE_VALUE_OP) {
          // todo 这个字段没有用
          e.TRADE_VALUE_OP = 'GREATER'
        }
        if (!e.TRADE_DAY_RANGE) {
          e.TRADE_DAY_RANGE = 7
        }
        if (!e.TRADE_OP_TYPE) {
          e.TRADE_OP_TYPE = 'NOT_LIMITED'
        }
        if (!e.TRADE_VALUE) {
          e.TRADE_VALUE = 0
          e.TRADE_BUSINESS_TYPE = "REAL_TIME"
          e.TRADE_TYPE_RANGE = []
        }
        if (!e.BALANCE_OP_TYPE) {
          e.BALANCE_OP_TYPE = 'NOT_LIMITED'
        }
        if (!e.BALANCE_VALUE) {
          e.BALANCE_VALUE = 0
        }
        if (!e.REGISTERED_VALUE) {
          e.REGISTERED_OP_TYPE = 'NOT_LIMITED'
        }
        if (!e.info_url) {
          e.info_url = ''
        }
        if (this.form_data.lock_day === '') {
          this.form_data.lock_day = 0
        }
        e['USED_VALUE'] = e.not_used_list
        e['TRADE_TYPE_RANGE'] = e.TRADE_TYPE_RANGE
      })
      return new_form_data;
    },
    submit_validate_data() {
      let method = 'post';
      let id = this.$route.query.id;
      if (id !== '0') {
        method = 'put';
      }
      let check_success = true
      Object.values(this.form_data.details).forEach((detail) => {
        detail.title = detail.title? detail.title.trim(): '';
        if (detail.title && detail.title.length > 200) {
          this.$message.error(`语言(${detail.lang})的标题长度超过不能200`);
          check_success = false
        }
      })
      if (!check_success) {
        return
      }
      if (!this.form_data.start_time || !this.form_data.end_time) {
        this.$message.error(`请选择活动时间`);
        return;
      }
      if (!this.form_data.asset) {
        this.$message.error(`请输入充值币种`);
        return;
      }
      if (!this.form_data.chains.length) {
        this.$message.error(`请输入充值币种公链`);
        return;
      }
      if (!this.form_data.threshold) {
        this.$message.error(`请输入最低充值数量`);
        return;
      }
      if (!this.form_data.cover) {
        this.$message.error(`请配置活动封面`);
        return;
      }
      let that = this;
      // debugger;
      this.form_data.configs.forEach(e => {
        if (!e.mode) {
          this.$message.error(`请选择活动方式`);
          check_success = false
          return;
        }
        if (!e.flag) {
          e.flag = 'NONE';
        }
        if (!e.gift_rules || !e.gift_rules.length) {
          this.$message.error(`奖励规则不能为空`);
          check_success = false
          return;
        }
        e.gift_rules.forEach(ee => {
          if (!ee.rank_min || !ee.rank_max || !ee.rank_limit || !ee.rank_total) {
            this.$message.error(`排名设置不能为空，且必须完整！`);
            check_success = false
          }
          if (!this.isNumeric(ee.rank_min) || !this.isNumeric(ee.rank_max) || !this.isNumeric(ee.rank_limit) || !this.isNumeric(ee.rank_total)) {
            this.$message.error(`排名设置相关值，需为数字类型！`);
            check_success = false
          }
          if (ee.rank_min <= 0 || ee.rank_max <= 0 || ee.rank_limit <= 0 || ee.rank_total <= 0) {
            this.$message.error(`排名设置值不能小于 0！`);
            check_success = false
          }
          if (ee.gift_type === 'ASSET') {
            if (!ee.gift_asset) {
              this.$message.error(`奖励币种不能为空！`);
              check_success = false
            }
            if (e.mode === 'PROPORTION') {
              if (!ee.proportion) {
                this.$message.error(`赠送比例不能为空！`);
                check_success = false
              } else if (!this.isNumeric(ee.proportion)) {
                this.$message.error(`赠送比例需是数字类型！`);
                check_success = false
              } else if (Number(ee.proportion) < 0 || Number(ee.proportion) > 100) {
                this.$message.error(`赠送比例需在 0 ～ 100！`);
                check_success = false;
              }
            }
          } else if (ee.gift_type == 'EQUITY') {
            if (!ee.equity_id) {
              this.$message.error(`奖励权益不能为空！`);
              check_success = false
            }
          } else {
            if (!ee.coupon_apply_id) {
              this.$message.error(`奖励卡券不能为空！`);
              check_success = false
            }
          }
        })
      })
      if (!check_success) {
        return
      }

      if (!check_success) {
        return
      }

      var new_form_data = this.process_form_data();
      this.btnLoading = true;
      this.$axios[method](base_url, new_form_data)
        .then(res => {
          if (res?.data?.code === 0) {
            // this.form_data = {};
            // this.dialog_visible = false;
            let data = res.data.data;
            this.$router.replace({query: {id: data.id}});
            this.$route.query.id = data.id;
            this.res_success_notice(res);
            this.get_data();
            // this.$router.push("/operation/activity/deposit-bonus-activity");
          } else {
            this.res_fail_notice(res);
          }
        })
        .catch(e => {
          console.log(e);
          this.res_error_notice(e);
        }).finally(() => {
        this.btnLoading = false;
      })
    },
    isNumeric(value) {
      value = Number(value);
      return typeof value === 'number' && !Number.isNaN(value) && Number.isFinite(value);
    },
  },
  mounted() {
    this.get_data();
  },
  computed: {
    options: function () {
      return {
        image: {
          size: {
            max: 5000, // KB
            error: '图片大小不能大于5000KB',
          },
          accepts: ['.png', '.jpg', '.jpeg'],
          upload: (file) => {
            return new Promise((resolve) => {
              let formdata = new FormData()
              formdata.set('img', file)
              let response = {code: 0, data: "", message: "成功"}
              this.$axios.post('/api/upload/image', formdata).then(res => {
                if (res.data.code === 0) {
                  response.data = res.data.data.file_url;
                  resolve(response);
                } else {
                  response.code = res.data?.code;
                  response.message = res.data?.message;
                  resolve(response)
                }
              }).catch(res => {
                response.code = -1;
                response.message = "error";
                resolve(response)
              })
            });
          },
        },
        video: {
          validateUrl(url) {
            return {
              valid: true,
              message: 'ok',
            };
          },
        },
        container: {
          className: this.contents
        },
        quill: {bounds: '.el-tabs__content', debug: 'error'},
      }
    },
  },
  data() {
    return {
      filters: {
        airdrop_mode: null,
        asset: null,
        page: 1,
        limit: 50,
      },
      template_filters: {
        dynamic_user_type: 'DEPOSIT_BONUS_ACTIVITY',
        sort_name: 'send_at',
        page: 1,
        limit: 10
      },
      disable_edit: false,
      disable_edit_base: false,
      DIALOG_CREATION: 'creation',
      DIALOG_EDIT: 'edit',
      action: this.DIALOG_CREATION,
      activeNames: ["11", "12", "13", "14", "15"],
      items: [],
      total: 0,
      coupon_amount: 1,
      dialog_coupon_show: false,
      curGiftRule: {},
      curCfgIdx: {},
      coupon_items: [],
      coupon_total: 0,
      coupon_loading: false,
      coupon_types: {},
      source_types: {},
      dialogVisible: false,
      funding_sources: {},
      airdrop_mode_dict: {},
      not_used_list: [],
      TRADE_TYPE_RANGE: [],
      labels_dict: {},
      status_dict: {},
      currentEquity: null,
      selectedEquities: {},
      active_status_dict: {},
      asset_list: [],
      question_configs: {},
      introduction_configs: {},
      condition_keys: {},
      not_used_dict: {},
      trade_type_dict: {},
      asset_to_chains: {},
      market_maker_dict: {},
      modes: {},
      flags: {},
      coupons: {}, // 记录下关联的卡券
      estimate_type_dict: {},
      reward_type_dict: {},
      reward_conditions: [],
      // select_conditions: [],
      kyc_condition_type_dict: {'需要完成KYC': 'TRUE'},
      holding_list: [{label: '持有过', value: 'HOLD'}, {label: '未持有过', value: 'NOT_HOLD'}],
      vip_condition_type_dict: {
        'VIP1及以上': 'VIP1',
        'VIP2及以上': 'VIP2',
        'VIP3及以上': 'VIP3',
        'VIP4及以上': 'VIP4',
        'VIP5及以上': 'VIP5'
      },
      trade_business_type_dict: {'实时更新': 'REAL_TIME', '活动开始前': 'BEFORE_START'},
      trade_day_range_list: [{label: '7天', value: 7}, {label: '15天', value: 15}, {label: '30天', value: 30},],
      trade_value_op_list: [{label: '≥', value: 'GREATER'}, {label: '<', value: 'LESS'}],
      balance_value_op_list: [{label: '≥', value: 'GREATER'}, {label: '<', value: 'LESS'}],
      registered_value_op_list: [{label: '>', value: 'GREATER'}, {label: '<', value: 'LESS'}],
      answer_option_dict: {'A': 'A', 'B': 'B', 'C': 'C'},
      languages: [],
      default_detail_tab: 'EN_US',
      form_data: {
        start_time: null,
        end_time: null,
        zendesk_url: '',
        asset: null,
        chains: [],
        threshold: null,
        cover: null,
        cover_url: null,

        details: {
          id: null,
          deposit_bonus_id: null,
          lang: null,
          title: null,
        },
        configs: [
          {
            gift_rules: [],
            select_conditions: [],
            not_used_list: [],
            TRADE_TYPE_RANGE: [],
            VIP: 'VIP0', // default selected
            TRADE_BUSINESS_TYPE: 'REAL_TIME', // default selected
            KYC: 'FALSE', // default selected
            NEED_HOLDING: 'NOT_LIMITED', // default selected
            ASSET_HOLDING: '', // default selected
            TRADE_DAY_RANGE: 7, // default selected
            TRADE_VALUE_OP: 'GREATER', // default selected
            TRADE_OP_TYPE: 'NOT_LIMITED', // default selected
            BALANCE_OP_TYPE: 'NOT_LIMITED', // default selected
            TRADE_VALUE: 0, // default selected
            BALANCE_VALUE: 0, // default selected
            REGISTERED_OP_TYPE: 'NOT_LIMITED', // default selected
            USED_VALUE: [],
          }
        ],
      },
      cur_lang: "EN_US",
      langs: {},
      dialog_type: null,
      dialogImageUrl: "",
      dialog_visible: false,
      loading: true,
      btnLoading: false,
      loaded: false,
      upload_form: {
        type: null
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now();
        },
      },
    };
  },
};
</script>

<style>
.img-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.img-uploader .el-upload:hover {
  border-color: #409eff;
}

.img-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.hide .el-upload--picture-card {
  display: none;
}

.via-editor {
  width: 85% !important;
}

.via-editor .ql-bubble .ql-icon-picker .ql-picker-item {
  width: 100%;
}

.ql-toolbar {
  z-index: 999999;
}
</style>
