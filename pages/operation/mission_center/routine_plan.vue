<template>
    <el-container>
        <el-main>
            <h2 style="font-family: 'Microsoft YaHei', Arial, sans-serif">
                老用户任务推送
            </h2>

            <!-- Search Form -->
            <div class="search-form">
                <el-form :inline="true" :model="searchForm" class="form-inline">
                    <el-form-item label="推送ID">
                        <el-select v-model="searchForm.plan_id" placeholder="全部" clearable filterable
                            @clear="delete searchForm.plan_id">
                            <el-option label="全部" v-for="item in planIds" :key="item.key" :value="item.key"
                                :label="item.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="场景">
                        <el-select v-model="searchForm.scene_id" placeholder="全部" clearable filterable>
                            <el-option v-for="item in scenes" :label="item.value" :value="item.key" :key="item.key">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="客群ID">
                        <el-input v-model="searchForm.group_id" placeholder="请输入客群ID" clearable></el-input>
                    </el-form-item>
                    <el-form-item label="业务方">
                        <el-select v-model="searchForm.business_party" placeholder="全部" clearable filterable>
                            <el-option label="全部" value="" v-for="k, v in businessParties" :key="v" :value="v"
                                :label="k"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="状态">
                        <el-select v-model="searchForm.status" placeholder="全部" clearable>
                            <el-option label="全部" value="" v-for="k, v in statuses" :key="v" :value="v"
                                :label="k"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="handleSearch">搜索</el-button>
                        <el-button @click="handleReset">重置</el-button>
                    </el-form-item>
                </el-form>
                <div class="create-buttons">
                    <el-button type="primary" @click="handleCreateScene">+创建场景</el-button>
                    <el-button type="primary" @click="handleCreate">+创建任务推送</el-button>
                </div>
            </div>

            <!-- Table -->
            <el-table :data="tableData" style="width: 100%">
                <el-table-column prop="id" label="推送ID" width="100"></el-table-column>
                <el-table-column prop="scene_name" label="推送场景" width="150" class-name="scene-column">
                    <template #default="scope">
                        <el-tooltip :content="scope.row.scene_name" placement="top"
                            :disabled="scope.row.scene_name.length <= 20">
                            <span>{{ formatName(scope.row.scene_name) }}</span>
                        </el-tooltip>
                    </template>
                </el-table-column>
                <el-table-column prop="name" label="推送名称" width="280">
                    <template #default="scope">
                        <el-tooltip :content="scope.row.name" placement="top" :disabled="scope.row.name.length <= 20">
                            <span>{{ formatName(scope.row.name) }}</span>
                        </el-tooltip>
                    </template>
                </el-table-column>
                <el-table-column prop="business_party" label="业务方" width="120">
                </el-table-column>
                <el-table-column prop="mission_count" label="推送任务数量" width="120"></el-table-column>
                <el-table-column prop="group_ids" label="推送客群ID" width="200" class-name="customer-group-column">
                    <template #default="scope">
                        <div class="customer-group-ids">
                            <template v-if="scope.row.group_ids">
                                <template v-if="Array.isArray(scope.row.group_ids)">
                                    <span v-for="(id, index) in scope.row.group_ids">
                                        <el-link :key="`link_${scope.row.id}_${scope.$index}_${id}`"
                                            :href="`/operation/user-tag/group-detail?id=${id}`" type="primary"
                                            target="_blank" :underline="false" class="group-id-link">
                                            {{ id }}
                                        </el-link>
                                        <span v-if="index < scope.row.group_ids.length - 1" class="separator">、</span>
                                    </span>
                                </template>
                            </template>
                            <span v-else>-</span>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="推送成功/失败/总人数" width="180">
                    <template #default="scope">
                        {{ scope.row.success_count }} /
                        <span @click="showFailedUsers(scope.row)"
                            :class="scope.row.failed_count > 0 ? 'failed-count-link' : ''"
                            :style="scope.row.failed_count > 0 ? 'cursor: pointer; color: #409EFF' : ''">
                            {{ scope.row.failed_count }}
                        </span>
                        /
                        <el-link :href="`/operation/mission_center/routine_mission?plan_id=${scope.row.id}`"
                            type="primary" target="_blank" :underline="false">
                            {{ scope.row.total || '无上限' }}
                        </el-link>
                    </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" :formatter="(row) => statuses[row.status]">
                </el-table-column>
                <el-table-column width="180" class-name="effective-users-column">
                    <template #header>
                        <span>存在生效任务用户数</span>
                        <el-tooltip content="当前推送有多少用户存在1个以上正在生效的未过期未完成任务" placement="top">
                            <i class="el-icon-question" style="margin-left: 4px; color: #909399; cursor: help;"></i>
                        </el-tooltip>
                    </template>
                    <template #default="scope">
                        {{ getEffectiveUsersCount(scope.row) }}
                    </template>
                </el-table-column>
                <el-table-column prop="created_by_name" label="创建人" width="120">
                    <template #default="scope">
                        <el-link :href="'/users/user-details?id=' + scope.row.created_by" type="primary" target="_blank"
                            :underline="false"
                            style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                            {{ scope.row.created_by_name }}
                        </el-link>
                    </template>
                </el-table-column>
                <el-table-column prop="auditor_by_name" label="审核人" width="120">
                    <template #default="scope">
                        <el-link v-if="scope.row.auditor_by" :href="'/users/user-details?id=' + scope.row.auditor_by"
                            type="primary" target="_blank" :underline="false"
                            style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                            {{ scope.row.auditor_by_name }}
                        </el-link>
                        <span v-else>--</span>
                    </template>
                </el-table-column>
                <el-table-column prop="updated_at" label="表单数据最近更新时间" width="180"
                    :formatter="row => $formatDate(row.updated_at)"></el-table-column>
                <el-table-column label="操作" width="200" fixed="right">
                    <template #default="scope">
                        <el-button type="text" @click="handleView(scope.row)">查看</el-button>
                        <el-button type="text" @click="handleCopy(scope.row)">复制</el-button>
                        <el-button v-if="scope.row.status === 'DRAFT'" type="text"
                            @click="handleDelete(scope.row)">删除</el-button>
                        <el-button v-if="['EFFECTIVE', 'PASSED'].includes(scope.row.status)" type="text"
                            @click="handleStop(scope.row)">停止</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!-- Pagination -->
            <el-pagination :current-page.sync="searchForm.page" :page-size.sync="searchForm.limit"
                @size-change="handleSizeChange" @current-change="handleCurrentChange" :page-sizes="[50, 100, 200]"
                :hide-on-single-page="false" layout="total, sizes, prev, pager, next, jumper" :total="total">
            </el-pagination>

            <!-- 创建/修改场景弹框 -->
            <el-dialog :visible.sync="sceneDialogVisible" width="520px" :close-on-click-modal="false"
                @close="resetSceneForm" custom-class="scene-dialog">
                <div class="scene-dialog-header">
                    <h3 class="scene-dialog-title">{{ sceneDialogTitle }}</h3>
                    <el-button type="primary" size="small" @click="handleViewScenesFromDialog" class="view-scenes-btn">
                        <i class="el-icon-view"></i>
                        查看当前已有场景
                    </el-button>
                </div>
                <el-form :model="sceneForm" :rules="sceneRules" ref="sceneForm" label-width="100px" class="scene-form">
                    <el-form-item label="场景名称" prop="name" required>
                        <el-input v-model="sceneForm.name" placeholder="请输入场景名称，不会对用户展示" maxlength="30" show-word-limit
                            class="scene-name-field">
                        </el-input>
                    </el-form-item>
                    <el-form-item label="备注" prop="remark" class="remark-item">
                        <el-input v-model="sceneForm.remark" placeholder="请输入场景说明，不会用于展示" maxlength="50" show-word-limit
                            type="textarea" :rows="3" class="scene-remark-field">
                        </el-input>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="scene-dialog-footer">
                    <el-button @click="sceneDialogVisible = false" class="cancel-btn">
                        <i class="el-icon-close"></i>
                        取消
                    </el-button>
                    <el-button type="primary" @click="handleSceneSubmit" :loading="sceneSubmitting" class="confirm-btn">
                        <i class="el-icon-check" v-if="!sceneSubmitting"></i>
                        {{ sceneSubmitting ? '提交中...' : '确定' }}
                    </el-button>
                </div>
            </el-dialog>

            <!-- 查看当前已有场景弹框 -->
            <el-dialog title="当前已有场景" :visible.sync="scenesListDialogVisible" width="80%" :close-on-click-modal="false">
                <div class="scenes-list-container">
                    <el-table :data="scenesListData" v-loading="scenesListLoading" style="width: 100%">
                        <el-table-column prop="id" label="ID" width="80"></el-table-column>
                        <el-table-column prop="name" label="场景名称" width="200"></el-table-column>
                        <el-table-column prop="created_by_name" label="创建人" width="140px">
                            <template #default="scope">
                                <el-link :href="'/users/user-details?id=' + scope.row.created_by" type="primary"
                                    target="_blank" :underline="false">
                                    {{ scope.row.created_by_name }}
                                </el-link>
                            </template>
                        </el-table-column>
                        <el-table-column prop="created_at" label="创建时间" width="180"
                            :formatter="row => $formatDate(row.created_at)">
                        </el-table-column>
                        <el-table-column prop="updated_at" label="修改时间" width="180">
                            <template #default="scope">
                                {{ scope.row.updated_at ? $formatDate(scope.row.updated_at) : '-' }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="remark" label="备注" width="200">
                            <template #default="scope">
                                {{ scope.row.remark || '-' }}
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width="100" fixed="right">
                            <template #default="scope">
                                <el-button type="text" @click="handleEditScene(scope.row)">修改</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <div slot="footer" class="dialog-footer">
                    <el-button @click="scenesListDialogVisible = false">关闭</el-button>
                </div>
            </el-dialog>

            <!-- 推送失败用户弹窗 -->
            <el-dialog title="推送失败用户" :visible.sync="failedUsersDialogVisible" width="80%"
                :close-on-click-modal="false">
                <div class="failed-users-container">
                    <!-- 搜索区域 -->
                    <div class="search-area">
                        <el-form :inline="true" :model="failedUsersSearchForm" class="search-form">
                            <el-form-item label="用户ID">
                                <UserSearch v-model="failedUsersSearchForm.user_id" :refresh_method="searchFailedUsers"
                                    @change="searchFailedUsers">
                                </UserSearch>
                            </el-form-item>
                            <el-form-item>
                                <el-button type="primary" @click="searchFailedUsers">搜索</el-button>
                                <el-button @click="resetFailedUsersSearch">重置</el-button>
                            </el-form-item>
                        </el-form>
                    </div>

                    <!-- 失败用户表格 -->
                    <el-table :data="failedUsersData" v-loading="failedUsersLoading" stripe style="width: 100%">
                        <el-table-column prop="sequence" label="序号" width="80" align="center">
                            <template #default="scope">
                                {{ (failedUsersSearchForm.page - 1) * failedUsersSearchForm.limit + scope.$index + 1 }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="plan_id" label="推送ID" width="100"></el-table-column>
                        <el-table-column prop="title" label="推送名称" width="200" show-overflow-tooltip></el-table-column>
                        <el-table-column prop="user_id" label="用户ID" width="120">
                            <template #default="scope">
                                <el-link :href="'/users/user-details?id=' + scope.row.user_id" type="primary"
                                    target="_blank" :underline="false">
                                    {{ scope.row.user_id }}
                                </el-link>
                            </template>
                        </el-table-column>
                        <el-table-column prop="email" label="邮箱" width="200" show-overflow-tooltip></el-table-column>
                        <el-table-column prop="fail_reason" label="失败原因" width="150"></el-table-column>
                        <el-table-column prop="device_id" label="失败原因补充信息" min-width="300" show-overflow-tooltip>
                            <template #default="scope">
                                <span>
                                    <el-link :href="'/users/relation-list?user_id=' + scope.row.user_id" type="primary"
                                        target="_blank" :underline="false">
                                        设备值： {{ scope.row.device_id }}
                                    </el-link>
                                </span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="created_at" label="失败时间" width="180"
                            :formatter="row => $formatDate(row.created_at)">
                        </el-table-column>
                    </el-table>

                    <!-- 分页 -->
                    <el-pagination :current-page.sync="failedUsersSearchForm.page"
                        :page-size.sync="failedUsersSearchForm.limit" @size-change="handleFailedUsersPageSizeChange"
                        @current-change="handleFailedUsersPageChange" :page-sizes="[20, 50, 100]"
                        layout="total, sizes, prev, pager, next, jumper" :total="failedUsersTotal"
                        style="margin-top: 20px; text-align: center;">
                    </el-pagination>
                </div>

                <div slot="footer" class="dialog-footer">
                    <el-button @click="failedUsersDialogVisible = false">关闭</el-button>
                </div>
            </el-dialog>
        </el-main>
    </el-container>
</template>

<script>
import UserSearch from "@/components/user/UserSearch";
export default {
    name: 'RoutinePlan',
    components: {
        UserSearch
    },
    data() {
        return {
            searchForm: {
                scene_type: 'ROUTINE',
                plan_id: null,
                scene_id: null,
                group_id: null,
                business_party: null,
                status: null,
                page: 1,
                limit: 50,
            },
            scenes: {},
            customerGroups: {},
            businessParties: {},
            statuses: {},
            planIds: {},
            tableData: [],
            total: 0,
            // 失败用户弹窗相关数据
            failedUsersDialogVisible: false,
            failedUsersData: [],
            failedUsersTotal: 0,
            failedUsersLoading: false,
            currentPlanId: null,
            failedUsersSearchForm: {
                user_id: '',
                page: 1,
                limit: 20
            },
            // 场景管理相关数据
            sceneDialogVisible: false,
            sceneDialogTitle: '创建场景',
            sceneSubmitting: false,
            sceneForm: {
                id: null,
                name: '',
                remark: ''
            },
            sceneRules: {
                name: [
                    { required: true, message: '请输入场景名称', trigger: 'blur' },
                    { min: 1, max: 30, message: '场景名称长度在 1 到 30 个字符', trigger: 'blur' }
                ],
                remark: [
                    { max: 50, message: '备注长度不能超过 50 个字符', trigger: 'blur' }
                ]
            },
            // 场景列表弹窗相关数据
            scenesListDialogVisible: false,
            scenesListData: [],
            scenesListLoading: false
        }
    },
    created() {
        this.searchForm.plan_id = this.$route.query.plan_id;
        this.fetchData()
    },
    methods: {
        async fetchData() {
            try {
                // 过滤掉 null 值
                const params = { ...this.searchForm };
                Object.keys(params).forEach(key => {
                    if (params[key] === null || params[key] === undefined || params[key] === '') {
                        delete params[key];
                    }
                });
                console.log(params)
                const response = await this.fetchRoutinePlanList(params);
                this.tableData = response.items
                this.scenes = response.extra_data.scenes
                this.customerGroups = response.extra_data.customer_groups
                this.businessParties = response.extra_data.business_parties
                this.statuses = response.extra_data.statuses
                this.planIds = response.extra_data.plan_id_mapper
                this.total = response.total
            } catch (error) {
                console.log(error)
                this.$message.error('获取数据失败')
            }
        },
        formatName(name) {
            if (!name) return '';
            if (name.length <= 20) return name;
            return name.substring(0, 20) + '...';
        },
        formatCustomerGroupIds(ids) {
            if (!ids) return '';
            if (Array.isArray(ids)) {
                return ids.join('、');
            }
            return ids;
        },
        getEffectiveUsersCount(row) {
            if (row.status === 'ENDED') {
                return '已结束';
            }
            if (row.status === 'PENDING_REVIEW') {
                return '待审核';
            }
            return row.effective_users_count || 0;
        },
        handleSearch() {
            this.searchForm.page = 1
            this.fetchData()
        },
        handleReset() {
            this.searchForm = {
                plan_id: null,
                scene_id: null,
                group_id: null,
                business_party: null,
                status: null,
                page: 1,
                limit: 50
            }
            this.handleSearch()
        },
        handleCreateScene() {
            this.sceneDialogTitle = '创建场景';
            this.sceneForm = {
                id: null,
                name: '',
                remark: ''
            };
            this.sceneDialogVisible = true;
        },
        handleViewScenes() {
            this.scenesListDialogVisible = true;
            this.fetchScenesList();
        },
        handleEditScene(row) {
            this.sceneDialogTitle = '修改场景';
            this.sceneForm = {
                id: row.id,
                name: row.name,
                remark: row.remark || ''
            };
            this.sceneDialogVisible = true;
            this.scenesListDialogVisible = false;
        },
        async handleSceneSubmit() {
            this.$refs.sceneForm.validate(async (valid) => {
                if (valid) {
                    this.sceneSubmitting = true;
                    try {
                        if (this.sceneForm.id) {
                            // 修改场景
                            await this.updateScene({
                                id: this.sceneForm.id,
                                name: this.sceneForm.name,
                                remark: this.sceneForm.remark,
                                scene_type: 'ROUTINE'
                            });
                            this.$message.success('场景修改成功');
                        } else {
                            // 创建场景
                            await this.createScene({
                                name: this.sceneForm.name,
                                remark: this.sceneForm.remark,
                                scene_type: 'ROUTINE'
                            });
                            this.$message.success('场景创建成功');
                        }
                        this.sceneDialogVisible = false;
                        // 刷新场景列表和主页面数据
                        this.fetchScenesList();
                        this.fetchData();
                    } catch (error) {
                        this.$message.error(error.message || '操作失败');
                    } finally {
                        this.sceneSubmitting = false;
                    }
                }
            });
        },
        resetSceneForm() {
            this.$refs.sceneForm && this.$refs.sceneForm.resetFields();
            this.sceneForm = {
                id: null,
                name: '',
                remark: ''
            };
        },
        async fetchScenesList() {
            this.scenesListLoading = true;
            try {
                const response = await this.fetchScenesListAPI();
                this.scenesListData = response.items || [];
            } catch (error) {
                console.error('获取场景列表失败:', error);
                this.$message.error('获取场景列表失败');
                this.scenesListData = [];
            } finally {
                this.scenesListLoading = false;
            }
        },
        // API calls for scene management
        async createScene(data) {
            return this.$axios.post('/api/operation/mission/scenes', data).then(res => {
                return this.checkApiResponse(res.data);
            });
        },

        async updateScene(data) {
            return this.$axios.put(`/api/operation/mission/scenes`, data).then(res => {
                return this.checkApiResponse(res.data);
            });
        },

        async fetchScenesListAPI() {
            return this.$axios.get('/api/operation/mission/scenes').then(res => {
                return this.checkApiResponse(res.data);
            });
        },
        handleCreate() {
            const routeData = this.$router.resolve('/operation/mission_center/routine_plan_detail?id=0')
            window.open(routeData.href, '_blank')
        },
        handleView(row) {
            const routeData = this.$router.resolve(`/operation/mission_center/routine_plan_detail?id=${row.id}`)
            window.open(routeData.href, '_blank')
        },
        handleCopy(row) {
            this.$confirm('确认复制该推送任务？<br> (复制后会创建相同推送内容，需要重新提交审核)', '复制推送', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                dangerouslyUseHTMLString: true
            }).then(() => {
                const routeData = this.$router.resolve(`/operation/mission_center/routine_plan_detail?id=${row.id}&copy=true`)
                window.open(routeData.href, '_blank')
            }).catch(() => { })
        },
        handleDelete(row) {
            this.$confirm('确认删除该推送任务？<br> (删除后无法恢复。)', '删除推送', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                dangerouslyUseHTMLString: true
            }).then(async () => {
                try {
                    await this.deleteRoutinePlan(row.id)
                    this.$message.success('删除成功')
                    this.fetchData()
                } catch (error) {
                    this.$message.error('删除失败')
                }
            }).catch(() => { })
        },
        handleStop(row) {
            this.$confirm('确认停止该推送任务？<br> (停止后将不会新增推送用户，已推送的用户任务不受影响。)', '停止推送', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                dangerouslyUseHTMLString: true
            }).then(async () => {
                try {
                    await this.stopRoutinePlan(row.id)
                    this.$message.success('停止成功')
                    this.fetchData()
                } catch (error) {
                    this.$message.error(error.message || '停止失败')
                }
            }).catch(() => { })
        },
        handleSizeChange(val) {
            this.searchForm.limit = val
            this.fetchData()
        },
        handleCurrentChange(val) {
            this.searchForm.page = val
            this.fetchData()
        },
        checkApiResponse(res) {
            if (res.code !== 0) {
                throw new Error(res.message || '操作失败')
            }
            return res.data
        },
        // API calls
        async fetchRoutinePlanList(params) {
            return this.$axios.get('/api/operation/mission/routine', {
                params: params
            }).then(res => {
                return this.checkApiResponse(res.data)
            })
        },
        async deleteRoutinePlan(id) {
            return this.$axios.delete(`/api/operation/mission/${id}`).then(res => {
                return this.checkApiResponse(res.data)
            })
        },
        async stopRoutinePlan(id) {
            return this.$axios.post(`/api/operation/mission/${id}/stop`).then(res => {
                return this.checkApiResponse(res.data)
            })
        },

        // 显示失败用户弹窗
        showFailedUsers(row) {
            if (row.failed_count <= 0) {
                return;
            }
            this.currentPlanId = row.id;
            this.failedUsersDialogVisible = true;
            this.resetFailedUsersSearch();
            this.fetchFailedUsers();
        },

        // 获取失败用户数据
        async fetchFailedUsers() {
            if (!this.currentPlanId) return;

            this.failedUsersLoading = true;
            try {
                const params = {
                    ...this.failedUsersSearchForm,
                    plan_id: this.currentPlanId
                };

                // 过滤掉空值
                Object.keys(params).forEach(key => {
                    if (params[key] === null || params[key] === '') {
                        delete params[key];
                    }
                });

                const response = await this.fetchFailedUsersList(params);
                this.failedUsersData = response.items || [];
                this.failedUsersTotal = response.total || 0;
            } catch (error) {
                console.error('获取失败用户数据失败:', error);
                this.$message.error('获取失败用户数据失败');
                this.failedUsersData = [];
                this.failedUsersTotal = 0;
            } finally {
                this.failedUsersLoading = false;
            }
        },

        // 搜索失败用户
        searchFailedUsers() {
            this.failedUsersSearchForm.page = 1;
            this.fetchFailedUsers();
        },

        // 重置失败用户搜索
        resetFailedUsersSearch() {
            this.failedUsersSearchForm = {
                user_id: '',
                page: 1,
                limit: 20
            };
            if (this.failedUsersDialogVisible) {
                this.fetchFailedUsers();
            }
        },

        // 失败用户分页大小改变
        handleFailedUsersPageSizeChange(val) {
            this.failedUsersSearchForm.limit = val;
            this.failedUsersSearchForm.page = 1;
            this.fetchFailedUsers();
        },

        // 失败用户页码改变
        handleFailedUsersPageChange(val) {
            this.failedUsersSearchForm.page = val;
            this.fetchFailedUsers();
        },

        // API: 获取失败用户列表
        async fetchFailedUsersList(params) {
            return this.$axios.get('/api/operation/mission/failed-users', {
                params: params
            }).then(res => {
                return this.checkApiResponse(res.data);
            });
        },

        handleViewScenesFromDialog() {
            // this.sceneDialogVisible = false;
            this.handleViewScenes();
        },
    }
}
</script>

<style scoped>
.search-instructions {
    color: #666;
    font-size: 14px;
    margin-bottom: 15px;
    font-family: 'Microsoft YaHei', Arial, sans-serif;
}

.search-form {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.search-form .form-inline {
    flex: 1;
}

.create-buttons {
    display: flex;
    gap: 10px;
    margin-left: 20px;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* 表格列样式 */
.scene-column {
    background-color: #fff5f5;
}

.customer-group-column {
    background-color: #fff5f5;
}

.effective-users-column {
    background-color: #fff5f5;
}

.failed-count-link:hover {
    text-decoration: underline;
}

.scenes-list-container {
    max-height: 500px;
    overflow-y: auto;
}

/* 场景弹框样式优化 */
.scene-dialog {
    .el-dialog__header {
        display: none;
        /* 隐藏默认标题 */
    }

    .el-dialog__body {
        padding: 0;
    }

    .el-dialog__footer {
        padding: 0;
    }
}

.scene-dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 24px 24px 0 24px;
}

.scene-dialog-title {
    font-size: 20px;
    font-weight: 600;
    color: #303133;
    margin: 0;
    font-family: 'Microsoft YaHei', Arial, sans-serif;
}

.view-scenes-btn {
    font-size: 13px;
    padding: 8px 16px;
    height: 32px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(64, 158, 255, 0.1);
}

.view-scenes-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(64, 158, 255, 0.2);
}

.view-scenes-btn i {
    margin-right: 4px;
    font-size: 14px;
}

.scene-form {
    padding: 0 24px;

    .el-form-item {
        margin-bottom: 24px;
    }

    .el-form-item__label {
        font-weight: 500;
        color: #606266;
        font-size: 14px;
        line-height: 1.5;
    }

    .el-form-item__label::before {
        content: '*';
        color: #f56c6c;
        margin-right: 4px;
        font-weight: bold;
    }

    .el-form-item__label:not(.is-required)::before {
        display: none;
    }

    .remark-item .el-form-item__label::before {
        display: none;
    }

    .el-form-item__content {
        line-height: 1.5;
    }
}

.scene-name-field {
    .el-input__inner {
        height: 40px;
        line-height: 40px;
        border-radius: 6px;
        border: 1px solid #dcdfe6;
        font-size: 14px;
        transition: all 0.3s ease;
        padding: 0 12px;
    }

    .el-input__inner:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
    }

    .el-input__inner:hover {
        border-color: #c0c4cc;
    }
}

.scene-remark-field {
    .el-textarea__inner {
        border-radius: 6px;
        border: 1px solid #dcdfe6;
        font-size: 14px;
        transition: all 0.3s ease;
        padding: 12px;
        resize: vertical;
        min-height: 80px;
    }

    .el-textarea__inner:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
    }

    .el-textarea__inner:hover {
        border-color: #c0c4cc;
    }
}

.scene-dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 20px 24px 24px 24px;
    border-top: 1px solid #f0f0f0;
    margin-top: 8px;
    background-color: #fafafa;
}

.cancel-btn {
    width: 88px;
    height: 36px;
    background-color: #ffffff;
    color: #606266;
    border: 1px solid #dcdfe6;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cancel-btn:hover {
    background-color: #f5f7fa;
    border-color: #c0c4cc;
    color: #409eff;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.cancel-btn i {
    margin-right: 4px;
    font-size: 14px;
}

.confirm-btn {
    width: 88px;
    height: 36px;
    background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
    border: none;
    color: #ffffff;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
}

.confirm-btn:hover {
    background: linear-gradient(135deg, #66b1ff 0%, #85c1ff 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(64, 158, 255, 0.3);
}

.confirm-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
}

.confirm-btn i {
    margin-right: 4px;
    font-size: 14px;
}

/* 字数统计样式优化 */
.el-input__count {
    color: #909399;
    font-size: 12px;
    font-weight: 500;
}

/* 输入框聚焦时的字数统计颜色 */
.scene-name-field:focus-within .el-input__count,
.scene-remark-field:focus-within .el-input__count {
    color: #409eff;
}

/* 加载状态优化 */
.confirm-btn.is-loading {
    background: linear-gradient(135deg, #a0cfff 0%, #b3d8ff 100%);
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 2px 4px rgba(64, 158, 255, 0.1);
}

/* 响应式优化 */
@media (max-width: 768px) {
    .scene-dialog {
        width: 90% !important;
        margin: 5vh auto;
    }

    .scene-dialog-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .view-scenes-btn {
        align-self: flex-end;
    }

    .scene-form {
        padding: 0 16px;
    }

    .scene-dialog-footer {
        padding: 16px;
        flex-direction: column;
    }

    .cancel-btn,
    .confirm-btn {
        width: 100%;
    }
}

/* 动画效果 */
.scene-dialog {
    animation: dialogFadeIn 0.3s ease-out;
}

@keyframes dialogFadeIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }

    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* 表单验证样式优化 */
.scene-form .el-form-item.is-error .el-input__inner,
.scene-form .el-form-item.is-error .el-textarea__inner {
    border-color: #f56c6c;
    box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.1);
}

.scene-form .el-form-item.is-error .el-input__inner:focus,
.scene-form .el-form-item.is-error .el-textarea__inner:focus {
    border-color: #f56c6c;
    box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2);
}

/* 占位符样式优化 */
.scene-name-field .el-input__inner::placeholder,
.scene-remark-field .el-textarea__inner::placeholder {
    color: #c0c4cc;
    font-size: 13px;
}

/* 场景列表弹框样式优化 */
.scenes-list-container {
    max-height: 60vh;
    overflow-y: auto;
    border-radius: 6px;
    border: 1px solid #e4e7ed;
}

.scenes-list-container .el-table {
    border-radius: 6px;
    overflow: hidden;
}

.scenes-list-container .el-table th {
    background-color: #fafafa;
    font-weight: 600;
    color: #606266;
}

.scenes-list-container .el-table td {
    padding: 12px 0;
}

/* 链接样式优化 */
.scenes-list-container .el-link {
    font-weight: 500;
    transition: color 0.3s ease;
}

.scenes-list-container .el-link:hover {
    color: #66b1ff !important;
}
</style>