<template>
    <el-container>
        <el-main>
            <h2 style="font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif">
                新用户任务推送
            </h2>

            <!-- Search Form -->
            <div class="search-form">
                <el-form :inline="true" :model="searchForm" class="form-inline">
                    <el-form-item label="推送ID">
                        <el-select v-model="searchForm.plan_id" placeholder="推送ID" clearable filterable
                            @clear="delete searchForm.plan_id">
                            <el-option value="" v-for="k, v in planIds" :key="k" :value="v" :label="k"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="业务方">
                        <el-select v-model="searchForm.business_party" placeholder="业务方" clearable filterable>
                            <el-option label="全部" value="" v-for="k, v in business_parties" :key="v" :value="v"
                                :label="k"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="推送圈群">
                        <el-select v-model="searchForm.channel" placeholder="推送圈群" clearable>
                            <el-option label="全部" value="" v-for="k, v in channels" :key="v" :value="v"
                                :label="k"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="状态">
                        <el-select v-model="searchForm.status" placeholder="状态" clearable>
                            <el-option value="" v-for="k, v in statuses" :key="v" :value="v" :label="k"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="handleSearch">搜索</el-button>
                        <el-button @click="handleReset">重置</el-button>
                    </el-form-item>
                </el-form>
                <el-button type="primary" class="create-btn" @click="handleCreate">+ 创建任务推送</el-button>
            </div>

            <!-- Table -->
            <el-table :data="tableData" style="width: 100%">
                <el-table-column prop="id" label="推送ID"></el-table-column>
                <el-table-column prop="type" label="推送场景">
                    <template #default>新用户任务</template>
                </el-table-column>
                <el-table-column prop="name" label="推送名称" width="280">
                    <template #default="scope">
                        <el-tooltip :content="scope.row.name" placement="top" :disabled="scope.row.name.length <= 20">
                            <span>{{ formatName(scope.row.name) }}</span>
                        </el-tooltip>
                    </template>
                </el-table-column>
                <el-table-column prop="business_party" label="业务方"></el-table-column>
                <el-table-column prop="mission_count" label="推送任务数量"></el-table-column>
                <el-table-column prop="channel" label="推送圈群"></el-table-column>
                <el-table-column label="推送成功/失败/总人数" width="150px">
                    <template #default="scope">
                        {{ scope.row.success_count }}
                        /
                        <span @click="showFailedUsers(scope.row)"
                            :class="scope.row.failed_count > 0 ? 'failed-count-link' : ''"
                            :style="scope.row.failed_count > 0 ? 'cursor: pointer; color: #409EFF' : ''">
                            {{ scope.row.failed_count }}
                        </span>
                        /
                        <el-link :href="`/operation/mission_center/newbie_mission?plan_id=${scope.row.id}`"
                            type="primary" target="_blank" :underline="false">
                            {{ scope.row.total }}
                        </el-link>
                    </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" :formatter="(row) => statuses[row.status]">
                </el-table-column>
                <el-table-column prop="created_by_name" label="创建人">
                    <template #default="scope">
                        <el-link :href="'/users/user-details?id=' + scope.row.created_by" type="primary" target="_blank"
                            :underline="false"
                            style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                            {{ scope.row.created_by_name }}
                        </el-link>
                    </template>
                </el-table-column>
                <el-table-column prop="auditor_by_name" label="审核人" width="120">
                    <template #default="scope">
                        <el-link :href="'/users/user-details?id=' + scope.row.auditor_by" type="primary" target="_blank"
                            :underline="false"
                            style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                            {{ scope.row.auditor_by_name }}
                        </el-link>
                    </template>
                </el-table-column>
                <el-table-column prop="updated_at" label="更新时间"
                    :formatter="row => $formatDate(row.updated_at)"></el-table-column>
                <el-table-column label="操作" width="200" fixed="right">
                    <template #default="scope">
                        <el-button type="text" @click="handleView(scope.row)">查看</el-button>
                        <el-button type="text" @click="handleCopy(scope.row)">复制</el-button>
                        <el-button v-if="scope.row.status === 'DRAFT'" type="text"
                            @click="handleDelete(scope.row)">删除</el-button>
                        <el-button v-if="['EFFECTIVE', 'PASSED'].includes(scope.row.status)" type="text"
                            @click="handleStop(scope.row)">停止</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!-- Pagination -->
            <el-pagination :current-page.sync="searchForm.page" :page-size.sync="searchForm.limit"
                @size-change="handleSizeChange" @current-change="handleCurrentChange" :page-sizes="[50, 100, 200]"
                :hide-on-single-page="false" layout="total, sizes, prev, pager, next, jumper" :total="total">
            </el-pagination>

            <!-- 推送失败用户弹窗 -->
            <el-dialog title="推送失败用户" :visible.sync="failedUsersDialogVisible" width="80%"
                :close-on-click-modal="false">
                <div class="failed-users-container">
                    <!-- 搜索区域 -->
                    <div class="search-area">
                        <el-form :inline="true" :model="failedUsersSearchForm" class="search-form">
                            <el-form-item label="用户ID">
                                <UserSearch v-model="failedUsersSearchForm.user_id" :refresh_method="searchFailedUsers"
                                    @change="searchFailedUsers">
                                </UserSearch>
                            </el-form-item>
                            <el-form-item>
                                <el-button type="primary" @click="searchFailedUsers">搜索</el-button>
                                <el-button @click="resetFailedUsersSearch">重置</el-button>
                            </el-form-item>
                        </el-form>
                    </div>

                    <!-- 失败用户表格 -->
                    <el-table :data="failedUsersData" v-loading="failedUsersLoading" stripe style="width: 100%">
                        <el-table-column prop="sequence" label="序号" width="80" align="center">
                            <template #default="scope">
                                {{ (failedUsersSearchForm.page - 1) * failedUsersSearchForm.limit + scope.$index + 1 }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="plan_id" label="推送ID" width="100"></el-table-column>
                        <el-table-column prop="title" label="推送名称" width="200" show-overflow-tooltip></el-table-column>
                        <el-table-column prop="user_id" label="用户ID" width="120">
                            <template #default="scope">
                                <el-link :href="'/users/user-details?id=' + scope.row.user_id" type="primary"
                                    target="_blank" :underline="false">
                                    {{ scope.row.user_id }}
                                </el-link>
                            </template>
                        </el-table-column>
                        <el-table-column prop="email" label="邮箱" width="200" show-overflow-tooltip></el-table-column>
                        <el-table-column prop="fail_reason" label="失败原因" width="150"></el-table-column>
                        <el-table-column prop="device_id" label="失败原因补充信息" min-width="300" show-overflow-tooltip>
                            <template #default="scope">
                                <span>
                                    <el-link :href="'/users/relation-list?user_id=' + scope.row.user_id" type="primary"
                                        target="_blank" :underline="false">
                                        设备值： {{ scope.row.device_id }}
                                    </el-link>
                                </span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="created_at" label="失败时间" width="180"
                            :formatter="row => $formatDate(row.created_at)">
                        </el-table-column>
                    </el-table>

                    <!-- 分页 -->
                    <el-pagination :current-page.sync="failedUsersSearchForm.page"
                        :page-size.sync="failedUsersSearchForm.limit" @size-change="handleFailedUsersPageSizeChange"
                        @current-change="handleFailedUsersPageChange" :page-sizes="[20, 50, 100]"
                        layout="total, sizes, prev, pager, next, jumper" :total="failedUsersTotal"
                        style="margin-top: 20px; text-align: center;">
                    </el-pagination>
                </div>

                <div slot="footer" class="dialog-footer">
                    <el-button @click="failedUsersDialogVisible = false">关闭</el-button>
                </div>
            </el-dialog>
        </el-main>
    </el-container>
</template>

<script>
import UserSearch from "@/components/user/UserSearch";
export default {
    name: 'NewbiePlan',
    components: {
        UserSearch
    },
    data() {
        return {
            searchForm: {
                plan_id: null,
                channel: null,
                status: null,
                page: 1,
                limit: 50,
            },
            channels: {},
            statuses: {},
            planIds: {},
            business_parties: {},
            tableData: [],
            total: 0,
            total: 0,
            // 失败用户弹窗相关数据
            failedUsersDialogVisible: false,
            failedUsersData: [],
            failedUsersTotal: 0,
            failedUsersLoading: false,
            currentPlanId: null,
            failedUsersSearchForm: {
                user_id: '',
                page: 1,
                limit: 20
            }
        }
    },
    created() {
        this.searchForm.plan_id = this.$route.query.plan_id;
        this.fetchData()
    },
    methods: {
        async fetchData() {
            try {
                // 过滤掉 null 值
                const params = { ...this.searchForm };
                Object.keys(params).forEach(key => {
                    if (params[key] === null) {
                        delete params[key];
                    }
                });

                const response = await this.fetchNewbiePlanList(params);
                this.tableData = response.items
                this.channels = response.channels
                this.statuses = response.statuses
                this.planIds = response.plan_id_mapper
                this.total = response.total
                this.business_parties = response.business_parties
            } catch (error) {
                console.log(error)
                this.$message.error('获取数据失败')
            }
        },
        formatName(name) {
            if (!name) return '';
            if (name.length <= 20) return name;
            return name.substring(0, 20) + '...';
        },
        handleSearch() {
            this.searchForm.page = 1
            this.fetchData()
        },
        handleReset() {
            this.searchForm = {
                plan_id: null,
                channel: null,
                status: null,
                page: 1,
                limit: 50
            }
            this.handleSearch()
        },
        handleCreate() {
            const routeData = this.$router.resolve('/operation/mission_center/newbie_plan_detail?id=0')
            window.open(routeData.href, '_blank')
        },
        handleView(row) {
            const routeData = this.$router.resolve(`/operation/mission_center/newbie_plan_detail?id=${row.id}`)
            window.open(routeData.href, '_blank')
        },
        handleCopy(row) {
            this.$confirm('确认复制该推送任务？<br> (复制后会创建相同推送内容，需要重新提交审核)', '复制推送', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                dangerouslyUseHTMLString: true
            }).then(() => {
                const routeData = this.$router.resolve(`/operation/mission_center/newbie_plan_detail?id=${row.id}&copy=true`)
                window.open(routeData.href, '_blank')
            }).catch(() => { })
        },
        handleDelete(row) {
            this.$confirm('确认删除该推送任务？<br> (删除后无法恢复。)', '删除推送', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                dangerouslyUseHTMLString: true
            }).then(async () => {
                try {
                    await this.deleteNewbiePlan(row.id)
                    this.$message.success('删除成功')
                    this.fetchData()
                } catch (error) {
                    this.$message.error('删除失败')
                }
            }).catch(() => { })
        },
        handleStop(row) {
            this.$confirm('确认停止该推送任务？<br> (停止后将不会新增推送用户，已推送的用户任务不受影响。)', '停止推送', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                dangerouslyUseHTMLString: true
            }).then(async () => {
                try {
                    await this.stopNewbiePlan(row.id)
                    this.$message.success('停止成功')
                    this.fetchData()
                } catch (error) {
                    this.$message.error(error.message || '停止失败')
                }
            }).catch(() => { })
        },
        handleDialogConfirm() {
            if (this.dialogCallback) {
                this.dialogCallback()
            }
            this.dialogVisible = false
        },
        handleSizeChange(val) {
            this.searchForm.limit = val
            this.fetchData()
        },
        handleCurrentChange(val) {
            this.searchForm.page = val
            this.fetchData()
        },
        checkApiResponse(res) {
            if (res.code !== 0) {
                throw new Error(res.message || '操作失败')
            }
            return res.data
        },
        // API calls
        async fetchNewbiePlanList(params) {
            return this.$axios.get('/api/operation/mission', {
                params: params
            }).then(res => {
                return this.checkApiResponse(res.data)
            })
        },
        async deleteNewbiePlan(id) {
            return this.$axios.delete(`/api/operation/mission/${id}`).then(res => {
                return this.checkApiResponse(res.data)
            })
        },
        async stopNewbiePlan(id) {
            return this.$axios.post(`/api/operation/mission/${id}/stop`).then(res => {
                return this.checkApiResponse(res.data)
            })
        },

        // 显示失败用户弹窗
        showFailedUsers(row) {
            if (row.failed_count <= 0) {
                return;
            }
            this.currentPlanId = row.id;
            this.failedUsersDialogVisible = true;
            this.resetFailedUsersSearch();
            this.fetchFailedUsers();
        },

        // 获取失败用户数据
        async fetchFailedUsers() {
            if (!this.currentPlanId) return;

            this.failedUsersLoading = true;
            try {
                const params = {
                    ...this.failedUsersSearchForm,
                    plan_id: this.currentPlanId
                };

                // 过滤掉空值
                Object.keys(params).forEach(key => {
                    if (params[key] === null || params[key] === '') {
                        delete params[key];
                    }
                });

                const response = await this.fetchFailedUsersList(params);
                this.failedUsersData = response.items || [];
                this.failedUsersTotal = response.total || 0;
            } catch (error) {
                console.error('获取失败用户数据失败:', error);
                this.$message.error('获取失败用户数据失败');
                this.failedUsersData = [];
                this.failedUsersTotal = 0;
            } finally {
                this.failedUsersLoading = false;
            }
        },

        // 搜索失败用户
        searchFailedUsers() {
            this.failedUsersSearchForm.page = 1;
            this.fetchFailedUsers();
        },

        // 重置失败用户搜索
        resetFailedUsersSearch() {
            this.failedUsersSearchForm = {
                user_id: '',
                page: 1,
                limit: 20
            };
            if (this.failedUsersDialogVisible) {
                this.fetchFailedUsers();
            }
        },

        // 失败用户分页大小改变
        handleFailedUsersPageSizeChange(val) {
            this.failedUsersSearchForm.limit = val;
            this.failedUsersSearchForm.page = 1;
            this.fetchFailedUsers();
        },

        // 失败用户页码改变
        handleFailedUsersPageChange(val) {
            this.failedUsersSearchForm.page = val;
            this.fetchFailedUsers();
        },

        // API: 获取失败用户列表
        async fetchFailedUsersList(params) {
            return this.$axios.get('/api/operation/mission/failed-users', {
                params: params
            }).then(res => {
                return this.checkApiResponse(res.data);
            });
        },

    }
}
</script>

<style scoped>
.search-form {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.search-form .form-inline {
    flex: 1;
}

.search-form .create-btn {
    margin-left: 20px;
}


.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}
</style>
