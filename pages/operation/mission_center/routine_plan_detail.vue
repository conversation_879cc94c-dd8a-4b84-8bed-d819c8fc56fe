<template>
    <div class="task-push-container">
        <h1>{{ isEditMode ? '查看任务推送' : '创建任务推送' }}</h1>
        <div>
            推送状态: <span style="color: red; font-weight: bold; font-size: 20px;">{{ pushSetting.status === "DRAFT" ?
                '待提交' : pushStatus }}</span>
        </div>
        <div class="action-buttons">
            <el-button @click="goBack">返回</el-button>

            <el-button @click="saveAsDraft" v-if="!editDisabled">保存</el-button>

            <el-button type="primary" @click="submitForReview" v-if="!editDisabled && !copy">提交审核</el-button>

            <el-button v-if="canReview" @click="updatePlanReviewStatus('REJECTED')">审核不通过</el-button>
            <el-button type="primary" @click="updatePlanReviewStatus('PASSED')" v-if="canReview">审核通过</el-button>
        </div>

        <!-- 推送设置 -->
        <el-card shadow="never" class="section-card">
            <div slot="header" class="card-header">
                <span>推送设置</span>
                <i class="el-icon-arrow-down"></i>
            </div>
            <el-form label-width="120px" ref="pushSettingForm" :model="pushSetting" :rules="pushSettingRules"
                :disabled="editDisabled">
                <el-form-item label="推送场景：" prop="scene_id">
                    <el-select v-model="pushSetting.scene_id" placeholder="请选择场景" filterable>
                        <el-option v-for="item in sortedScenes" :key="item.key" :label="item.value" :value="item.key" />
                    </el-select>
                </el-form-item>

                <el-form-item label="推送名称：" prop="name" style="width: 60%;">
                    <el-input v-model="pushSetting.name" placeholder="请输入名称，不会对用户展示" maxlength="50" show-word-limit />
                </el-form-item>

                <el-form-item label="业务方：" prop="business_party">
                    <el-select v-model="pushSetting.business_party" placeholder="请选择业务方">
                        <el-option v-for="(value, key) in BusinessParties" :key="key" :label="value" :value="key">
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="推送用户：" required>
                    <el-radio-group v-model="userType" disabled>
                        <el-radio label="someUser">部分用户</el-radio>
                    </el-radio-group>

                    <!-- 分群选择区域 -->
                    <div class="user-group-section">
                        <el-button type="primary" @click="handleUserGroup" :disabled="editDisabled">选择客群</el-button>

                        <!-- 已选择的分群列表 -->
                        <div v-for="(item, index) in tagGroups" :key="index" class="selected-group-item">
                            <div class="group-info">
                                <span class="group-label">• 分群信息</span>
                                <span class="group-name">{{ item.name }}</span>
                                <span class="group-count">{{ item.user_count }}人</span>
                                <span class="group-id">ID: {{ item.id }}</span>
                                <el-button @click="deleteTagGroup(item, index)" type="danger" size="mini"
                                    :disabled="editDisabled">删除</el-button>
                            </div>
                        </div>

                        <!-- 免打扰客群设置 -->
                        <div class="whitelist-section">
                            <el-checkbox v-model="pushSetting.whitelist_enabled"
                                :disabled="editDisabled">是否剔除免打扰客群</el-checkbox>
                            <div v-if="pushSetting.whitelist_enabled" class="whitelist-input">
                                <el-input v-model="pushSetting.whitelist_user_ids" placeholder="请输入免打扰客群"
                                    :disabled="editDisabled" @change="updateUserCount"></el-input>
                                <el-tooltip content="上传" placement="right" :open-delay="500" :hide-after="2000">
                                    <el-upload ref="upload" action="/api/operation/email-push/imports" name="file"
                                        :data="{ value_type: 'user' }"
                                        :headers="{ AUTHORIZATION: $cookies.get('admin_token') }"
                                        :before-upload="before_upload" :on-success="(res) => upload_success(res)"
                                        :on-error="upload_error" :show-file-list="false">
                                        <el-button type="primary" icon="el-icon-upload" circle size="mini"
                                            :disabled="editDisabled"></el-button>
                                    </el-upload>
                                </el-tooltip>
                                <el-button type="primary" icon="el-icon-download" circle size="mini"
                                    style="margin-left: 10px" @click="download_template"
                                    :disabled="editDisabled"></el-button>
                            </div>
                        </div>


                        <!-- 人数统计 -->
                        <div class="user-count-section">
                            <div class="count-info">
                                <span class="count-label">当前分群人数统计: {{ totalUserCount }}</span>
                                <el-button type="primary" icon="el-icon-download" circle size="mini"
                                    :disabled="editDisabled" @click="downloadUser"></el-button>
                            </div>

                            <!-- 检查客群接收情况按钮 -->
                            <div class="reception-check-section">
                                <el-button type="primary" @click="checkUserReception"
                                    :disabled="editDisabled || !tagGroups.length" :loading="receptionCheckLoading">
                                    检查客群接收情况
                                    <el-badge v-if="!editDisabled && unreachableCount > 0" :value="unreachableCount"
                                        class="item" />
                                </el-button>
                            </div>

                            <!-- 接收情况统计弹窗 -->
                            <el-dialog title="客群接收情况统计" :visible.sync="receptionDialogVisible" width="600px"
                                :close-on-click-modal="false">
                                <div class="reception-stats">
                                    <div class="stats-item">
                                        <span class="stats-label">可接收用户：</span>
                                        <span class="stats-value success">{{ reachableCount }}人</span>
                                    </div>
                                    <div class="stats-item">
                                        <span class="stats-label">无法接收用户：</span>
                                        <span class="stats-value danger">{{ unreachableCount }}人</span>
                                    </div>
                                </div>

                                <div class="reception-actions">
                                    <el-button type="primary" @click="downloadUnreachableUsers"
                                        :disabled="unreachableCount === 0" :loading="downloadLoading">
                                        下载无法触达用户信息
                                    </el-button>
                                </div>

                                <div slot="footer" class="dialog-footer">
                                    <el-button @click="receptionDialogVisible = false">关闭</el-button>
                                </div>
                            </el-dialog>
                        </div>
                    </div>
                </el-form-item>

                <el-form-item label="推送人数：">
                    <el-input-number v-model="pushSetting.total" placeholder="不填则无上限" :min="0" :precision="0"
                        @change="handleTotalChange">
                    </el-input-number>

                    <span v-if="pushSetting.total === 0" style="margin-left: 10px; color: #909399;">无上限</span>
                </el-form-item>

                <el-form-item label="推送时间：" prop="start_at">
                    <div class="time-picker">
                        <el-date-picker v-model="pushSetting.start_at" type="datetime" format="yyyy-MM-dd HH:mm:ss"
                            value-format="timestamp" :picker-options="pickerOptions" placeholder="开始时间 (UTC+8)"
                            @change="handleStartTimeChange" />
                        <span class="separator">-</span>
                        <el-date-picker v-model="pushSetting.end_at" type="datetime" format="yyyy-MM-dd HH:mm:ss"
                            value-format="timestamp" :picker-options="pickerOptions" placeholder="结束时间" />
                    </div>
                </el-form-item>
            </el-form>
        </el-card>

        <!-- 任务设置 -->
        <el-card shadow="never" class="section-card">
            <div slot="header" class="card-header">
                <span>任务设置</span>
                <i class="el-icon-arrow-down"></i>
            </div>
            <el-button type="primary" class="add-task-btn" @click="addTask" :disabled="editDisabled">添加任务</el-button>

            <!-- 任务列表 -->
            <draggable v-model="tasks" handle=".drag-handle" @start="dragStart" @end="dragEnd" :disabled="editDisabled">
                <transition-group>
                    <div v-for="(task, index) in tasks" :key="index" class="task-item">
                        <div class="task-header">
                            <div class="task-header-left">
                                <i class="el-icon-rank drag-handle"></i>
                                <span class="task-number">任务{{ task.sequence }}</span>
                                <span v-if="task.id" class="task-id">任务ID: {{ task.id || '-' }}</span>
                            </div>
                            <el-button type="danger" circle icon="el-icon-close" size="mini" @click="removeTask(index)"
                                :disabled="editDisabled"></el-button>
                        </div>
                        <el-form :ref="'taskForm' + index" :model="task" :rules="taskRules" label-width="120px"
                            :disabled="editDisabled">
                            <el-form-item label="任务类型：" prop="mission_condition">
                                <el-select v-model="task.mission_condition" placeholder="请选择"
                                    @change="handleMissionConditionChange(task)">
                                    <el-option v-for="(label, value) in MissionCondition" :key="value" :label="label"
                                        :value="value">
                                    </el-option>
                                </el-select>
                            </el-form-item>

                            <el-form-item label="任务条件：" prop="logic_params">
                                <div class="condition-row">
                                    <el-input :value="getConditionLabel(task.mission_condition)" disabled
                                        class="condition-label">
                                    </el-input>

                                    <el-select class="condition-operator" :value="`>=`" disabled
                                        v-if="task.mission_condition != 'COPY_TRADING_ONCE' && task.mission_condition != 'DEMO_TRADING_ONCE'">
                                        <el-option label=">=" value=">="></el-option>
                                    </el-select>

                                    <el-input-number v-model="task.logic_params[task.mission_condition]"
                                        class="condition-value" :min="0" :precision="0"
                                        v-if="task.mission_condition != 'COPY_TRADING_ONCE' && task.mission_condition != 'DEMO_TRADING_ONCE'"></el-input-number>
                                </div>
                            </el-form-item>

                            <el-form-item label="任务周期：" prop="deadline_days">
                                <el-input-number v-model="task.deadline_days" placeholder="请输入有效天数" class="cycle-input"
                                    :min="1" :precision="0"></el-input-number>
                                <span class="unit-text">日</span>
                            </el-form-item>

                            <!-- 奖励设置 -->
                            <el-form-item label="奖励类型：" prop="reward_type">
                                <el-radio-group v-model="task.reward_type" @change="() => task.equity_id = null">
                                    <el-radio label="AIRDROP">空投奖励</el-radio>
                                    <el-radio label="CASHBACK">权益奖励</el-radio>
                                </el-radio-group>
                            </el-form-item>

                            <!-- 奖励内容 -->
                            <el-form-item prop="reward_content" v-if="task.reward_type === 'AIRDROP'">
                                <el-select v-model="task.asset" placeholder="选择币种" class="token-select">
                                    <el-option label="CET" value="CET"></el-option>
                                </el-select>

                                <el-input-number v-model="task.amount" placeholder="数量" class="token-amount"
                                    :precision="0" :min="0"></el-input-number>

                                <span class="unit-text">个</span>
                            </el-form-item>
                            <el-form-item v-else prop="reward_content">
                                <el-select v-model="selectedReward" placeholder="请选择">
                                    <el-option label="手续费返现" value="手续费返现" key="手续费返现">手续费返现</el-option>
                                </el-select>
                                <el-select v-model="task.equity_id" placeholder="选择权益类型" class="equity-select"
                                    filterable clearable>
                                    <el-option v-for="(value, key) in equityOptions" :key="key" :label="value"
                                        :value="key">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-form>
                    </div>
                </transition-group>
            </draggable>
        </el-card>

        <!-- 分群选择对话框 -->
        <UserTagGroupDialog :tagGroups="tagGroups" :dialogUserGroupMap="dialogUserGroupMap"></UserTagGroupDialog>
    </div>
</template>

<script>
import draggable from 'vuedraggable';
import UserTagGroupDialog from "@/components/UserTagGroupDialog";
// 添加常量定义
const SceneType = {};
const MissionCondition = {};

export default {
    components: {
        draggable,
        UserTagGroupDialog
    },
    provide() {
        return { testSendFunc: null };
    },
    data() {
        return {
            missionPlanId: this.$route.query.id || 0,
            copy: this.$route.query.copy === 'true' || false,
            isEditMode: false,
            editDisabled: false,
            canReview: false,
            languages: {},
            pushStatus: "",
            pushSetting: {
                scene_type: "ROUTINE",
                business_party: null,
                name: '',
                total: null,
                start_at: null,
                end_at: null,
                whitelist_enabled: false,
                whitelist_user_ids: "",
            },
            tasks: [],
            BusinessParties: {},
            Scenes: {},
            isDragging: false,
            equityOptions: {},
            // 添加常量到组件中使其可在模板中使用
            SceneType,
            MissionCondition,
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() < Date.now() - 24 * 60 * 60 * 1000;
                },
            },
            // 分群相关数据
            tagGroups: [],
            dialogUserGroupMap: {
                dialogUserGroup: false,
                dialogUserGroupTotal: 0,
                dialogUserGroupItems: [],
                group_types: {},
                dialogUserGroupLoading: false,
                dialogUserGroupFilters: {
                    name: null,
                    page: null,
                    limit: 10,
                },
            },
            pushSettingRules: {
                scene_id: [
                    { required: true, message: '请选择推送场景', trigger: 'change' }
                ],
                business_party: [
                    { required: true, message: '请选择业务方', trigger: 'change' }
                ],
                name: [
                    { required: true, message: '请输入推送名称', trigger: 'blur' },
                    { max: 50, message: '长度不能超过50个字符', trigger: 'blur' }
                ],
                start_at: [
                    { required: true, message: '请选择开始日期', trigger: 'blur' }
                ],
            },
            taskRules: {
                mission_condition: [
                    { required: true, message: '请选择任务类型', trigger: 'change' }
                ],
                logic_params: [
                    {
                        validator: (rule, value, callback) => {
                            const task = this.tasks.find(t => t.logic_params === value);
                            if (!task || !value[task.mission_condition]) {
                                callback(new Error('请输入任务条件值'));
                            } else {
                                callback();
                            }
                        },
                        trigger: 'blur'
                    }
                ],
                deadline_days: [
                    { required: true, message: '请输入任务周期', trigger: 'blur' },
                    { type: 'number', message: '必须为数字值', trigger: 'blur', transform: value => Number(value) }
                ],
                reward_type: [
                    { required: true, message: '请选择奖励类型', trigger: 'change' }
                ],
                reward_content: [
                    {
                        validator: (rule, value, callback) => {
                            const task = this.tasks.find(t => t.reward_type === this.reward_type);
                            if (!task) {
                                callback();
                                return;
                            }

                            if (task.reward_type === 'AIRDROP') {
                                if (!task.asset || !task.amount) {
                                    callback(new Error('请选择代币'));
                                } else {
                                    callback();
                                }
                            } else if (task.reward_type === 'CASHBACK') {
                                if (!task.equity_id) {
                                    callback(new Error('请选择权益类型'));
                                } else {
                                    callback();
                                }
                            } else {
                                callback();
                            }
                        },
                        trigger: 'change'
                    }
                ]
            },
            selectedReward: '手续费返现',
            userType: "someUser", // 添加这个字段用于展示

            // 客群接收情况相关
            receptionCheckLoading: false,
            receptionDialogVisible: false,
            downloadLoading: false,
            reachableCount: 0,
            unreachableCount: 0,
            // 添加实际可接收用户数
            actualUserCount: null,
            // 添加标志，用于区分初始化和用户操作
            isInitialized: false,
        }
    },
    computed: {
        // 计算总用户数（使用实际可接收用户数）
        totalUserCount() {
            if (this.actualUserCount !== null && this.actualUserCount !== undefined) {
                return this.actualUserCount;
            }
            return this.tagGroups.reduce((total, group) => total + (group.user_count || 0), 0);
        },

        // 排序后的场景列表
        sortedScenes() {
            if (!this.Scenes || Object.keys(this.Scenes).length === 0) {
                return [];
            }

            // 对 key 进行数值倒序排列，返回排序后的数组
            const sortedKeys = Object.keys(this.Scenes).sort((a, b) => parseInt(b) - parseInt(a));
            const sortedScenes = [];

            sortedKeys.forEach(key => {
                sortedScenes.push({
                    key: key,
                    value: this.Scenes[key]
                });
            });
            console.log(sortedScenes);
            return sortedScenes;
        }
    },
    watch: {
        // 监听 tagGroups 变化，自动统计客群人数
        tagGroups: {
            handler(newVal, oldVal) {
                // 检查是否有重复的分群ID
                if (this.isInitialized) {
                    // 只有在初始化完成后且 tagGroups 有内容且与之前不同时才统计
                    // 延迟执行，避免频繁调用
                    this.$nextTick(() => {
                        this.updateUserCount();
                        this.checkUniqueGroups(newVal, oldVal)
                    });
                }
            },
            deep: true
        }
    },
    mounted() {
        this.loadMissionPlanData();
        if (this.missionPlanId !== '0') {
            this.isEditMode = true;
        } else {
            // 初始化新建模式的数据
            this.initializeNewData();
        }
        // 标记初始化完成
        this.$nextTick(() => {
            this.isInitialized = true;
        });
    },
    methods: {
        // 比较两个 tagGroups 数组是否相等
        checkUniqueGroups(newVal, oldVal) {
            const groupIds = newVal.map(group => group.id);
            const uniqueIds = [...new Set(groupIds)];

            if (groupIds.length !== uniqueIds.length) {
                // 找到重复的分群
                const duplicateId = groupIds.find((id, index) => groupIds.indexOf(id) !== index);
                const duplicateGroup = newVal.find(group => group.id === duplicateId);

                this.$message.error(`分群 "${duplicateGroup.name}" 已存在，不能重复添加`);

                // 移除重复的分群
                this.tagGroups = newVal.filter((group, index) =>
                    groupIds.indexOf(group.id) === index
                );
                return; // 移除重复后不执行后续逻辑，等待下一次变化
            }
        },

        // 分群相关方法
        handleUserGroup() {
            this.dialogUserGroupMap.dialogUserGroup = true;
            this.dialogUserGroupMap.dialogUserGroupFilters.name = '';
            this.userGroupQuery();
        },
        userGroupQuery() {
            this.dialogUserGroupMap.dialogUserGroupLoading = true;
            this.$axios.get('/api/operation/user-tag/group-list', { params: this.dialogUserGroupMap.dialogUserGroupFilters }).then(
                res => {
                    this.dialogUserGroupMap.dialogUserGroupLoading = false;
                    if (res && res.data.code === 0) {
                        let data = res.data.data;
                        this.dialogUserGroupMap.dialogUserGroupItems = data.items;
                        this.dialogUserGroupMap.dialogUserGroupTotal = data.total;
                        this.dialogUserGroupMap.group_types = data.extra.group_types;
                    } else {
                        this.dialogUserGroupMap.dialogUserGroupItems = [];
                        this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
                    }
                }
            );
        },
        deleteTagGroup(item, index) {
            this.tagGroups.splice(index, 1);
        },
        download_template() {
            let url = "/api/operation/email-push/template";
            this.$download_from_url(url, "routine-plan-template.xlsx");
        },
        before_upload(file) {
            let name = file.name;
            if (
                !name.endsWith(".xlsx") &&
                !name.endsWith(".xls") &&
                !name.endsWith("csv")
            ) {
                this.$message.error("只能上传excel表格");
                return false;
            }
        },
        upload_success(res) {
            if (res?.code === 0) {
                this.pushSetting.whitelist_user_ids = res.data.items.join();
                this.$message.success(`共导入 ${res.data.total} 条记录`);
                this.updateUserCount();
            } else {
                this.$message.error(
                    `上传失败! (code: ${res?.code}; message: ${res?.message})`
                );
            }
        },
        upload_error(err) {
            this.$message.error(`上传失败! (${err})`);
        },

        handleTotalChange(value) {
            if (value < 1) {
                this.pushSetting.total = 0;
            }
        },
        // 初始化新建模式的数据
        initializeNewData() {
            this.pushSetting = {
                scene_type: "ROUTINE",
                scene_id: null, // 保持为 null，当用户选择时会是字符串
                name: '',
                business_party: null,
                total: null,
                start_at: null,
                end_at: null,
                status: "DRAFT",
                whitelist_enabled: false,
                whitelist_user_ids: "",
            };

            // 初始化多语言内容
            for (let lang in this.languages) {
                this.deliver_content[lang] = { content: '' };
            }
        },

        // 任务操作方法
        addTask() {
            if (this.tasks.length >= 5) {
                this.$message.error('最多只能添加5个任务');
                return;
            };
            this.tasks.push({
                mission_condition: 'DEPOSIT_AMOUNT',
                logic_params: {
                    "DEPOSIT_AMOUNT": null
                },
                sequence: this.tasks.length + 1,
                equity_id: null,
                deadline_days: null,
                reward_type: 'AIRDROP',
                asset: '',
                id: null,
                amount: null,
            });
        },
        updateTaskIndex() {
            // 更新所有任务的sequence
            this.tasks.forEach((task, index) => {
                task.sequence = index + 1;
            });
        },
        removeTask(index) {
            this.tasks.splice(index, 1);
            this.updateTaskIndex()
        },
        goBack() {
            this.$confirm('确认返回上一页？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$router.push('/operation/mission_center/routine_plan');
            }).catch(() => { });
        },

        // 后端接口方法
        loadMissionPlanData() {
            this.$axios.get(`/api/operation/mission/routine/${this.missionPlanId}`)
                .then(res => {
                    if (res.data.code === 0) {
                        const data = res.data.data;
                        this.pushStatus = data.status_value;
                        this.SceneType = data.scene_types;
                        this.MissionCondition = data.conditions;
                        this.equityOptions = data.rewards;
                        this.BusinessParties = data.business_parties;
                        this.Scenes = data.scenes;

                        if (this.missionPlanId == 0) {
                            return
                        }

                        this.pushSetting = {
                            scene_type: data.scene_type || 'ROUTINE',
                            scene_id: String(data.scene_id), // 转换为字符串类型
                            name: data.name || '',
                            business_party: data.business_party,
                            total: data.total,
                            start_at: data.start_at * 1000,
                            end_at: data.end_at ? data.end_at * 1000 : data.end_at,
                            status: data.status,
                            whitelist_enabled: data.whitelist_enabled || false,
                            whitelist_user_ids: data.whitelist_user_ids.join() || "",
                        };

                        // 填充分群数据
                        if (data.tag_groups) {
                            this.tagGroups = data.tag_groups;
                        }

                        // 填充任务设置
                        if (data.missions && data.missions.length > 0) {
                            this.tasks = data.missions.map(task => ({
                                mission_condition: task.mission_condition,
                                logic_params: task.logic_params,
                                deadline_days: task.deadline_days,
                                reward_type: task.reward_type,
                                asset: task.reward_type == 'AIRDROP' ? task.asset : null,
                                amount: task.reward_type == 'AIRDROP' ? task.amount : null,
                                sequence: task.sequence,
                                equity_id: task.equity_id ? String(task.equity_id) : null,
                                id: this.copy ? null : task.id
                            }));
                        }

                        this.isEditMode = true;
                        let is_self_record = data.is_self_record
                        if (this.copy) {
                            this.pushSetting.status = "DRAFT"
                            is_self_record = true
                        }
                        this.editDisabled = !["DRAFT", "REJECTED"].includes(this.pushSetting.status) || !is_self_record
                        this.canReview = this.pushSetting.status == "PENDING"
                    } else {
                        this.$message.error(res.data.message || '加载数据失败');
                    }
                })
                .catch(err => {
                    this.$message.error(`加载数据失败: ${err.message || '未知错误'}`);
                });
        },

        saveAsDraft() {
            this.saveMissionPush();
        },

        submitForReview() {
            this.handleSubmit('review');
        },
        updatePlanReviewStatus(status) {
            this.handleSubmit('review_status', {
                status: status,
                successMessage: status === 'PASSED' ? '已审核成功' : '已拒绝审核'
            });
        },
        saveMissionPush() {
            this.handleSubmit('save');
        },

        // Drag and drop methods
        dragStart() {
            this.isDragging = true;
            // Add any additional logic when drag starts
        },

        dragEnd() {
            this.isDragging = false;
            this.updateTaskIndex()
        },

        // Helper methods for task rewards
        getConditionLabel(missionCondition) {
            switch (missionCondition) {
                case 'DEPOSIT_AMOUNT':
                    return '累计入金额 (USDT)';
                case 'SPOT_AMOUNT':
                    return '累计交易额 (USDT)';
                case 'PERPETUAL_AMOUNT':
                    return '累计交易额 (USDT)';
                case 'COPY_TRADING_ONCE':
                    return '完成一次跟单';
                case 'DEMO_TRADING_ONCE':
                    return '完成一次模拟交易';
                default:
                    return '';
            }
        },

        handleMissionConditionChange(task) {
            let key = task.mission_condition
            if (key == 'COPY_TRADING_ONCE' || key == 'DEMO_TRADING_ONCE') {
                task.logic_params = {
                    ASSET: '',
                    [key]: 1
                };
            } else {
                task.logic_params = {};
            }
        },

        getConditionUnit(missionCondition) {
            switch (missionCondition) {
                case 'DEPOSIT_AMOUNT':
                    return 'USDT';
                case 'SPOT_AMOUNT':
                    return 'USDT';
                case 'PERPETUAL_AMOUNT':
                    return 'USDT';
                default:
                    return '';
            }
        },
        // 调整时间为最近的分钟整点
        adjustToNearestMinute(timestamp) {
            if (!timestamp) return null;
            const date = new Date(timestamp);

            // 如果当前已经是分钟整点，直接返回
            if (date.getSeconds() === 0 && date.getMilliseconds() === 0) {
                return date.getTime();
            }

            // 否则调整到下一个分钟整点
            date.setSeconds(0);
            date.setMilliseconds(0);
            date.setMinutes(date.getMinutes() + 1);
            return date.getTime();
        },


        // 处理开始时间变化
        handleStartTimeChange(value) {
            if (!value) return;

            const selectedDate = new Date(value);
            const now = new Date();
            const nextTime = this.adjustToNearestMinute(now);
            // 判断是否是当天
            const isChange = selectedDate.getDate() === now.getDate() &&
                selectedDate.getMonth() === now.getMonth() &&
                selectedDate.getFullYear() === now.getFullYear() && selectedDate < nextTime;

            if (isChange) {
                // 如果是当天，调整为下一个整点
                this.pushSetting.start_at = nextTime;
            } else {
                // 如果不是当天，直接使用选择的时间
                this.pushSetting.start_at = this.adjustToNearestMinute(value);
            }
        },

        // 新增：统一的提交处理方法
        async handleSubmit(submitType, options = {}) {
            const {
                status = null,  // 状态检查
                successMessage = '',  // 成功提示
                errorMessage = '',  // 错误提示
                apiEndpoint = '',  // API端点
                method = 'post',  // 请求方法
                extraData = {}  // 额外数据
            } = options;

            // 状态检查
            if (status && !status.includes(this.pushSetting.status)) {
                this.$message.error('当前状态不允许此操作');
                return;
            }

            // 表单验证
            const isValid = await this.validateForms();
            if (!isValid) return;

            // 构建基础数据
            const submitData = {
                ...this.buildSubmitData(),
                ...extraData
            };

            try {
                const res = await this.$axios[method](apiEndpoint, submitData);
                if (res.data.code === 0) {
                    this.$message.success(successMessage);

                    // 处理保存后的逻辑
                    if (['save', 'review'].includes(submitType)) {
                        this.missionPlanId = res.data.data.id;
                        this.copy = this.copy ? false : this.copy;
                        const targetQuery = { id: this.missionPlanId, copy: this.copy };
                        const targetRoute = this.$router.resolve({ query: targetQuery }).route;
                        if (targetRoute.fullPath !== this.$route.fullPath) {
                            this.$router.replace({ query: targetQuery });
                        }
                    }

                    // 重新加载数据
                    this.loadMissionPlanData();
                } else {
                    this.$message.error(res.data.message || errorMessage);
                    if (submitType == 'review_status') {
                        this.loadMissionPlanData();
                    }
                }
            } catch (err) {
                this.$message.error(`${errorMessage}: ${err.message || '未知错误'}`);
            }
        },

        // 重构后的保存方法
        async saveMissionPush() {
            const apiEndpoint = this.isEditMode && !this.copy ?
                `/api/operation/mission/${this.missionPlanId}` :
                '/api/operation/mission';
            const method = this.isEditMode && !this.copy ? 'put' : 'post';

            await this.handleSubmit('save', {
                successMessage: this.isEditMode ? '已更新草稿' : '已保存为草稿',
                errorMessage: '保存失败',
                apiEndpoint,
                method
            });
        },

        // 重构后的提交审核方法
        async submitForReview() {
            await this.handleSubmit('review', {
                status: ["DRAFT", "REJECTED"],
                successMessage: '提交审核成功',
                errorMessage: '提交审核失败',
                apiEndpoint: `/api/operation/mission/${this.missionPlanId}`,
                method: 'patch',
                extraData: {
                    status: 'PENDING'  // 可以添加额外的状态信息
                }
            });
        },

        // 重构后的审核通过方法
        async updatePlanReviewStatus(status) {
            const statusConfig = {
                'PASSED': {
                    confirmText: '确认审核通过此任务推送?<br><span style="color: #999; font-size: 14px;">(通过后任务推送立即生效)</span>',
                    successMessage: '已审核成功'
                },
                'REJECTED': {
                    confirmText: '确认审核不通过',
                    successMessage: '已拒绝审核'
                }
            };

            const config = statusConfig[status];

            try {
                await this.$confirm(config.confirmText, '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                    dangerouslyUseHTMLString: true,
                    customClass: 'custom-confirm-dialog',
                    center: true
                });

                await this.handleSubmit('review_status', {
                    successMessage: config.successMessage,
                    errorMessage: '审核操作失败',
                    apiEndpoint: `/api/operation/mission/${this.missionPlanId}/review`,
                    method: 'post',
                    extraData: { status }
                });
            } catch (err) {
                if (err !== 'cancel') {
                    this.$message.error('审核操作失败');
                } else {
                    this.$message({
                        type: 'info',
                        message: '已取消审核'
                    });
                }
            }
        },

        // 验证表单
        validateForms() {
            return new Promise((resolve, reject) => {
                this.$refs.pushSettingForm.validate(valid => {
                    if (!valid) {
                        this.$message.error('请完善推送设置信息');
                        reject(false);
                    }

                    // 检查任务是否有重复的任务类型和条件值
                    const taskMap = new Map();
                    let hasDuplicate = false;
                    this.tasks.forEach(task => {
                        const key = `${task.mission_condition}-${task.logic_params[task.mission_condition]}`;
                        if (taskMap.has(key)) {
                            hasDuplicate = true;
                            const existingTask = this.tasks.find(t => t.sequence === taskMap.get(key));
                            this.$message.error(`任务${task.sequence}和任务${existingTask.sequence}的任务类型和条件值重复`);
                        } else {
                            taskMap.set(key, task.sequence);
                        }
                    });

                    if (hasDuplicate) {
                        reject(false);
                    }

                    if (this.pushSetting.end_at && this.pushSetting.end_at < this.pushSetting.start_at) {
                        this.$message.error('结束时间不能小于开始时间');
                        reject(false);
                    }
                    // 验证任务列表是否为空
                    if (this.tasks.length === 0) {
                        this.$message.error('请至少添加一个任务');
                        reject(false);
                    }
                    // 验证推送客群
                    if (this.tagGroups.length === 0) {
                        this.$message.error('老用户任务必须指定用户画像分组');
                        reject(false);
                    }

                    // 验证每个任务的表单
                    const taskValidations = this.tasks.map((task, index) => {
                        return new Promise((resolve) => {
                            const formRef = this.$refs[`taskForm${index}`];
                            if (formRef && formRef.length > 0) {
                                formRef[0].validate(valid => {
                                    if (!valid) {
                                        this.$message.error(`第${index + 1}个任务信息不完整`);
                                        reject(false);
                                    }
                                    if (task.reward_type === 'AIRDROP') {
                                        if (!task.asset || !task.amount) {
                                            this.$message.error(`第${index + 1}个任务, 请填写空投奖励的币种和数量`);
                                            reject(false);
                                        }
                                    } else if (task.reward_type === 'CASHBACK') {
                                        if (!task.equity_id) {
                                            this.$message.error(`第${index + 1}个任务, 请选择权益.`)
                                            reject(false);
                                        }
                                    }
                                    resolve(true);
                                });
                            } else {
                                resolve(false);
                            }
                        });
                    });

                    Promise.all(taskValidations).then(results => {
                        if (results.every(valid => valid)) {
                            resolve(true);
                        } else {
                            reject(false);
                        }
                    });
                });
            });
        },

        // 构建提交数据
        // 新增：构建提交数据方法
        buildSubmitData() {
            let groupIds = this.tagGroups.map(group => group.id);
            const submitData = {
                id: this.copy ? null : this.missionPlanId,
                scene_type: this.pushSetting.scene_type,
                scene_id: Number(this.pushSetting.scene_id), // 转换回数字类型
                group_ids: groupIds,
                business_party: this.pushSetting.business_party,
                name: this.pushSetting.name,
                total: this.pushSetting.total,
                start_at: this.pushSetting.start_at,
                end_at: this.pushSetting.end_at,
                whitelist_enabled: this.pushSetting.whitelist_enabled,
                whitelist_user_ids: this.pushSetting.whitelist_user_ids,
                missions: this.tasks.map(task => ({
                    mission_condition: task.mission_condition,
                    logic_params: { ...task.logic_params, ...{ ASSET: this.getConditionUnit(task.mission_condition) } },
                    deadline_days: task.deadline_days,
                    sequence: task.sequence,
                    asset: task.asset,
                    amount: task.amount,
                    id: task.id,
                    equity_id: task.equity_id ? Number(task.equity_id) : null,
                    reward_type: task.reward_type
                }))
            };
            return submitData;
        },

        // 统计当前客群人数（当 tagGroups 变化时调用）
        async updateUserCount() {
            if (!this.tagGroups.length) {
                this.actualUserCount = 0;
                return;
            }

            if (!this.pushSetting.scene_id) {
                this.$message.error('请先选择推送场景');
                return;
            }

            try {
                const groupIds = this.tagGroups.map(group => group.id);
                const response = await this.$axios.post('/api/operation/mission/check-reception', {
                    group_ids: groupIds,
                    whitelist_user_ids: this.pushSetting.whitelist_user_ids,
                    scene_id: this.pushSetting.scene_id
                });

                if (response.data.code === 0) {
                    const data = response.data.data;
                    this.reachableCount = data.reachable_count || 0;
                    this.unreachableCount = data.unreachable_count || 0;
                    // 更新实际可接收用户数
                    this.actualUserCount = data.user_count || 0;
                }
            } catch (error) {
                this.$message.error('统计客群人数失败:' + error);
            }
        },

        // 检查客群接收情况（手动点击时调用）
        async checkUserReception() {
            if (!this.tagGroups.length) {
                this.$message.warning('请先选择客群');
                return;
            }

            this.receptionCheckLoading = true;
            try {
                const groupIds = this.tagGroups.map(group => group.id);
                const response = await this.$axios.post('/api/operation/mission/check-reception', {
                    group_ids: groupIds,
                    scene_id: this.pushSetting.scene_id,
                    whitelist_user_ids: this.pushSetting.whitelist_user_ids,
                });

                if (response.data.code === 0) {
                    const data = response.data.data;
                    this.reachableCount = data.reachable_count || 0;
                    this.unreachableCount = data.unreachable_count || 0;
                    this.receptionDialogVisible = true;
                } else {
                    this.$message.error(response.data.message || '检查接收情况失败');
                }
            } catch (error) {
                this.$message.error(`检查接收情况失败: ${error.message || '未知错误'}`);
            } finally {
                this.receptionCheckLoading = false;
            }
        },
        // 下载用户信息
        async downloadUser() {
            if (this.actualUserCount === 0) {
                this.$message.warning('没有可接收的用户');
                return;
            }
            try {
                const groupIds = this.tagGroups.map(group => group.id);
                const response = await this.$axios.post('/api/operation/mission/check-reception', {
                    group_ids: groupIds,
                    scene_id: this.pushSetting.scene_id,
                    whitelist_user_ids: this.pushSetting.whitelist_user_ids,
                    is_export: true
                }, {
                    responseType: 'blob'
                });
                // 创建下载链接
                const blob = new Blob([response.data], {
                    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                });
                const url = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = `客群用户信息_${new Date().toISOString().slice(0, 10)}.xlsx`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                window.URL.revokeObjectURL(url);

                this.$message.success('下载成功');

            } catch (error) {
                this.$message.error(`下载失败: ${error.message || '未知错误'}`);
            } finally {
                this.downloadLoading = false;
            }
        },

        // 下载无法触达用户信息
        async downloadUnreachableUsers() {
            if (this.unreachableCount === 0) {
                this.$message.warning('没有无法触达的用户');
                return;
            }

            this.downloadLoading = true;
            try {
                const groupIds = this.tagGroups.map(group => group.id);
                const response = await this.$axios.post('/api/operation/mission/download-unreachable-users', {
                    group_ids: groupIds,
                    whitelist_user_ids: this.pushSetting.whitelist_user_ids,
                    scene_id: this.pushSetting.scene_id,
                }, {
                    responseType: 'blob'
                });

                // 创建下载链接
                const blob = new Blob([response.data], {
                    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                });
                const url = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = `无法触达用户信息_${new Date().toISOString().slice(0, 10)}.xlsx`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                window.URL.revokeObjectURL(url);

                this.$message.success('下载成功');
            } catch (error) {
                this.$message.error(`下载失败: ${error.message || '未知错误'}`);
            } finally {
                this.downloadLoading = false;
            }
        },
    }
}
</script>

<style scoped>
.task-push-container {
    padding: 20px;
}

h1 {
    font-size: 20px;
    margin-bottom: 20px;
}

.action-buttons {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 20px;
}

.section-card {
    margin-bottom: 20px;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.group-input {
    width: 100%;
}

.time-picker {
    display: flex;
    align-items: center;
}

.separator {
    margin: 0 10px;
}

.add-task-btn {
    margin-bottom: 20px;
}

.task-item {
    margin: 20px 0;
    padding: 15px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    position: relative;
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.task-header-left {
    display: flex;
    align-items: center;
}

.drag-handle {
    cursor: move;
    font-size: 16px;
    color: #909399;
    margin-right: 10px;
    padding: 2px;
}

.drag-handle:hover {
    color: #409EFF;
}

.task-number {
    font-weight: bold;
    color: #f56c6c;
}

.condition-input {
    display: flex;
    align-items: center;
    gap: 10px;
}

.condition-input .el-input {
    width: 120px;
}

.date-unit {
    margin-left: 5px;
}

.token-input {
    width: 120px;
}

.token-unit {
    margin-left: 5px;
}

.fee-discount {
    display: flex;
    align-items: center;
    gap: 10px;
}

.fee-discount .el-input {
    width: 120px;
}

/* 分群选择区域样式 */
.user-group-section {
    margin-top: 15px;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 4px;
}

.selected-group-item {
    margin: 10px 0;
    padding: 10px;
    background-color: #fff;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
}

.group-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.group-label {
    color: #f56c6c;
    font-weight: bold;
}

.group-name {
    color: #606266;
    font-weight: 500;
}

.group-count {
    color: #909399;
}

.group-id {
    color: #909399;
    font-size: 12px;
}

.whitelist-section {
    margin: 15px 0;
    padding: 10px;
    background-color: #fff;
    border-radius: 4px;
}

.whitelist-input {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 10px;
}

.whitelist-input .el-input {
    width: 300px;
}

.task-id {
    font-size: 11px;
    color: hsl(210, 100%, 63%);
    margin-left: 50px;
    background-color: #ecf5ff;
    padding: 3px 8px;
    border-radius: 4px;
    border: 1px solid #b3d8ff;
    font-weight: 500;
}

/* 客群接收情况相关样式 */
.user-count-section {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 15px;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 4px;
}

.count-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.reception-check-section {
    display: flex;
    align-items: center;
    gap: 10px;
}

.reception-stats {
    margin-bottom: 20px;
}

.stats-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
}

.stats-item:last-child {
    border-bottom: none;
}

.stats-label {
    font-weight: 500;
    color: #606266;
}

.stats-value {
    font-weight: bold;
    font-size: 16px;
}

.stats-value.success {
    color: #67c23a;
}

.stats-value.danger {
    color: #f56c6c;
}

.reception-actions {
    text-align: center;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #f0f0f0;
}

/* 徽章样式调整 */
:deep(.el-badge__content) {
    background-color: #f56c6c;
}

:deep(.el-badge__content.is-fixed) {
    top: 0;
    right: 10px;
    transform: translateY(-50%) translateX(100%);
}

/* 对话框样式 */
:deep(.el-dialog__body) {
    padding: 20px;
}

:deep(.el-dialog__footer) {
    padding: 10px 20px 20px;
    text-align: center;
}

/* 推送圈群样式 */
.push-group-container {
    width: 100%;
}

.condition-card {
    border: 1px solid #ebeef5;
    border-radius: 4px;
    background-color: #fff;
    overflow: hidden;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.02);
    margin-bottom: 15px;
}

.condition-header {
    padding: 12px 15px;
    background-color: #f5f7fa;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #ebeef5;
}

.condition-header i {
    margin-right: 8px;
    color: #409EFF;
}

.condition-content {
    padding: 15px;
}

.condition-row {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.condition-operator {
    width: 80px;
    flex-shrink: 0;
}

.condition-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 5px;
}

.condition-hint i {
    margin-right: 5px;
}

.mining-pool-notice {
    display: flex;
    align-items: center;
    background-color: #f0f9eb;
    padding: 10px 15px;
    border-radius: 4px;
    color: #67c23a;
}

.mining-pool-notice i {
    margin-right: 8px;
    font-size: 16px;
}

.condition-container {
    padding: 0;
}

.condition-row {
    display: flex;
    align-items: center;
    background-color: #fff;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.03);
}

.condition-label {
    width: 180px;
    margin-right: 10px;
    background-color: #f5f7fa;
}

.condition-operator {
    width: 80px;
    margin: 0 10px;
}

.condition-value {
    width: 200px;
    margin-left: 10px;
}

.no-condition {
    color: #909399;
    font-style: italic;
    padding: 10px;
    background-color: #f5f7fa;
    border-radius: 4px;
}

/* Transition styles for drag and drop */
.task-item {
    transition: all 0.3s;
}

.sortable-ghost {
    opacity: 0.5;
    background: #c8ebfb;
}

.sortable-drag {
    opacity: 0.8;
    background: #f9f9f9;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* Reward styles */
.reward-container {
    display: flex;
    align-items: center;
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 4px;
    margin-left: 120px;
    /* Align with form items that have labels */
}

.token-select,
.fee-discount-select {
    width: 180px;
    margin-right: 10px;
}

.token-amount,
.fee-discount-value {
    width: 180px;
    margin-right: 10px;
}

.unit-text {
    margin-left: 5px;
    color: #606266;
}

.cycle-input {
    width: 180px;
}

/* 添加到现有的 <style> 部分 */
:deep(.custom-confirm-dialog) {
    border-radius: 8px;
}

:deep(.custom-confirm-dialog .el-message-box__header) {
    padding-right: 20px;
}

:deep(.custom-confirm-dialog .el-message-box__content) {
    padding: 30px 20px;
    text-align: center;
}

:deep(.custom-confirm-dialog .el-message-box__btns) {
    padding: 10px 20px 20px;
}

:deep(.custom-confirm-dialog .el-button) {
    border-radius: 4px;
    padding: 10px 20px;
    min-width: 100px;
}

:deep(.custom-confirm-dialog .el-button--default) {
    background-color: #f5f5f5;
    border-color: #f5f5f5;
    color: #333;
}

:deep(.custom-confirm-dialog .el-button--primary) {
    background-color: #409EFF;
}

:deep(.custom-confirm-dialog .el-message-box__headerbtn) {
    font-size: 18px;
    top: 15px;
    right: 15px;
}


/* 调整 MessageEditor 的宽度和位置 */
.compact-editor {
    width: 80%;
    margin: 0;
}

.compact-editor :deep(.tox-tinymce) {
    width: 100% !important;
}

.compact-editor :deep(.tox .tox-edit-area__iframe) {
    width: 100% !important;
}

.compact-editor :deep(.tox .tox-edit-area) {
    width: 100% !important;
}

/* 只增加特定 equity_id select 的宽度 */
:deep(.equity-select) {
    width: 260px !important;
}
</style>
