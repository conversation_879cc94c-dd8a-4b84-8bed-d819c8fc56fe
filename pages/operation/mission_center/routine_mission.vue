<template>
    <el-container>
        <el-main>
            <h2 style="font-family: 'Microsoft YaHei', Arial, sans-serif">
                老用户任务推送明细
            </h2>

            <el-form :inline="true" :model="search_data">
                <el-form-item label="用户ID">
                    <UserSearch v-model="search_data.user_id"></UserSearch>
                </el-form-item>

                <el-form-item label="推送ID">
                    <el-select v-model="search_data.plan_id" filterable clearable placeholder="<ALL>">
                        <el-option v-for="(name, id) in extra_data.plan_id_mapper" :key="id" :label="`${name}`"
                            :value="Number(id)">
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="任务ID">
                    <el-select v-model="search_data.mission_id" filterable clearable placeholder="<ALL>">
                        <el-option v-for="(name, id) in extra_data.mission_id_mapper" :key="id" :label="name"
                            :value="Number(id)">
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="推送状态">
                    <el-select v-model="search_data.push_status" clearable placeholder="<ALL>">
                        <el-option v-for="(value, name) in extra_data.push_status" :key="name" :label="value"
                            :value="name">
                        </el-option>
                    </el-select>

                </el-form-item>


                <el-form-item label="任务进行状态">
                    <el-select v-model="search_data.status" clearable placeholder="<ALL>">
                        <el-option v-for="(value, name) in extra_data.statuses" :key="name" :label="value"
                            :value="name">
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="任务奖励">
                    <el-select v-model="search_data.reward_type" clearable placeholder="<ALL>">
                        <el-option v-for="(value, name) in extra_data.reward_types" :key="name" :label="value"
                            :value="name">
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="奖励发放状态">
                    <el-select v-model="search_data.reward_status" clearable placeholder="<ALL>">
                        <el-option v-for="(value, name) in extra_data.reward_statuses" :key="name" :label="value"
                            :value="name">
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="推送时间">
                    <el-date-picker v-model="createdAtRange" type="datetimerange" range-separator="至"
                        start-placeholder="开始日期" end-placeholder="结束日期" format="yyyy-MM-dd HH:mm:ss"
                        @change="handleCreatedAtRangeChange" value-format="timestamp" />
                </el-form-item>

                <el-form-item label="任务完成时间">
                    <el-tooltip content="指用户完成任务的时间，与发奖时间通常仅相差几分钟内，由于直接筛选发放时间因跨表查询过慢，故通过「任务完成时间」来间接筛选「发放时间」。"
                        placement="top">
                        <i class="el-icon-question" style="margin-left: 8px; color: #909399; cursor: pointer;"></i>
                    </el-tooltip>
                    <el-date-picker v-model="completedAtRange" type="datetimerange" range-separator="至"
                        start-placeholder="开始日期" end-placeholder="结束日期" format="yyyy-MM-dd HH:mm:ss"
                        @change="handleCompletedAtRangeChange" value-format="timestamp" />
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" @click="search">搜索</el-button>
                </el-form-item>

                <el-form-item>
                    <el-button @click="clear_search">重置</el-button>
                </el-form-item>

                <el-form-item>
                    <el-tooltip content="导出数据" placement="right" :open-delay="500" :hide-after="2000">
                        <el-button icon="el-icon-download" circle @click="download"></el-button>
                    </el-tooltip>
                </el-form-item>
            </el-form>

            <el-table :data="items" v-loading="loading">
                <el-table-column prop="plan_id" label="推送ID" width="100" fixed>
                    <template slot-scope="scope">
                        <el-link :href="'/operation/mission_center/routine_plan_detail?id=' +
                            scope.row.plan_id
                            " type="primary" target="_blank" :underline="false" style="
                  width: 100%;
                  font-weight: normal;
                  display: inline-block;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                ">
                            {{ scope.row.plan_id }}
                        </el-link>
                    </template>
                </el-table-column>

                <el-table-column prop="plan_name" label="推送名称" width="200" show-overflow-tooltip
                    fixed></el-table-column>

                <el-table-column prop="mission_id" label="任务ID" width="100">
                    <template slot-scope="scope">
                        <el-link :href="'/operation/mission_center/routine_plan_detail?id=' +
                            scope.row.plan_id
                            " type="primary" target="_blank" :underline="false" style="
                  width: 100%;
                  font-weight: normal;
                  display: inline-block;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                ">
                            {{ scope.row.mission_id }}
                        </el-link>
                    </template>
                </el-table-column>

                <el-table-column prop="audit_id" label="用户ID" width="120">
                    <template slot-scope="scope">
                        <el-link :href="'/users/user-details?id=' + scope.row.user_id" type="primary" target="_blank"
                            :underline="false" style="
                  width: 100%;
                  font-weight: normal;
                  display: inline-block;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                ">
                            {{ scope.row.user_id }}
                        </el-link>
                    </template>
                </el-table-column>

                <el-table-column prop="push_status" label="推送状态" width="100"></el-table-column>

                <el-table-column prop="created_at" label="推送时间" width="180"
                    :formatter="(row) => $formatDate(row.created_at)">
                </el-table-column>

                <el-table-column prop="mission_condition" label="任务类型" width="120"></el-table-column>

                <el-table-column prop="status" label="任务进行状态" width="120">
                    <template #default="scope">
                        {{ scope.row.status === '已失败' ? '--' : scope.row.status }}
                    </template>
                </el-table-column>

                <el-table-column prop="completed_at" label="达标时间" width="180"
                    :formatter="(row) => $formatDate(row.completed_at)">
                </el-table-column>

                <el-table-column prop="reward_name" label="任务奖励" width="150">
                    <template slot-scope="scope">
                        <el-link v-if="scope.row.reward.reward_type === 'CASHBACK'"
                            :href="'/operation/equity-center/cashback/equity-list?equity_id=' + scope.row.equity_id"
                            type="primary" target="_blank" :underline="false" style="
                  width: 100%;
                  font-weight: normal;
                  display: inline-block;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                ">
                            {{ scope.row.reward_name }}
                        </el-link>
                        <span v-else>{{ scope.row.reward_name }}</span>
                    </template>
                </el-table-column>

                <el-table-column prop="reward.status" label="奖励发放状态" width="120">
                    <template #default="scope">
                        <el-tooltip :content="scope.row.fail_reason" placement="top" v-if="scope.row.fail_reason">
                            <span>{{ scope.row.reward.status }}</span>
                        </el-tooltip>
                        <span v-else>{{ scope.row.reward.status }}</span>
                    </template>
                </el-table-column>

                <el-table-column prop="reward.cost_amount" label="应发奖励" width="120">
                    <template slot-scope="scope">
                        <span v-if="scope.row.waring_flag" style="color: red;">
                            {{ scope.row.reward.cost_amount }} {{ scope.row.reward.cost_asset }}
                        </span>
                        <span v-else>{{ scope.row.reward.cost_amount }} {{ scope.row.reward.cost_asset }}</span>
                    </template>
                </el-table-column>

                <el-table-column prop="reward.real_amount" label="实发奖励" width="120">
                    <template slot-scope="scope">
                        <span v-if="scope.row.waring_flag" style="color: red;">
                            {{ scope.row.reward.real_amount }} {{ scope.row.reward.cost_asset }}
                        </span>
                        <span v-else>{{ scope.row.reward.real_amount }} {{ scope.row.reward.cost_asset }}</span>
                    </template>
                </el-table-column>

                <el-table-column prop="reward.created_at" label="发放时间" width="180"
                    :formatter="(row) => $formatDate(row.reward.created_at)">
                </el-table-column>

                <el-table-column prop="updated_at" label="更新时间" width="180"
                    :formatter="(row) => $formatDate(row.updated_at)">
                </el-table-column>
            </el-table>

            <CursorPagination ref="page" :cursor.sync="search_data.cursor" :has-next="has_next"
                :page-size.sync="search_data.limit" :page-sizes="[20, 50, 100, 200]" @next="get_data">
            </CursorPagination>
        </el-main>
    </el-container>
</template>

<script>
import UserSearch from "@/components/user/UserSearch";
import CursorPagination from "@/components/CursorPagination.vue";

const base_url = "/api/operation/mission/user-mission";

export default {
    components: {
        UserSearch,
        CursorPagination,
    },

    data() {
        return {
            loading: false,
            items: [],
            has_next: false,
            createdAtRange: null,
            completedAtRange: null,
            extra_data: {
                statuses: {},
                reward_types: {},
                reward_statuses: {},
                plan_id_mapper: {},
                mission_id_mapper: {},
                push_status: {},
            },
            search_data: {
                scene_type: 'ROUTINE',
                user_id: null,
                plan_id: null,
                mission_id: null,
                status: null,
                reward_type: null,
                reward_status: null,
                cursor: null,
                limit: 50,
                created_at_start: null,
                created_at_end: null,
                completed_at_start: null,
                completed_at_end: null,
            }
        };
    },

    mounted() {
        // 如果URL中有plan_id参数，设置到搜索条件中
        if (this.$route.query.plan_id) {
            this.search_data.plan_id = Number(this.$route.query.plan_id);
        }
        this.get_data();
    },

    methods: {
        get_request_params() {
            return _.omitBy(this.search_data, v => v === null || v === '');
        },
        search() {
            this.search_data.cursor = null;
            this.get_data();
        },
        get_data() {
            this.loading = true;
            let params = this.get_request_params();

            this.$axios.get(base_url, { params })
                .then(res => {
                    this.loading = false;
                    if (res.data.code === 0) {
                        const { items, has_next, extra_data, cursor } = res.data.data;
                        this.items = items;
                        this.has_next = has_next;
                        this.extra_data = extra_data;
                        this.$refs.page.pushNextCursor(cursor);
                    } else {
                        this.$message.error(
                            `code: ${res.data.code}; message: ${res.data.message}`
                        );
                    }
                })
                .catch(err => {
                    this.loading = false;
                    this.$message.error(`获取数据失败: ${err}`);
                });
        },

        clear_search() {
            this.search_data = {
                user_id: null,
                plan_id: null,
                mission_id: null,
                status: null,
                reward_type: null,
                reward_status: null,
                push_status: null,
                scene_type: 'ROUTINE',
                cursor: null,
                created_at_start: null,
                created_at_end: null,
                completed_at_start: null,
                completed_at_end: null,
                limit: 50,
            };
            this.createdAtRange = null;
            this.completedAtRange = null;
            this.get_data();
        },
        handleCreatedAtRangeChange(value) {
            if (value && value.length === 2) {
                this.search_data.created_at_start = Math.floor(value[0] / 1000);
                this.search_data.created_at_end = Math.floor(value[1] / 1000);
            } else {
                this.search_data.created_at_start = null;
                this.search_data.created_at_end = null;
            }
            this.get_data();
        },
        handleCompletedAtRangeChange(value) {
            if (value && value.length === 2) {
                this.search_data.completed_at_start = Math.floor(value[0] / 1000);
                this.search_data.completed_at_end = Math.floor(value[1] / 1000);
            } else {
                this.search_data.completed_at_start = null;
                this.search_data.completed_at_end = null;
            }
            this.get_data();
        },
        download() {
            this.$download_from_url(base_url, 'newbie_mission.xlsx', { export: 1, ...this.get_request_params() });
        },
    }
};
</script>
