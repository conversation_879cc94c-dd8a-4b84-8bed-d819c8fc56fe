<template>
    <el-container>
        <el-main>
            <h2 style="font-family: 'Microsoft YaHei', Arial, sans-serif">
                新用户任务统计
                <el-tooltip placement="right" :open-delay="500">
                    <div slot="content">
                        <p>说明：统计每日对应任务“当天”成功推送的用户数，以及这些用户对应的其他数据每日更新变化：达标用户数、已发奖用户数、应发奖励、实发奖励。若该批用户的任务过期/权益过期则停止更新。</p>
                        <p>如：7月29日任务ID60在7.29当天成功推送10人，其中1人达标，0人发奖……，成功推送人数【10】在后续天数中不会再更新，但这10人的其他数据会更新到无数据可更新为止。</p>
                    </div>
                    <i class="el-icon-question"></i>
                </el-tooltip>
            </h2>
            <el-form :inline="true" :model="search_data">

                <el-form-item label="统计日期">
                    <el-date-picker v-model="reportDateRange" type="daterange" range-separator="至"
                        start-placeholder="开始日期" end-placeholder="结束日期" @change="handleReportDateRangeChange"
                        value-format="yyyy-MM-dd" :clearable="true" :editable="false" />
                </el-form-item>

                <el-form-item label="推送ID">
                    <el-select v-model="search_data.plan_id" filterable clearable placeholder="<ALL>"
                        :loading="loading">
                        <el-option v-for="(name, id) in extra_data.plan_id_mapper" :key="id" :label="`${name}`"
                            :value="id">
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="任务ID">
                    <el-select v-model="search_data.mission_id" filterable clearable placeholder="<ALL>"
                        :loading="loading">
                        <el-option v-for="(name, id) in extra_data.mission_id_mapper" :key="id" :label="name"
                            :value="Number(id)">
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="任务奖励">
                    <el-select v-model="search_data.reward_type" filterable clearable placeholder="<ALL>"
                        :loading="loading">
                        <el-option v-for="(label, value) in extra_data.reward_types" :key="value" :label="label"
                            :value="value">
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" @click="search" :loading="loading">
                        搜索
                    </el-button>
                </el-form-item>

                <el-form-item>
                    <el-button @click="clear_search" :loading="loading">
                        重置
                    </el-button>
                </el-form-item>

                <el-form-item>
                    <el-tooltip content="导出数据" placement="right" :open-delay="500" :hide-after="2000">
                        <el-button icon="el-icon-download" circle @click="download" :loading="loading">
                        </el-button>
                    </el-tooltip>
                </el-form-item>
            </el-form>

            <el-table :data="this.items" style="width: 100%">
                <el-table-column prop="report_date" label="日期" width="120"
                    :formatter="(row) => $formatDate(row.report_date, 'YYYY-MM-DD')" fixed>
                </el-table-column>

                <el-table-column prop="plan_id" label="推送ID" width="100">
                    <template slot-scope="scope">
                        <el-link :href="'/operation/mission_center/newbie_plan_detail?id=' +
                            scope.row.plan_id
                            " type="primary" target="_blank" :underline="false" style="
                  width: 100%;
                  font-weight: normal;
                  display: inline-block;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                ">
                            {{ scope.row.plan_id }}
                        </el-link>
                    </template>
                </el-table-column>

                <el-table-column prop="plan_name" label="推送名称" width="200" show-overflow-tooltip fixed>
                </el-table-column>

                <el-table-column prop="mission_id" label="任务ID" width="100">
                    <template slot-scope="scope">
                        <el-link :href="'/operation/mission_center/newbie_plan_detail?id=' +
                            scope.row.plan_id
                            " type="primary" target="_blank" :underline="false" style="
                  width: 100%;
                  font-weight: normal;
                  display: inline-block;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                ">
                            {{ scope.row.mission_id }}
                        </el-link>
                    </template>
                </el-table-column>
                <el-table-column prop="mission_condition" label="任务类型" width="120"></el-table-column>

                <el-table-column prop="delivery_count" label="推送成功用户数" width="150"></el-table-column>

                <el-table-column prop="completion_count" label="达标用户数" width="100"></el-table-column>

                <el-table-column prop="finished_count" label="已发奖用户数" width="120"></el-table-column>
                <el-table-column prop="reward_name" label="任务奖励" width="180">
                    <template slot-scope="scope">
                        <el-link v-if="scope.row.reward.reward_type === 'CASHBACK'"
                            :href="'/operation/equity-center/cashback/equity-list?equity_id=' + scope.row.equity_id"
                            type="primary" target="_blank" :underline="false" style="
                  width: 100%;
                  font-weight: normal;
                  display: inline-block;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                ">
                            {{ scope.row.reward_name }}
                        </el-link>
                        <span v-else>{{ scope.row.reward_name }}</span>
                    </template>
                </el-table-column>


                <el-table-column prop="reward_amount" label="应发奖励" width="120">
                    <template slot-scope="scope">
                        <span v-if="scope.row.waring_flag" style="color: red;">
                            {{ scope.row.reward_amount }} {{ scope.row.reward.value_type }}
                        </span>
                        <span v-else>{{ scope.row.reward_amount }} {{ scope.row.reward.value_type }}</span>
                    </template>
                </el-table-column>

                <el-table-column prop="real_reward_amount" label="实发奖励" width="120">
                    <template slot-scope="scope">
                        <span v-if="scope.row.waring_flag" style="color: red;">
                            {{ scope.row.real_reward_amount }} {{ scope.row.reward.value_type }}
                        </span>
                        <span v-else>{{ scope.row.real_reward_amount }} {{ scope.row.reward.value_type }}</span>
                    </template>
                </el-table-column>

                <el-table-column prop="updated_at" label="更新时间" width="180"
                    :formatter="(row) => $formatDate(row.updated_at)">
                </el-table-column>
            </el-table>

            <el-pagination :current-page.sync="search_data.page" :page-size.sync="search_data.limit"
                @size-change="get_data" @current-change="get_data" :page-sizes="[50, 100, 200]"
                :hide-on-single-page="true" layout="total, sizes, prev, pager, next, jumper" :total="this.total">
            </el-pagination>

            <el-backtop></el-backtop>
        </el-main>
    </el-container>
</template>


<script>
const base_url = "/api/operation/mission/statistics";

export default {
    mounted() {
        this.get_data();
    },
    methods: {
        handleReportDateRangeChange(value) {
            if (value && value.length === 2) {
                this.search_data.report_date_start = value[0];
                this.search_data.report_date_end = value[1];
            } else {
                this.search_data.report_date_start = null;
                this.search_data.report_date_end = null;
            }
            this.get_data();
        },
        get_request_params() {
            return _.omitBy(this.search_data, v => v === null || v === '');
        },
        search() {
            this.search_data.page = 1;
            this.get_data();
        },
        get_data() {
            this.loading = true;
            let params = this.get_request_params();
            this.$axios.get(base_url, { params: params }).then((res) => {
                this.loading = false;
                if (res.data.code === 0) {
                    let data = res.data.data;
                    this.items = data.items;
                    this.total = data.total;
                    this.extra_data = data.extra_data;
                } else {
                    this.$message.error(
                        `code: ${res.data.code}; message: ${res.data.message}`
                    );
                }
            });
        },
        clear_search() {
            this.search_data = {
                date: null,
                plan_id: null,
                mission_id: null,
                reward_type: null,
                report_date_start: null,
                report_date_end: null,
                page: 1,
                limit: 50,
                is_export: 0,
            };
            this.reportDateRange = null;
            this.get_data();
        },
        download() {
            this.$download_from_url(base_url, 'newbie_statistics.xlsx', { ...this.get_request_params(), export: true })
        },
    },
    data() {
        return {
            loading: false,
            total: 0,
            items: [],
            reportDateRange: null,
            extra_data: {
                reward_types: {},
                plan_id_mapper: {},
                mission_id_mapper: {}
            },
            search_data: {
                date: null,
                plan_id: null,
                mission_id: null,
                reward_type: null,
                report_date_start: null,
                report_date_end: null,
                page: 1,
                limit: 50,
                is_export: 0,
            },
        };
    },
};
</script>
