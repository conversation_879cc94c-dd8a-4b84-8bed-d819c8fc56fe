<template>
  <el-container>
    <el-main>
      <h2 style="font-family: 'Microsoft YaHei', <PERSON>l, sans-serif">返现权益使用记录</h2>

      <el-form :inline="true" :model="search_data">
        <el-form-item label="用户ID">
          <UserSearch v-model="search_data.user_id" @change="handle_page_refresh"></UserSearch>
        </el-form-item>

        <el-form-item label="发放类型">
          <el-select clearable filterable v-model="search_data.business_type" @change="handle_business_type_change"
            placeholder="<ALL>">
            <el-option v-for="(k, v) in this.business_type_dict" :key="v" :label="k" :value="v"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="任务ID/平台发放ID/活动ID">
          <el-select v-model="search_data.source_value" filterable clearable placeholder="请选择ID" style="width: 300px"
            @change="handle_source_id_change" :disabled="!search_data.business_type">
            <el-option v-for="i in source_id_options" :key="i.value" :label="i.label" :value="i.value"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="权益ID">
          <el-select filterable clearable @change="handle_page_refresh" v-model="search_data.equity_id"
            placeholder="<ALL>" style="width: 250px">
            <el-option v-for="(value, label) in all_equity_dict" :key="label" :label="value" :value="label"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="权益使用状态">
          <el-select clearable filterable allow-create @change="handle_page_refresh" v-model="search_data.status"
            placeholder="<ALL>" style="width: 150px">
            <el-option v-for="(value, label) in status_dict" :key="value" :label="value" :value="label"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="发放时间">
          <el-date-picker @change="handle_page_refresh" v-model="filters_mid.date_range[0]" type="datetime"
            placeholder="∞" value-format="timestamp">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="">
          <el-date-picker @change="handle_page_refresh" v-model="filters_mid.date_range[1]" type="datetime"
            placeholder="∞" value-format="timestamp">
          </el-date-picker>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="get_data">搜索</el-button>
        </el-form-item>

        <el-form-item>
          <el-button @click="clear_search">重置</el-button>
        </el-form-item>

        <el-form-item>
          <el-tooltip content="导出数据" placement="right" :open-delay="500" :hide-after="2000">
            <el-button icon="el-icon-download" type="success" circle @click="download"></el-button>
          </el-tooltip>
        </el-form-item>
      </el-form>

      <!-- 统计数据行 -->
      <div class="stats-container">
        <div class="stats-header" @click="toggleStats">
          <span class="stats-title">数据统计</span>
          <i :class="['el-icon-arrow-down', { 'is-collapsed': !showStats }]"></i>
        </div>

        <div class="stats-content" :class="{ 'is-collapsed': !showStats }">
          <div class="stat-item">
            <div class="stat-value">{{ statistics.totalRecords }}</div>
            <div class="stat-label">总记录数</div>
          </div>

          <div class="stat-item">
            <div class="stat-value">{{ statistics.uniqueUsers }}</div>
            <div class="stat-label">去重用户数</div>
          </div>

          <div class="stat-item">
            <div class="stat-value">
              <span v-for="(amount, asset) in statistics.totalCostAmount" :key="asset" class="asset-inline">
                {{ amount }} {{ asset }}
              </span>
            </div>
            <div class="stat-label">累计返现价值</div>
          </div>

          <div class="stat-item">
            <div class="stat-value">
              <span v-for="(amount, asset) in statistics.totalRealCostAmount" :key="asset" class="asset-inline">
                {{ amount }} {{ asset }}
              </span>
            </div>
            <div class="stat-label">累计返现额度</div>
          </div>

          <div class="stat-item">
            <div class="stat-value">
              <div v-for="(amount, asset) in statistics.totalCashbackAmount" :key="asset" class="asset-multi">
                {{ amount }} {{ asset }}
              </div>
            </div>
            <div class="stat-label">累计返现数目</div>
          </div>

          <div class="stat-item">
            <div class="stat-value">{{ statistics.usingCount }}</div>
            <div class="stat-label">使用中</div>
          </div>

          <div class="stat-item">
            <div class="stat-value">{{ statistics.usedCount }}</div>
            <div class="stat-label">已使用</div>
          </div>

          <div class="stat-item">
            <div class="stat-value">{{ statistics.expiredCount }}</div>
            <div class="stat-label">已过期</div>
          </div>
        </div>
      </div>

      <el-table :data="this.items" style="width: 100%">
        <el-table-column prop="equity_id" label="权益ID" min-width="80px">
          <template slot-scope="scope">
            <el-link :href="'/operation/equity-center/cashback/equity-list?equity_id=' +
              scope.row.equity_id
              " type="primary" target="_blank" :underline="false" style="
                width: 100%;
                font-weight: normal;
                display: inline-block;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              ">{{ scope.row.equity_id }}</el-link>
          </template>
        </el-table-column>

        <el-table-column prop="cashback_scope" label="返现范围"
          :formatter="(row) => `${cashback_scope_dict[row.cashback_scope]}`" min-width="130px"></el-table-column>

        <el-table-column prop="business_type" label="发放类型" min-width="130px"
          :formatter="(row) => `${business_type_dict[row.business_type]}`"></el-table-column>

        <el-table-column prop="user_id" label="任务ID/平台发放ID/活动ID" min-width="160px">
          <template slot-scope="scope">
            <el-link :href="buildSourceLink(scope.row)" type="primary" target="_blank" :underline="false" style="
                width: 100%;
                font-weight: normal;
                display: inline-block;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              ">{{ scope.row.source_id }}</el-link>
          </template>
        </el-table-column>

        <el-table-column prop="user_id" label="用户ID" min-width="160px">
          <template slot-scope="scope">
            <el-link :href="'/users/user-details?id=' + scope.row.user_id" type="primary" target="_blank"
              :underline="false" style="
                width: 100%;
                font-weight: normal;
                display: inline-block;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              ">{{ scope.row.user_id }}</el-link>
          </template>
        </el-table-column>

        <el-table-column prop="created_at" label="发放时间(UTC+8)" min-width="200px"
          :formatter="(row) => $formatDate(row.created_at)"></el-table-column>

        <el-table-column prop="finished_at" label="到期时间(UTC+8)" min-width="200px"
          :formatter="(row) => $formatDate(row.finished_at)"></el-table-column>

        <el-table-column prop="cost_amount" label="返现价值" min-width="160px" show-overflow-tooltip>
          <template slot-scope="scope">{{ scope.row.cost_amount }} {{ scope.row.cost_asset }}</template>
        </el-table-column>

        <el-table-column prop="real_cost_amount" label="累计返现额度" min-width="160px" show-overflow-tooltip>
          <template slot-scope="scope">
            <span :style="{ color: scope.row.pending_cost_amount > 0 ? 'red' : '' }">{{ scope.row.real_cost_amount }} {{
              scope.row.cost_asset }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="real_cashback_amount" label="累计返现数目" min-width="160px" show-overflow-tooltip>
          <template slot-scope="scope">
            <span :style="{ color: scope.row.pending_cashback_amount > 0 ? 'red' : '' }">{{
              scope.row.real_cashback_amount }} {{ scope.row.cashback_asset }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="status" label="使用权益状态" min-width="120px"
          :formatter="(row) => `${status_dict[row.status] || row.status}`"></el-table-column>

        <el-table-column prop="updated_at" label="更新时间(UTC+8)" min-width="200px"
          :formatter="(row) => $formatDate(row.updated_at)"></el-table-column>

        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-link
              :href="'/operation/equity-center/cashback/user-equity-history?user_equity_id=' + + scope.row.user_equity_id"
              type="primary" target="_blank" :underline="false" style="
                width: 100%;
                font-weight: normal;
                display: inline-block;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              ">查看明细</el-link>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination :current-page.sync="search_data.page" :page-size.sync="search_data.limit" @size-change="get_data"
        @current-change="get_data" :page-sizes="[50, 100, 200]" :hide-on-single-page="false"
        layout="total, sizes, prev, pager, next, jumper" :total="this.total"></el-pagination>

      <el-backtop></el-backtop>
    </el-main>
  </el-container>
</template>


<script>
import UserSearch from "../../../../components/user/UserSearch";
const base_url = "/api/equity-center/cashback/user-equity";

export default {
  components: {
    UserSearch,
  },
  mounted() {
    this.search_data.equity_id = this.$route.query.equity_id;
    this.search_data.user_id = this.$route.query.user_id;
    this.get_data();
  },
  created() {
    this.$sync_router_query(this, "search_data", {
      equity_id: String,
      user_id: String,
      source_value: String,
      business_type: String,
      status: String,
      start_time: String,
      end_time: String,
    });
    let search_data = this.search_data;
    if (search_data.start_time && search_data.end_time) {
      this.search_data.date_range = [
        new Date(search_data.start_time * 1000),
        new Date(search_data.end_time * 1000),
      ];
    }
  },
  data() {
    return {
      action: null,
      total: 0,
      items: [],
      user_types: {},
      submit_data: {},
      all_equity_dict: {},
      business_type_dict: {},
      cashback_scope_dict: {},
      status_dict: {},
      // 移除级联选择器相关数据
      // CascaderValue: [],
      // cascader_options: [],
      // cascader_props: {
      //   expandTrigger: 'hover',
      //   value: 'value',
      //   label: 'label',
      //   children: 'children'
      // },
      // 新增发放ID选项数据
      source_id_options: [],
      search_data: {
        equity_id: null,
        source_value: null, // 发放ID
        business_type: null, // 发放类型
        page: 1,
        limit: 100,
        start_time: null,
        end_time: null,
      },
      filters_mid: {
        date_range: [null, null],
      },
      // 统计数据
      statistics: {
        totalRecords: 0,
        uniqueUsers: 0,
        totalCostAmount: {},
        totalRealCostAmount: {},
        totalCashbackAmount: {},
        usingCount: 0,
        usedCount: 0,
        expiredCount: 0
      },
      showStats: true, // 控制统计行显示/隐藏

      // 级联菜单相关数据
      cascader_options: [],
      cascader_props: {
        expandTrigger: 'hover',
        value: 'value',
        label: 'label',
        children: 'children'
      },
    };
  },
  methods: {
    get_request_params() {
      return _.omitBy(this.search_data, v => v === null || v === '' || v === undefined);
    },
    get_data() {
      this.loading = true;
      this.$axios.get(base_url, { params: this.get_request_params() }).then((res) => {
        this.loading = false;
        if (res.data.code === 0) {
          let data = res.data.data;
          this.items = data.items;
          this.total = data.total;
          this.status = data.status;
          // 从后端获取统计数据
          let equity_statistics = data.extra.equity_statistics
          this.statistics = {
            totalRecords: equity_statistics.equity_count || 0,
            uniqueUsers: equity_statistics.user_count || 0,
            totalCostAmount: equity_statistics.cost_amount_mapper || {},
            totalRealCostAmount: equity_statistics.real_cost_amount_list || {},
            totalCashbackAmount: equity_statistics.real_cashback_mapper || {},
            usingCount: equity_statistics.status_mapper['USING'] || 0,
            usedCount: equity_statistics.status_mapper['FINISHED'] || 0,
            expiredCount: equity_statistics.status_mapper['EXPIRED'] || 0
          };
          this.all_equity_dict = data.extra.all_equity_dict;
          this.status_dict = data.extra.status_dict;
          this.business_type_dict = data.extra.business_type_dict;
          this.cashback_scope_dict = data.extra.cashback_scope_dict;
          // 构建级联菜单选项（保留用于构建发放ID选项）
          this.cascader_options = data.extra.selector_option;
          // 更新发放ID选项
          this.update_source_id_options();
        } else {
          this.$message.error(
            `code: ${res.data.code}; message: ${res.data.message
            }; data: ${JSON.stringify(res.data?.data)}`
          );
        }
      });
    },
    handle_page_refresh() {
      this.reset_page();
      this.get_data();
    },
    reset_page() {
      this.search_data.page = 1;
    },
    clear_search() {
      this.search_data = {
        page: 1,
        limit: 100,
        source_value: null,
        business_type: null,
      };
      this.source_id_options = [];
      this.get_data();
    },
    // 修改发放类型变化处理方法
    handle_business_type_change() {
      // 清空发放ID选择
      this.search_data.source_value = null;
      // 根据发放类型更新发放ID选项
      this.update_source_id_options();
      // 刷新页面数据
      this.handle_page_refresh();
    },

    // 新增发放ID变化处理方法
    handle_source_id_change() {
      // 刷新页面数据
      this.handle_page_refresh();
    },

    // 新增更新发放ID选项的方法
    update_source_id_options() {
      if (!this.search_data.business_type) {
        this.source_id_options = [];
        return;
      }

      // 从级联选项中找到对应业务类型的子选项
      const businessType = this.search_data.business_type;
      const businessOption = this.cascader_options.find(option => option.value === businessType);

      if (businessOption && businessOption.children) {
        this.source_id_options = businessOption.children.map(child => ({
          label: child.label,
          value: child.value
        }));
      } else {
        this.source_id_options = [];
      }
    },

    download() {
      let filename;
      filename = "user-cashback-equity-list.xlsx";
      let params = { ...this.search_data, export: true };
      this.$download_from_url(base_url, filename, params);
      this.$alert("下载成功", "下载成功", {
        confirmButtonText: "确定",
      });
    },
    buildSourceLink(row) {
      if (!row.business_type || !row.source_id) return '#';

      // 根据业务类型构建不同的跳转链接
      switch (row.business_type) {
        case 'MISSION':
          if (row.scene_type === 'NEWBIE') {
            return `/operation/mission_center/newbie_plan_detail/?id=${row.plan_id}`;
          } else {
            return `/operation/mission_center/routine_plan_detail/?id=${row.plan_id}`;
          }

        case 'PLATFORM_SEND':
          // 平台直接发放，跳转到权益发放详情页
          return `/operation/equity-center/send-apply-list?apply_id=${row.source_id}`;

        case 'AIRDROP_ACTIVITY':
          // 空投活动，跳转到空投详情页
          return `/operation/activity/airdrop-activity?id=${row.source_id}`;

        case 'DEPOSIT_BONUS_ACTIVITY':
          // 推广活动，跳转到推广详情页
          return `/operation/activity/deposit-bonus-activity?id=${row.source_id}`;
      }
    },
    toggleStats() {
      this.showStats = !this.showStats;
    },

    // 移除级联选择器相关方法
    // handle_cascader_change(value) {
    //   this.search_data.source_value = value[1];
    //   this.handle_page_refresh();
    // },
  },
  watch: {
    "filters_mid.date_range": function (date_range) {
      Object.assign(this.search_data, {
        start_time:
          date_range && date_range[0] ? date_range[0] / 1000 : null,
        end_time:
          date_range && date_range[1] ? date_range[1] / 1000 : null,
      });
    },
  },
};
</script>

<style scoped>
.stats-container {
  margin-bottom: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background: #fff;
  overflow: hidden;
}

.stats-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  background: #f5f7fa;
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s;
}

.stats-header:hover {
  background: #ebeef5;
}

.stats-title {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
}

.stats-header i {
  font-size: 12px;
  color: #909399;
  transition: transform 0.3s;
}

.stats-header i.is-collapsed {
  transform: rotate(-90deg);
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: #fff;
  transition: all 0.3s ease;
  overflow: hidden;
}

.stats-content.is-collapsed {
  max-height: 0;
  padding: 0 16px;
}

.stat-item {
  flex: 1;
  text-align: center;
  padding: 0 8px;
  min-width: 0;
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  line-height: 1.2;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.stat-label {
  font-size: 11px;
  color: #909399;
  line-height: 1.2;
  white-space: nowrap;
}

.asset-inline {
  display: inline-block;
  margin-right: 8px;
  font-size: 16px;
  font-weight: 500;
}

.asset-inline:last-child {
  margin-right: 0;
}

/* 优化：多行资产数据统一样式 */
.asset-multi {
  font-size: 14px;
  font-weight: 500;
  color: #495057;
  line-height: 1.4;
  margin: 1px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
}

/* 响应式处理 */
@media (max-width: 1200px) {
  .stat-value {
    font-size: 18px;
  }

  .asset-inline {
    font-size: 14px;
    margin-right: 6px;
  }

  .asset-multi {
    font-size: 13px;
  }
}

@media (max-width: 768px) {
  .stats-content {
    flex-wrap: wrap;
    gap: 8px;
  }

  .stat-item {
    flex: 0 0 calc(50% - 4px);
    min-width: 120px;
  }
}
</style>
