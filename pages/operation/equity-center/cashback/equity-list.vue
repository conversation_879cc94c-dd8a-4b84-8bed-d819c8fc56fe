<template>
  <el-container>
    <el-main>
      <h2 style="font-family: 'Microsoft YaHei', Arial, sans-serif">返现权益管理</h2>

      <el-form :inline="true" :model="search_data">
        <el-form-item label="权益ID">
          <el-select filterable clearable allow-create @change="handle_page_refresh" v-model="search_data.equity_id"
            placeholder="<ALL>" style="width: 250px">
            <el-option v-for="(value, label) in all_equity_dict" :key="value" :label="value" :value="label"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="权益状态">
          <el-select clearable filterable v-model="search_data.status" @change="handle_page_refresh"
            placeholder="<ALL>">
            <el-option v-for="(k, v) in this.status_dict" :key="v" :label="k" :value="v"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="返现范围">
          <el-select clearable filterable v-model="search_data.cashback_scope" @change="handle_page_refresh"
            placeholder="<ALL>">
            <el-option v-for="(k, v) in this.cashback_scope_dict" :key="v" :label="k" :value="v"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="返现价值">
          <el-select v-model="search_data.cost_amount" clearable filterable @change="handle_page_refresh">
            <el-option v-for="(k, v) in this.cost_amount_dict" :key="v" :label="k" :value="v"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="返现币种">
          <el-select clearable filterable v-model="search_data.cashback_asset" @change="handle_page_refresh"
            placeholder="<ALL>">
            <el-option v-for="v in this.all_assets" :key="v" :label="v" :value="v"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="权益有效期">
          <el-select clearable filterable v-model="search_data.effective_days" @change="handle_page_refresh"
            placeholder="<ALL>">
            <el-option v-for="v in this.effective_days" :key="v" :label="v" :value="v"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="get_data">搜索</el-button>
        </el-form-item>

        <el-form-item>
          <el-button @click="clear_search">重置</el-button>
        </el-form-item>

        <el-form-item>
          <el-tooltip content="+ 创建权益" placement="right" :open-delay="500" :hide-after="2000">
            <el-button type="primary" @click="handleCreate">+ 创建权益</el-button>
          </el-tooltip>
        </el-form-item>
      </el-form>

      <el-table :data="items" style="width: 100%">
        <el-table-column prop="id" label="权益ID" fixed></el-table-column>

        <el-table-column prop="type" label="权益类型"
          :formatter="(row) => `${equity_type_dict[row.type]}`"></el-table-column>

        <el-table-column prop="cashback_scope" label="返现范围"
          :formatter="(row) => `${cashback_scope_dict[row.cashback_scope]}`"></el-table-column>

        <el-table-column prop="cost_asset" label="返现价值">
          <template slot-scope="scope">{{ scope.row.cost_amount }} {{ scope.row.cost_asset }}</template>
        </el-table-column>

        <el-table-column prop="cashback_asset" label="返现币种"></el-table-column>

        <el-table-column prop="cashback_ratio" label="返现比例">
          <template slot-scope="scope">{{ scope.row.cashback_ratio }}%</template>
        </el-table-column>

        <el-table-column prop="effective_days" label="权益有效期">
          <template slot-scope="scope">{{ scope.row.effective_days }}日</template>
        </el-table-column>

        <el-table-column prop="remark" label="备注" show-overflow-tooltip min-width="160px">
          <template slot-scope="scope">{{ scope.row.remark == null ? "-" : scope.row.remark }}</template>
        </el-table-column>

        <el-table-column prop="status" label="权益状态"
          :formatter="(row) => `${status_dict[row.status]}`"></el-table-column>

        <el-table-column label="创建人" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-link :href="'/users/user-details?id=' + scope.row.creator" type="primary" target="_blank"
              :underline="false"
              style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">{{
                scope.row.creator_name || scope.row.creator }}</el-link>
          </template>
        </el-table-column>

        <el-table-column prop="updated_at" label="更新时间(UTC+8)" :formatter="(row) => $formatDate(row.updated_at)"
          min-width="160px"></el-table-column>

        <el-table-column label="操作">
          <template slot-scope="scope">
            <span>
              <el-tooltip content="修改状态">
                <el-button size="mini" @click="handleChangeStatus(scope.row)" type="text">{{ scope.row.status == "OPEN"
                  ? "禁用" : "启用" }}</el-button>
              </el-tooltip>
            </span>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination :current-page.sync="search_data.page" :page-size.sync="search_data.limit"
        @size-change="handle_limit_change" @current-change="handle_page_change" :page-sizes="[200, 100, 50, 25]"
        :hide-on-single-page="false" layout="total, sizes, prev, pager, next, jumper" :total="total"></el-pagination>

      <el-dialog :title="action === DIALOG_CREATION ? '创建权益' : '编辑权益'" :visible.sync="edit_show" width="80%"
        :before-close="handleClose">
        <el-form :model="submit_data" ref="submit_data" :disabled="this.disabled" label-width="150px">
          <el-form-item label="权益类型" required prop="equity_type">
            <el-select clearable filterable v-model="submit_data.equity_type" disabled required>
              <el-option key="CASHBACK" label="手续费返现" value="CASHBACK"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="返现范围" required prop="cashback_scope">
            <el-select v-model="submit_data.cashback_scope" clearable filterable>
              <el-option v-for="(k, v) in this.cashback_scope_dict" :key="v" :label="k" :value="v"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="返现币种" required prop="cashback_asset">
            <el-select v-model="submit_data.cashback_asset" clearable filterable>
              <el-option v-for="v in this.all_assets" :key="v" :label="v" :value="v"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="返现价值" required>
            <el-select v-model="submit_data.cost_amount" clearable filterable disabled>
              <el-option v-for="(k, v) in this.cost_amount_dict" :key="v" :label="k" :value="v"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="返现比例" required prop="cashback_ratio">
            <el-input-number v-model="submit_data.cashback_ratio" :min="1" :max="100" :precision="0"
              placeholder="请输入返现比例" style="width: 240px"></el-input-number>%
          </el-form-item>

          <el-form-item prop="effective_days" label="权益有效期" required>
            <el-input-number v-model="submit_data.effective_days" :min="1" :max="60" :precision="0" placeholder="请输入有效期"
              style="width: 240px"></el-input-number>日
          </el-form-item>

          <el-form-item prop="remark" label="备注">
            <el-input v-model="submit_data.remark" placeholder="仅用于后台说明，不会对用户展示" maxlength="50" show-word-limit
              style="width: 900px"></el-input>
          </el-form-item>
        </el-form>

        <span slot="footer" class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="submit">确 定</el-button>
        </span>
      </el-dialog>

      <el-backtop></el-backtop>
    </el-main>
  </el-container>
</template>


<script>
const base_url = "/api/equity-center/cashback/list";

export default {
  mounted() {
    this.search_data.equity_id = this.$route.query.equity_id;
    this.get_data();
  },
  methods: {
    get_request_params() {
      return _.omitBy(this.search_data, v => v === null || v === '');
    },
    get_data() {
      this.loading = true;
      this.$axios.get(base_url, { params: this.get_request_params() }).then((res) => {
        this.loading = false;
        if (res.data.code === 0) {
          let data = res.data.data;
          this.items = data.items;
          this.total = data.total;
          this.status_dict = data.extra.status_dict;
          this.all_equity_dict = data.extra.all_equity_dict;
          this.equity_type_dict = data.extra.equity_type_dict;
          this.cashback_scope_dict = data.extra.cashback_scope_dict;
          this.all_assets = data.extra.all_assets;
          this.effective_days = data.extra.effective_days;
          this.cost_amount_dict = data.extra.cost_amount_dict;
        } else {
          this.$message.error(
            `code: ${res.data.code}; message: ${res.data.message}`
          );
        }
      });
    },
    reset_page() {
      this.search_data.page = 1;
    },
    handle_limit_change() {
      this.reset_page();
      this.get_data();
    },
    handle_page_change() {
      this.get_data();
    },
    notice(title, message) {
      this.$alert(message, title, {
        confirmButtonText: "确定",
      });
    },
    res_success_notice(res) {
      let title = "提交成功";
      this.notice(title, title);
    },
    res_fail_notice(res) {
      let title = "提交失败";
      let message = `(code: ${res.data.code}; message: ${res.data.message
        }); data: ${JSON.stringify(res.data?.data)}`;
      this.notice(title, message);
    },
    res_error_notice(error) {
      let title = "提交失败";
      let message = `(code: ${error.response.status}; message: ${error.message
        }); data: ${JSON.stringify(res.data?.data)}`;
      this.notice(title, message);
    },
    handleCreate() {
      this.edit_show = true;
      this.disabled = false;
      this.action = this.DIALOG_CREATION;
      this.submit_data = JSON.parse(JSON.stringify(this.default_submit_data));
    },
    handleEdit(row) {
      this.action = this.DIALOG_EDIT;
      this.disabled = row.status == "启用" ? true : false;
      this.submit_data = JSON.parse(JSON.stringify(row));
      this.edit_show = true;
    },
    handleClose() {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.edit_show = false;
          this.submit_data = {};
          done();
        })
        .catch((_) => { });
    },
    submit() {
      if (!this.submit_data.effective_days) {
        this.$message.error(`权益有效期不能为空`);
        return false;
      }
      if (!this.submit_data.cashback_asset) {
        this.$message.error(`返现币种不能为空`);
        return false;
      }
      if (!this.submit_data.cashback_scope) {
        this.$message.error(`返现范围不能为空`);
        return false;
      }
      if (!this.submit_data.cost_amount) {
        this.$message.error(`返现价值不能为空`);
        return false;
      }
      if (!this.submit_data.cashback_ratio) {
        this.$message.error(`返现比例不能为空`);
        return false;
      }
      this.$refs["submit_data"].validate((valid) => {
        if (!valid) {
          this.$alert("校验失败请修改", "校验失败请修改", {
            confirmButtonText: "确定",
          });
          return false;
        } else {
          if (this.action === this.DIALOG_CREATION) {
            this.handleCreateConfirm();
          } else {
            this.handleEditConfirm();
          }
        }
      });
    },
    handleCreateConfirm() {
      let method = "post";
      this.$axios[method](base_url, this.submit_data)
        .then((res) => {
          if (res.data.code === 0) {
            this.edit_show = false;
            this.res_success_notice(res);
            this.submit_data = {};
            this.get_data();
          } else {
            this.res_fail_notice(res);
          }
        })
        .catch((e) => {
          console.log(e);
          this.res_error_notice(e);
        });
    },
    handleChangeStatus(row) {
      let op_str = "";
      let new_status = "";
      if (row.status == "OPEN") {
        op_str = "禁用";
        new_status = "CLOSE";
      } else {
        op_str = "启用";
        new_status = "OPEN";
      }
      let submit_data = { id: row.id, status: new_status };
      this.$confirm("请确认是否" + op_str + "权益").then((_) => {
        this.$axios["patch"](base_url, submit_data).then((res) => {
          if (res.data.code === 0) {
            this.$message.success(op_str + "成功");
            this.get_data();
          }
        });
      });
    },
    clear_search() {
      this.search_data = _.clone(this.default_search_data);;
      this.get_data();
    },
    handle_page_refresh() {
      this.reset_page();
      this.get_data();
    },
    reset_page() {
      this.search_data.page = 1;
    },
    handleEditConfirm() {
      if (this.disabled) {
        this.edit_show = false;
        return;
      }
      let method = "put";
      this.$axios[method](
        base_url + `/${this.submit_data.id}`,
        this.submit_data
      )
        .then((res) => {
          if (res.data.code === 0) {
            this.edit_show = false;
            this.res_success_notice(res);
            this.submit_data = {};
            this.get_data();
          } else {
            this.res_fail_notice(res);
          }
        })
        .catch((e) => {
          console.log(e);
          this.res_error_notice(e);
        });
    },
    refresh() {
      this.$forceUpdate();
    },
  },
  data() {
    return {
      DIALOG_CREATION: "CREATION",
      DIALOG_EDIT: "EDIT",
      action: null,
      disabled: false,
      edit_show: false,
      total: 0,
      items: [],
      assets: [],
      effective_days: [],
      cost_amount_dict: {},
      default_submit_data: {
        equity_type: "CASHBACK",
        cost_asset: "USDT",
        cashback_asset: "CET",
        cashback_ratio: 100,
      },
      submit_data: {},
      all_assets: [],
      all_equity_dict: {},
      status_dict: {},
      equity_type_dict: {},
      cashback_scope_dict: {},
      default_search_data: {
        page: 1,
        limit: 50,
        equity_id: null,
        status: null,
        cashback_scope: null,
        cashback_asset: null,
        cost_amount: null,
        effective_days: null,
      },
      search_data: {
        page: 1,
        limit: 50,
        equity_id: null,
        status: null,
        cashback_scope: null,
        cashback_asset: null,
        cost_amount: null,
        effective_days: null,
      },
    };
  },
};
</script>

<style scoped>
.el-input {
  width: auto;
}
</style>
