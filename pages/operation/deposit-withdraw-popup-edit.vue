<template>
  <div class="table-data">
    <el-row type="flex" justify="space-between" align="middle">
      <el-col :span="22">
        <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
          弹窗设置
        </h2>
      </el-col>

      <el-col :span="1">
        <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
          <el-button icon="el-icon-refresh-left"
                     circle
                     @click="get_data"></el-button>
        </el-tooltip>
      </el-col>
    </el-row>
    <el-collapse v-model="activeNames">
      <el-collapse-item name="0">
        <template slot="title"><h3 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">基本信息</h3></template>
        <el-form :model="filters_form"
                 label-width="100px"
                 v-loading="loading">
          <el-form-item label="名称: " required>
            <el-input v-model="filters_form.name" style="width: 300px">
            </el-input>
          </el-form-item>

          <el-form-item label="链" required>
            <el-select
              v-model="filters_form.chain"
              @change="handle_form_chain_selection"
              :disabled="$route.query.id !== '0'"
              filterable
              clearable
            >
              <el-option v-for="c in chain_list" :label="c" :value="c" :key="c"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="币种" required>
            <el-select v-model="filters_form.asset" :disabled="$route.query.id !== '0'" filterable clearable>
              <el-option v-for="a in asset_list" :label="a" :value="a" :key="a"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="类型: " required>
            <el-radio-group v-model="filters_form.offline_type">
              <template v-for="(name, key) in offline_type_dict">
                <el-radio :label="key">{{ name }}</el-radio>
              </template>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="平台: " required>
            <el-radio-group v-model="filters_form.platform" @change="changeSelectableTriggerPages">
              <template v-for="(name, key) in platforms">
                <el-radio :label="key">{{ name.toUpperCase() }}</el-radio>
              </template>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="触发页: " required>
            <el-radio-group v-model="filters_form.trigger_page">
              <template v-for="(name, key) in trigger_pages">
                <el-radio :label="key">{{ name }}</el-radio>
              </template>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="频率: " required>
            <el-radio-group v-model="filters_form.frequency">
              <template v-for="(name, key) in frequencies">
                <el-radio :label="key">{{ name }}</el-radio>
              </template>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="开始时间:" required>
            <el-date-picker v-model="filters_form.started_at"
                            type="datetime"
                            clearable
                            style="width: 300px">
            </el-date-picker>
          </el-form-item>

          <el-form-item label="结束时间:" required>
            <el-date-picker v-model="filters_form.ended_at"
                            type="datetime"
                            clearable
                            style="width: 300px">
            </el-date-picker>
          </el-form-item>

          <el-form-item label="是否跳转: " required>
            <el-radio-group v-model="filters_form.jump_page_enabled">
              <el-radio :label="false">否</el-radio>
              <el-radio :label="true">是</el-radio>
            </el-radio-group>
          </el-form-item>

          <span v-if="filters_form.jump_page_enabled">
            <el-form-item label="跳转类型: " required>
              <el-select v-model="filters_form.jump_type" style="width: 300px" @change="getTargetPages()">
                <el-option v-for="(label, value) in selectable_jump_types"
                           :key="value"
                           :label="label"
                           :value="value">
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="目标页: " required>
              <el-select v-model="filters_form.jump_id" style="width: 300px" filterable>
                <el-option v-for="target_page in target_pages"
                           :key="target_page.id"
                           :label="`${target_page.remark} -- ${target_page.jump_data}`"
                           :value="target_page.id">
                </el-option>
              </el-select>
              <el-button icon="el-icon-s-primary" type="primary" @click="handleJumpPages">跳转管理</el-button>
            </el-form-item>
          </span>

        </el-form>
      </el-collapse-item>
      <el-collapse-item name="1">
        <template slot="title"><h3 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">目标客群</h3></template>
          <el-form>
            <el-form-item label="客群类型" required>
              <el-radio-group v-model="filters_form.filter_type">
                <el-radio v-for="(val, key) in filter_types" :key="key" :label="key">{{ val }}</el-radio>
              </el-radio-group>
            </el-form-item>
            <template v-if="filters_form.filter_type === 'FILTERS'">
              <UserGroupPicker :tagGroups="tagGroups" :dialogUserGroupMap="dialogUserGroupMap"></UserGroupPicker>
              <el-form :model="filters_form" :inline="true">
                <el-form-item>
                  <el-checkbox v-model="filters_form.whitelist_enabled">是否剔除免打扰客群</el-checkbox>
                </el-form-item>
                <template v-if="filters_form.whitelist_enabled">
                  <el-form-item>
                    <el-input v-model="filters_form.user_whitelist" :disabled="true"></el-input>
                  </el-form-item>
                  <el-form-item>
                    <el-tooltip content="上传" placement="right" :open-delay="500" :hide-after="2000">
                      <el-upload
                        ref="upload"
                        action="/api/operation/email-push/imports"
                        name="file"
                        :data="{value_type: 'user'}"
                        :headers="{AUTHORIZATION: $cookies.get('admin_token')}"
                        :before-upload="before_upload"
                        :on-success="res => upload_success(res, filters_form)"
                        :on-error="upload_error"
                        :show-file-list="false">
                        <el-button type="primary" size="mini" icon="el-icon-upload" circle></el-button>
                      </el-upload>
                    </el-tooltip>
                  </el-form-item>
                  <el-form-item>
                <el-tooltip content="下载模板" placement="right" :open-delay="500" :hide-after="2000">
                  <el-button type="primary" icon="el-icon-download" circle size="mini" style="margin-left: 10px;"
                             @click="download_template"></el-button>
                </el-tooltip>
          </el-form-item>
        </template>
      </el-form>
    </template>
    <el-form-item>
      <el-button type="primary" @click="save_to_draft()">
        保存规则
      </el-button>
    </el-form-item>
  </el-form>
      </el-collapse-item>
      <template v-if="$route.query.id !== '0' && contents_visible">
        <el-collapse-item name="1">
          <template slot="title">
            <h3 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">通知内容</h3>&#12288;
          </template>
          <MessageEditor
            :messageConfig="messageConfig"
            :contents="contents"
            :languages="languages"
            :template_filters="template_filters"
            :disabled="false"
            @afterSave="afterSaveDisplay"
          ></MessageEditor>
        </el-collapse-item>
      </template>
    </el-collapse>

    <UserTagGroupDialog :tagGroups="tagGroups" :dialogUserGroupMap="dialogUserGroupMap"></UserTagGroupDialog>

    <el-dialog
      title="跳转管理"
      :visible.sync="jump_show"
      width="70%">
      <el-tooltip content="新建" placement="right" :open-delay="500" :hide-after="2000">
        <el-button type="primary" icon="el-icon-plus" circle
                   @click="handleJumpCreate"></el-button>
      </el-tooltip>
      <el-table :data="jump_pages"
                stripe>
        <el-table-column
          prop="remark"
          label="跳转标注">
        </el-table-column>

        <el-table-column
          prop="jump_data"
          label="跳转链接">
        </el-table-column>

        <el-table-column
          prop="jump_type"
          :formatter="row => jump_types[row.jump_type]"
          label="跳转类型">
        </el-table-column>

        <el-table-column
          prop="operation"
          label="操作">
          <template slot-scope="scope">
            <el-tooltip content="编辑" placement="right" :open-delay="500" :hide-after="2000">
              <el-button size="mini" type="primary" icon="el-icon-edit" circle
                         @click="handleJumpEdit(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="right" :open-delay="500" :hide-after="2000">
              <el-button size="mini" type="danger" icon="el-icon-delete" circle
                         @click="handleDeleteJump(scope.row)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>

      </el-table>
    </el-dialog>

    <el-dialog
      :title="jump_action === DIALOG_CREATION? '添加跳转' : '编辑跳转'"
      :visible.sync="jump_action_show"
      :before-close="handleClose"
      width="60%">

      <el-form :model="jump_data" ref="jump_data" label-width="80px" :validate-on-rule-change="false">

      <el-form-item label="跳转标示">
        <el-input v-model="jump_data.remark"></el-input>
      </el-form-item>

      <el-form-item label="跳转类型" required>
        <el-select clearable v-model="jump_data.jump_type">
          <el-option v-for="(value, key) in jump_types"
                     :key="value"
                     :label="value"
                     :value="key">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="跳转链接" required>
        <el-input v-model="jump_data.jump_data"></el-input>
      </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="jump_submit">确 定</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
import Vue from 'vue';
import tinymce from 'tinymce/tinymce'
import 'tinymce/themes/modern/theme'
import 'tinymce/plugins/image'
import 'tinymce/plugins/link'
import 'tinymce/plugins/code'
import 'tinymce/plugins/table'
import 'tinymce/plugins/lists'
import 'tinymce/plugins/contextmenu'
import 'tinymce/plugins/wordcount'
import 'tinymce/plugins/colorpicker'
import 'tinymce/plugins/textcolor'
import moment from "moment";
import {Chart} from 'highcharts-vue';
import VueClipboard from 'vue-clipboard2';
import UserTagGroupDialog from "@/components/UserTagGroupDialog";
import UserGroupPicker from "@/components/UserGroupPicker";
import MessageEditor from "@/components/MessageEditor.vue";


Vue.use(VueClipboard);

export default {
  components: {
    MessageEditor,
    highcharts: Chart,
    UserTagGroupDialog,
    UserGroupPicker,
  },
  methods: {
    get_data() {
      let id = this.$route.query.id;
      if (id === '0') {
        this.activeNames = ['0', '1', '4'];
      }
      this.loading = true;
      this.$axios.get(`/api/asset/deposits/deposit-withdraw-popup-windows/${id}`).then(res => {
        this.loading = false;
        if (res?.data?.code === 0) {
          let data = res.data.data;
          this.assign_form(data);
          this.fetchJumpPages();
          this.get_contents();
          this.updateSelectableTriggerPages();
        } else {
          this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
        }
      });
    },
    assign_form(data) {
      // debugger;
      let form = this.filters_form;
      form.name = data.name;
      form.remark = data.remark;
      form.platform = data.platform;
      form.trigger_page = data.trigger_page;
      form.frequency = data.frequency;
      form.filter_type = data.filter_type;
      form.offline_type = data.offline_type;
      form.groups = data.groups;
      form.whitelist_enabled = data.whitelist_enabled;
      form.user_whitelist = data.user_whitelist;
      form.asset = data.asset === ''? "ALL": data.asset;
      form.chain = data.chain;
      form.started_at = data.started_at ? new Date(data.started_at * 1000) : null;
      form.ended_at = data.ended_at ? new Date(data.ended_at * 1000) : null;
      form.jump_type = data.jump_type || null;
      form.jump_id = data.jump_id || null;
      form.jump_page_enabled = data.jump_page_enabled;
      this.cur_status = data.status;
      let extra = data.extra;
      this.chain_assets_dict = extra.chain_assets_dict;
      this.chain_list = Object.keys(this.chain_assets_dict);
      this.asset_dict = extra.assets;
      this.platforms = extra.platforms;
      this.offline_type_dict = extra.offline_type_dict;
      this.trigger_pages = extra.trigger_pages;
      this.filter_types = extra.filter_types;
      this.jump_types = extra.jump_types;
      this.selectable_jump_types = {...this.jump_types};
      this.tagGroups = data.extra.tag_groups || [];
      this.frequencies = extra.frequencies;
      this.assets = extra.assets;
      this.spot_markets = extra.spot_markets;
      this.perpetual_markets = extra.perpetual_markets;
      this.languages = extra.languages;
      let lang_list = Object.keys(this.languages);
      if (!lang_list.includes(this.cur_lang)) {
        this.messageConfig.cur_lang = lang_list[0];
      }
      this.contents = Object.fromEntries(lang_list.map(lang => [lang, this.contents[lang] || {
        title: '',
        content: '',
      }]));
    },

    fetchJumpPages() {
      let url = '/api/operation/page/new-app-entrances/jump-list';
      let jump_pages = this.jump_pages;
      this.$axios.get(url).then(res => {
        if (res?.data?.code === 0) {
          jump_pages = res.data.data;
          jump_pages.forEach((e) => {
            if(e.jump_type === '原生') {
              e.jump_type = 'NATIVE';
            }
          });
          this.jump_pages = jump_pages;
          this.getTargetPages(true);
        } else {
          this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
        }
      });
    },
    getTargetPages(init=false) {
      // debugger;
      if(!init) {this.filters_form.jump_id = null;}
      let jumpType = this.filters_form.jump_type;
      let target_pages = [];
      this.jump_pages.forEach((e) => {
            if(e.jump_type === jumpType) {
              target_pages.push({
                  id: e.id,
                  remark: e.remark,
                  jump_data: e.jump_data,
              })
            }});
      this.target_pages = target_pages;
    },
    handleJumpPages() {
      this.jump_show = true;
    },
    handleJumpCreate() {
      this.jump_action = this.DIALOG_CREATION;
      this.jump_data = {};
      this.jump_action_show = true;
    },
    handleJumpEdit(row) {
      this.jump_action = this.DIALOG_EDIT;
      this.jump_data = _.clone(row);
      this.jump_action_show = true;
    },
    handleDeleteJump(row) {
      this.$confirm(`确认删除跳转 ${row.id} ${row.remark}?`).then(() => {
        this.$axios.delete(
          `/api/operation/page/new-app-entrances/jump-list/${row.id}`,
        ).then(
          res => {
            this.fetchJumpPages();
            if (res.data.code === 0) {
              this.dialog_show = false;
              this.jump_show = false;
              this.jump_action_show = false;
              this.jump_action = '';
              this.$message.success("删除成功!");
              this.loading = false;
            } else {
              this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
            }
          }
        ).catch(err => {
          this.$message.error(`失败! (${err})`);
        });
      });
    },
    jump_submit() {
      this.$refs["jump_data"].validate((valid) => {
        if (!valid) {
          this.$alert('校验失败请修改', '校验失败请修改', {
            confirmButtonText: '确定'
          });
        } else {
            let method = 'put';
            if (this.jump_action === this.DIALOG_CREATION) {
              method = "post";
            }
            this.$axios[method](
              '/api/operation/page/new-app-entrances/jump-list',
              this.jump_data
            ).then(
              res => {
                this.fetchJumpPages();
                if (res.data.code === 0) {
                  this.res_success_notice(res);
                  this.dialog_show = false;
                  this.jump_show = false;
                  this.jump_action_show = false;
                  this.jump_action = '';
                } else {
                  this.res_fail_notice(res);
                }
              }
            ).catch(_ => {
              this.res_error_notice(res);
            });
        }
      });
    },

    notice(title, message) {
      this.$alert(message, title, {
        confirmButtonText: '确定',
      });
    },
    res_success_notice(r) {
      let title = "提交成功";
      this.notice(title, title);
    },
    res_fail_notice(res) {
      let title = "提交失败";
      let message = `(code: ${res.data?.code}; message: ${res.data?.message})`;
      this.notice(title, message);
    },
    res_error_notice(error) {
      let title = "提交失败";
      let message = `(code: ${error.response.status}; message: ${error.message})`;
      this.notice(title, message);
    },

    format_date(timestamp, pattern = 'YYYY-MM-DD HH:mm:ss') {
      return moment(Number(timestamp) * 1000).format(pattern);
    },
    get_contents(id = 0) {
      if (!id) {
        id = this.$route.query.id;
      }
      if (id === '0') {
        return;
      }
      let contents = this.contents;
      let lang_list = Object.keys(this.languages);
      lang_list.forEach(lang => {
        this.$axios.get(`/api/asset/deposits/deposit-withdraw-popup-windows/${id}/langs/${lang}`).then(res => {
          if (res?.data?.code === 0) {
            let data = res.data.data;
            contents[lang] = {
              title: data.title,
              content: data.content,
            };
            this.contents_visible = lang_list.every(lang => contents[lang]);
          }
        })
      });
    },
    save_to_draft() {
      this.save_form('DRAFT');
    },
    async save_form(status) {
      // debugger;
      let form = this.$deepcopy(this.filters_form)
      form.status = status;
      if (form.asset === 'ALL') {
        form.asset = ''
      }
      form.started_at = this.filters_form.started_at
      form.ended_at = this.filters_form.ended_at
      if (!form.name) {
        this.$message.error('请输入名称!');
        return;
      }
      if (!form.platform) {
        this.$message.error('请输入平台!');
        return;
      }
      if (!form.frequency) {
        this.$message.error('请输入频率!');
        return;
      }
      if (!form.trigger_page) {
        this.$message.error('请输入触发页!');
        return;
      }
      let paramPages = {};
      let pages = [];
      let checkSuccess = true;
      if (!form.started_at) {
        this.$message.error('请输入开始时间!');
        return;
      }
      if (!form.ended_at) {
        this.$message.error('请输入结束时间!');
        return;
      }
      if (!form.jump_page_enabled) {
        form.jump_page_enabled = false;
        delete form.jump_id;
        delete form.jump_type;
      } else {
        if(!form.jump_type) {
          this.$message.error('请输入跳转类型!');
          return;
        }
        if(!form.jump_id) {
          this.$message.error('请输入跳转目标页!');
          return;
        }
      }
      if (!form.filter_type) {
        this.$message.error('请选择客群类型!');
        return;
      }
      if (form.filter_type !== 'NONE') {
        if (!this.tagGroups || this.tagGroups.length === 0) {
          this.$message.error('请选择客群!');
          return;
        }
      }
      let groups = [];
      this.tagGroups.forEach(group => {
        groups.push(group.id)
      })
      form.groups = groups;
      let id = this.$route.query.id;

      if (id === '0') {
        this.loading = true;

        this.$axios.post(`/api/asset/deposits/deposit-withdraw-popup-windows`, form).then(res => {
          this.loading = false;
          if (res?.data?.code === 0) {
            let data = res.data.data;
            let id = data.id;
            this.$router.replace({query: {id: id}});
            this.$message.success("保存成功!");
            this.get_contents(id);
          } else {
            this.$message.error(`保存失败! (code: ${res.data?.code}; message: ${res.data?.message})`);
          }
        }).catch(err => {
          this.loading = false;
          this.$message.error(`提交失败! (${err})`);
        });
      } else {
        this.loading = true;
        this.$axios.put(`/api/asset/deposits/deposit-withdraw-popup-windows/${id}`, form).then(res => {
          this.loading = false;
          if (res?.data?.code === 0) {
            let data = res.data.data;
            this.cur_status = data.status;
            this.$message.success("保存成功!");
          } else {
            this.$message.error(`保存失败! (code: ${res.data?.code}; message: ${res.data?.message})`);
          }
        }).catch(err => {
          this.loading = false;
          this.$message.error(`保存失败! (${err})`);
        });
      }
      this.afterSaveDisplay();
    },

    afterSaveDisplay() {
      this.activeNames = ['0', '1', '2'];
    },
    handle_form_chain_selection() {
      if (!this.filters_form['chain']) {
        return;
      }
      var asset_list = _.clone(this.chain_assets_dict[this.filters_form['chain']]);
      asset_list.splice(0, 0, 'ALL');
      if (this.filters_form.asset != null && !asset_list.includes(this.filters_form['asset'])) {
        this.filters_form.asset = null;
      }
      this.asset_list = asset_list;
    },
    download_template() {
      let url = '/api/operation/email-push/template';
      this.$download_from_url(url, 'email-push-template.xlsx')
    },
    before_upload(file) {
      let name = file.name;
      if (!name.endsWith('.xlsx') && !name.endsWith('.xls') && !name.endsWith('csv')) {
        this.$message.error("只能上传excel表格");
        return false;
      }
    }
    ,
    upload_success(res, filters_form) {
      if (res?.code === 0) {
        // debugger;
        filters_form.user_whitelist = res.data.items.join();
        this.$message.success(`共导入 ${res.data.total} 条记录`);
      } else {
        this.$message.error(`上传失败! (code: ${res?.code}; message: ${res?.message})`);
      }
    },
    upload_error(err) {
      this.$message.error(`上传失败! (${err})`);
    },
    handleImgUpload(blobInfo, success, failure) {
      let formdata = new FormData()
      formdata.set('img', blobInfo.blob())
      this.$axios.post('/api/upload/image', formdata).then(res => {
        if (res.data.code === 0) {
          success(res.data.data.file_url)
        } else {
          failure(`code: ${res.data?.code}; message: ${res.data?.message}`);
        }
      }).catch(res => {
        failure('error');
      })
    },
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          this.dialog_push_test = false;
          this.dialog_template = false;
          done();
        })
        .catch(_ => {
        });
    },
    changeSelectableTriggerPages() {
      this.filters_form.jump_type = null;
      this.filters_form.trigger_pages = [];
      this.selectable_trigger_pages = {...this.trigger_pages};
      this.selectable_jump_types = {...this.jump_types};
      this.updateSelectableTriggerPages();
    },
    updateSelectableTriggerPages() {
      if (this.filters_form.platform === 'APP') {
        delete this.selectable_trigger_pages['FIAT'];
      } else if (this.filters_form.platform === 'ALL') {
        delete this.selectable_trigger_pages['FIAT'];
        delete this.selectable_trigger_pages['ACCOUNT_ASSET'];

        delete this.selectable_jump_types['NATIVE'];
      } else if (this.filters_form.platform === 'WEB') {
        delete this.selectable_jump_types['NATIVE'];
      }
    },
  },
  mounted() {
    tinymce.init({})
    this.get_data();
  },
  data() {
    return {
      messageConfig: {
        extr_params: {},
        has_title: true,
        use_editor: true,
        save_url: "/api/asset/deposits/deposit-withdraw-popup-windows/${id}/langs/${lang}",
        cur_lang: null,
        has_test: false,
        no_translate: true
      },
      filters_form: {
        name: '',
        platform: null,
        offline_type: null,
        frequency: null,
        trigger_page: null,
        filter_type: null,
        groups: null,
        whitelist_enabled: false,
        user_whitelist: '',
        started_at: null,
        ended_at: null,
        jump_type: null,
        jump_id: null,
        jump_page_enabled: false,
        chain: null,
        asset: null,
        remark: null,
      },

      jump_action: null,
      jump_show: false,
      jump_action_show: false,
      jump_data: {},
      DIALOG_CREATION: 'CREATION',
      DIALOG_EDIT: 'EDIT',

      assets: [],
      spot_markets: [],
      perpetual_markets: [],
      platforms: {},
      jump_types: {},
      selectable_jump_types: {},
      jump_pages: [],
      trigger_pages: {},
      filter_types: {},
      frequencies: {},
      target_pages: [],
      chain_assets_dict: {},
      chain_list: [],
      asset_dict: {},
      asset_list: [],
      offline_type_dict: {},
      languages: {},
      cur_lang: null,
      cur_status: null,
      contents: {},
      contents_visible: false,
      dialog_template: false,
      template_items: [],
      template_filters: {
        business: 'DEPOSIT_WITHDRAW_WINDOW',
        enabled: true,
        title: null,
        page: null,
        limit: 10
      },
      template_total: 0,
      template_loading: false,

      tagGroups: [],
      dialogUserGroupMap: {
        dialogUserGroup: false,
        dialogUserGroupTotal: 0,
        dialogUserGroupItems: [],
        group_types: {},
        dialogUserGroupLoading: false,
        dialogUserGroupFilters: {
          name: null,
          page: null,
          limit: 10
        },
      },

      activeNames: ['0'],
      audit_data: [],
      loading: false,
      url_loading: false,
    }
  }
}

</script>
