<template>
  <div class="table-data">
    <el-row type="flex" justify="space-between" align="middle">
      <el-col :span="22">
        <h2 style="font-family: 'Microsoft YaHei', Arial, sans-serif">
          {{ action === DIALOG_CREATION ? "新建" : "编辑" }}活动专区
        </h2>
      </el-col>
    </el-row>
    <el-collapse v-model="activeNames" @change="handleCollapseChange">
      <el-collapse-item name="1">
        <template slot="title">
          <h2>基本信息</h2>
        </template>
        <el-form :model="form" ref="form" :rules="baseRules" label-width="150px" v-loading="loading"
          :disabled="isDisabledEdit">
          <el-form-item label="名称" required prop="name">
            <el-input v-model="form.name"></el-input>
          </el-form-item>

          <el-form-item label="活动数量" required prop="activity_count">
            <el-select v-model="form.activity_count" clearable filterable>
              <el-option v-for="value in AcitivtyCount" :key="value" :label="value" :value="value"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="开始时间" required prop="start_at">
            <el-date-picker required v-model="form.start_at" type="datetime" format="yyyy-MM-dd HH:mm:ss"
              value-format="timestamp" placeholder="开始时间">
            </el-date-picker>
            <el-row>
              <span>UTC时间：{{ $formatUTCDate(form.start_at) }}</span>
            </el-row>
          </el-form-item>

          <el-form-item label="结束时间:" required prop="start_at">
            <el-date-picker required v-model="form.end_at" type="datetime" format="yyyy-MM-dd HH:mm:ss"
              value-format="timestamp" placeholder="结束时间"></el-date-picker>
            <el-row>
              <span>UTC时间：{{ $formatUTCDate(form.end_at) }}</span>
            </el-row>
          </el-form-item>

          <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark"></el-input>
          </el-form-item>
        </el-form>
        <div v-if="!isDisabledEdit">
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="debounceSubmitBase" :disabled="isDisabledEdit">保存</el-button>
            <el-button type="danger" @click="cancelBase">取消</el-button>
          </span>
        </div>
      </el-collapse-item>

      <div v-if="$route.query.id !== '0'">
        <el-collapse-item v-for="(i, j) in activitiesData" v-bind:key="j" :name="j + 2">
          <template slot="title">
            <h2>活动{{ j + 1 }}配置</h2>
          </template>
          <h3>活动文案</h3>
          <el-form label-width="150px" :disabled="isDisabledEdit">
            <el-tabs v-model="cur_lang" type="card">
              <el-tab-pane v-for="(v, k) in langs" :key="k" :label="v" :name="k" :value="k">
                <el-form-item label="主标题" required>
                  <el-input v-model="activitiesData[j].contents[cur_lang].title"></el-input>
                </el-form-item>
                <el-form-item label="副标题" required>
                  <el-input v-model="activitiesData[j].contents[cur_lang].subtitle"></el-input>
                </el-form-item>
              </el-tab-pane>
            </el-tabs>
            <el-form-item>
              <el-row :gutter="1">
                <el-col :span="3">
                  <el-upload ref="upload"
                    :action="`/api/operation/perpetual-topic/activities/perpetual/special/template-upload`"
                    name="batch-upload" :show-file-list="false"
                    :headers="{ AUTHORIZATION: $cookies.get('admin_token') }"
                    :on-success="(res) => importSuccess(j, res)" :on-error="importError" accept=".xlsx">
                    <el-button type="primary">上传</el-button>
                  </el-upload>
                </el-col>
                <el-col :span="4">
                  <el-button type="primary" @click="handleDownloadTemplate">下载模板</el-button>
                </el-col>
              </el-row>
            </el-form-item>
            <h3>其他配置</h3>
            <el-form-item label="跳转类型" required>
              <el-select v-model="activitiesData[j].jump_type">
                <el-option key="URL" value="URL" label="URL"></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="跳转链接: " required>
              <el-select style="width: 300px" filterable v-model="activitiesData[j].jump_id">
                <el-option v-for="target_page in target_pages" :key="target_page.id"
                  :label="`${target_page.remark} -- ${target_page.jump_data}`" :value="target_page.id">
                </el-option>
              </el-select>
              <el-button icon="el-icon-s-primary" type="primary" @click="handleJumpPages">跳转管理</el-button>
            </el-form-item>
            <h4 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">LTR</h4>
            <el-form-item label="图片（白天模式）" required>
              <el-upload class="img-uploader" ref="upload-icon" :show-file-list="false" :action="`/api/upload/image`"
                name="img" :headers="{ AUTHORIZATION: $cookies.get('admin_token') }" :on-success="(res) => {
                  if (res.code === 0) {
                    activitiesData[j].daylight_file_key = res.data.file_key;
                    activitiesData[j].daylight_url = res.data.file_url;
                  } else {
                    $message.error(
                      `提交失败! (code: ${res.code}; message: ${res.message})`
                    );
                  }
                }
                  ">
                <span v-if="activitiesData[j].daylight_url">
                  <img :src="activitiesData[j].daylight_url" class="img" alt="" width="100%" />
                </span>
                <i v-else class="el-icon-plus img-uploader-icon"></i>
              </el-upload>
            </el-form-item>
            <el-form-item label="图片（夜间模式）" required>
              <el-upload class="img-uploader" ref="upload-icon" :show-file-list="false" :action="`/api/upload/image`"
                name="img" :headers="{ AUTHORIZATION: $cookies.get('admin_token') }" :on-success="(res) => {
                  if (res.code === 0) {
                    activitiesData[j].night_file_key = res.data.file_key;
                    activitiesData[j].night_url = res.data.file_url;
                  } else {
                    $message.error(
                      `提交失败! (code: ${res.code}; message: ${res.message})`
                    );
                  }
                }
                  ">
                <span v-if="activitiesData[j].night_url">
                  <img :src="activitiesData[j].night_url" class="img" alt="" width="100%" />
                </span>
                <i v-else class="el-icon-plus img-uploader-icon"></i>
              </el-upload>
            </el-form-item>
            <h4 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">RTL</h4>
            <el-form-item label="图片（白天模式）" required>
              <el-upload class="img-uploader" ref="upload-icon" :show-file-list="false" :action="`/api/upload/image`"
                name="img" :headers="{ AUTHORIZATION: $cookies.get('admin_token') }" :on-success="(res) => {
                  if (res.code === 0) {
                    activitiesData[j].rtl_daylight_file_key = res.data.file_key;
                    activitiesData[j].rtl_daylight_url = res.data.file_url;
                  } else {
                    $message.error(
                      `提交失败! (code: ${res.code}; message: ${res.message})`
                    );
                  }
                }
                  ">
                <span v-if="activitiesData[j].rtl_daylight_url">
                  <img :src="activitiesData[j].rtl_daylight_url" class="img" alt="" width="100%" />
                </span>
                <i v-else class="el-icon-plus img-uploader-icon"></i>
              </el-upload>
            </el-form-item>
            <el-form-item label="图片（夜间模式）" required>
              <el-upload class="img-uploader" ref="upload-icon" :show-file-list="false" :action="`/api/upload/image`"
                name="img" :headers="{ AUTHORIZATION: $cookies.get('admin_token') }" :on-success="(res) => {
                  if (res.code === 0) {
                    activitiesData[j].rtl_night_file_key = res.data.file_key;
                    activitiesData[j].rtl_night_url = res.data.file_url;
                  } else {
                    $message.error(
                      `提交失败! (code: ${res.code}; message: ${res.message})`
                    );
                  }
                }
                  ">
                <span v-if="activitiesData[j].rtl_night_url">
                  <img :src="activitiesData[j].rtl_night_url" class="img" alt="" width="100%" />
                </span>
                <i v-else class="el-icon-plus img-uploader-icon"></i>
              </el-upload>
            </el-form-item>
          </el-form>
          <div v-if="!isDisabledEdit">
            <span slot="footer" class="dialog-footer">
              <el-button type="primary" @click="submit(j)" :disabled="isDisabledEdit">保存</el-button>
              <el-button type="danger" @click="cancelBase">取消</el-button>
            </span>
          </div>
        </el-collapse-item>
      </div>
      <div v-if="['ONLINE', 'OFFLINE'].includes(status)">
        <el-collapse-item name="5">
          <template slot="title">
            <h2>数据统计</h2>
          </template>
          <el-row type="flex" justify="space-between" align="middle">
            <el-col :span="5">
              <template>
                <el-table :data="baseData" border>
                  <el-table-column prop="index" align="center" label="序号">
                  </el-table-column>
                  <el-table-column prop="name" align="center" label="活动名称">
                  </el-table-column>
                  <el-table-column prop="count" align="center" label="访问次数">
                  </el-table-column>
                </el-table>
              </template>
            </el-col>
            <el-col :span="12" style="position: relative">
              <highcharts class="hc" :reflow="true" :options="chartOptions"></highcharts>
            </el-col>
          </el-row>
        </el-collapse-item>
      </div>
    </el-collapse>
    <el-dialog title="跳转管理" :visible.sync="jump_show" width="70%">
      <el-tooltip content="新建" placement="right" :open-delay="500" :hide-after="2000">
        <el-button type="primary" icon="el-icon-plus" circle @click="handleJumpCreate"></el-button>
      </el-tooltip>
      <el-table :data="jump_pages" stripe>
        <el-table-column prop="remark" label="跳转标注"> </el-table-column>

        <el-table-column prop="jump_data" label="跳转链接"> </el-table-column>

        <el-table-column prop="jump_type" :formatter="(row) => jump_types[row.jump_type]" label="跳转类型">
        </el-table-column>

        <el-table-column prop="operation" label="操作">
          <template slot-scope="scope">
            <el-tooltip content="编辑" placement="right" :open-delay="500" :hide-after="2000">
              <el-button size="mini" type="primary" icon="el-icon-edit" circle
                @click="handleJumpEdit(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="right" :open-delay="500" :hide-after="2000">
              <el-button size="mini" type="danger" icon="el-icon-delete" circle
                @click="handleDeleteJump(scope.row)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <el-dialog :title="jump_action === DIALOG_CREATION ? '添加跳转' : '编辑跳转'" :visible.sync="jump_action_show"
      :before-close="handleClose" width="60%">
      <el-form :model="jump_data" ref="jump_data" label-width="80px" :validate-on-rule-change="false">
        <el-form-item label="跳转标示">
          <el-input v-model="jump_data.remark"></el-input>
        </el-form-item>

        <el-form-item label="跳转类型" required>
          <el-select clearable v-model="jump_data.jump_type">
            <el-option v-for="(value, key) in jump_types" :key="value" :label="value" :value="key">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="跳转链接" required>
          <el-input v-model="jump_data.jump_data"></el-input>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="jump_submit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>


<script>
import moment from "moment";
import UserGroupPicker from "@/components/UserGroupPicker";
import UserTagGroupDialog from "@/components/UserTagGroupDialog";
import { Chart } from "highcharts-vue";
export default {
  components: {
    UserGroupPicker,
    UserTagGroupDialog,
    highcharts: Chart,
  },
  methods: {
    handleDownloadTemplate() {
      let url = "/api/operation/perpetual-topic/activities/perpetual/special/template";
      this.$download_from_url(url, 'perpetual-topic-template.xlsx')
    },
    importError(err, file, fileList) {
      this.$alert("上传失败", "错误")
    },
    importSuccess(idx, response) {

      if (response.code !== 0) {
        this.$alert(response.message, '错误'
        ).then(() => {
        }).catch(() => {
        });
        return;
      }
      this.$alert('已成功上传，请检查翻译', '成功');

      let tans_data = {}

      response.data.forEach((e) => {
        let tmp = { title: '', subtitle: '' }
        if (e.title) {
          tmp.title = e.title.toString()
        }
        if (e.subtitle) {
          tmp.subtitle = e.subtitle.toString()
        }
        tans_data[e.lang] = tmp
      })
      this.activitiesData[idx].contents = tans_data;

    },
    submit(index) {
      let id_ = this.$route.query.id;
      let activityData = this.activitiesData[index];
      if (!activityData.jump_type) {
        this.$message.error(`请选择跳转类型`);
        return;
      }
      if (!activityData.jump_id) {
        this.$message.error(`请选择跳转链接`);
        return;
      }
      if (!activityData.night_file_key) {
        this.$message.error(`请上传夜间模式图片`);
        return;
      }
      if (!activityData.daylight_file_key) {
        this.$message.error(`请上传模式图片`);
        return;
      }
      let isHaveContent = false;
      for (var key in activityData.contents) {
        let value = activityData.contents[key];
        if (value.title && value.subtitle) {
          isHaveContent ||= true;
        }
      }
      if (!isHaveContent) {
        this.$message.error(`至少有一种语言的完整数据`);
        return;
      }
      if (activityData.activity_id) {
        this.$axios
          .put(`/api/operation/perpetual-topic/activities/${id_}`, activityData)
          .then((res) => {
            this.loading = false;
            if (res?.data?.code === 0) {
              let data = res.data.data;
              this.activitiesData[index].activity_id = data.id;
              this.$message.success("保存成功!");
            } else {
              this.$message.error(
                `保存失败! (code: ${res.data?.code}; message: ${res.data?.message})`
              );
            }
          })
          .catch((err) => {
            this.loading = false;
            this.$message.error(`提交失败! (${err})`);
          });
      } else {
        this.$axios
          .post(
            `/api/operation/perpetual-topic/activities/${id_}`,
            activityData
          )
          .then((res) => {
            this.loading = false;
            if (res?.data?.code === 0) {
              let data = res.data.data;
              this.activitiesData[index].activity_id = data.id;
              this.$message.success("保存成功!");
            } else {
              this.$message.error(
                `保存失败! (code: ${res.data?.code}; message: ${res.data?.message})`
              );
            }
          })
          .catch((err) => {
            this.loading = false;
            this.$message.error(`提交失败! (${err})`);
          });
      }
    },
    handleCollapseChange(val) {
      let lang_list = Object.keys(this.langs);
      this.cur_lang = lang_list[0];
    },
    debounce(func, delay = 1000, immediate = false) {
      let timer = null;
      //不能用箭头函数
      return function () {
        //在时间内重复调用的时候需要清空之前的定时任务
        if (timer) {
          clearTimeout(timer);
        }
        //适用于首次需要立刻执行的
        if (immediate && !timer) {
          func.apply(this, arguments);
        }
        timer = setTimeout(() => {
          func.apply(this, arguments);
        }, delay);
      };
    },
    submitBase() {
      let id_ = this.$route.query.id;
      if (this.form.start_at >= this.form.end_at) {
        this.$message.error(`开始时间应该小于结束时间`);
        return;
      }
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          this.$alert("校验失败请修改", "校验失败请修改", {
            confirmButtonText: "确定",
          });
          return false;
        } else {
          if (id_ === "0") {
            this.$axios
              .post(`/api/operation/perpetual-topic/activities`, this.form)
              .then((res) => {
                this.loading = false;
                if (res?.data?.code === 0) {
                  let data = res.data.data;
                  this.$router.replace({ query: { id: data.id } });
                  this.$route.query.id = data.id;
                  this.get_data();
                  this.$message.success("保存成功!");
                } else {
                  this.$message.error(
                    `保存失败! (code: ${res.data?.code}; message: ${res.data?.message})`
                  );
                }
              })
              .catch((err) => {
                this.loading = false;
                this.$message.error(`提交失败! (${err})`);
              });
          } else {
            this.form.id = id_;
            this.$axios
              .put(`/api/operation/perpetual-topic/activities`, this.form)
              .then((res) => {
                this.loading = false;
                if (res?.data?.code === 0) {
                  let data = res.data.data;
                  this.$router.replace({ query: { id: data.id } });
                  this.get_data();
                  this.$message.success("保存成功!");
                } else {
                  this.$message.error(
                    `保存失败! (code: ${res.data?.code}; message: ${res.data?.message})`
                  );
                }
              })
              .catch((err) => {
                this.loading = false;
                this.$message.error(`提交失败! (${err})`);
              });
          }
        }
      });
    },
    cancelBase() {
      this.$confirm(`是否退出当前页面?`).then(() => {
        window.opener = null;
        window.open("about:blank", "_top").close();
      });
    },
    handleJumpPages() {
      this.jump_show = true;
    },
    handleJumpCreate() {
      this.jump_action = this.DIALOG_CREATION;
      this.jump_data = {};
      this.jump_action_show = true;
    },
    handleJumpEdit(row) {
      this.jump_action = this.DIALOG_EDIT;
      this.jump_data = _.clone(row);
      this.jump_action_show = true;
    },
    handleDeleteJump(row) {
      this.$confirm(`确认删除跳转 ${row.id} ${row.remark}?`).then(() => {
        this.$axios
          .delete(`/api/operation/page/new-app-entrances/jump-list/${row.id}`)
          .then((res) => {
            this.fetchJumpPages();
            if (res.data.code === 0) {
              this.dialog_show = false;
              this.jump_show = false;
              this.jump_action_show = false;
              this.jump_action = "";
              this.$message.success("删除成功!");
              this.loading = false;
            } else {
              this.$message.error(
                `code: ${res.data?.code}; message: ${res.data?.message}`
              );
            }
          })
          .catch((err) => {
            this.$message.error(`失败! (${err})`);
          });
      });
    },
    fetchJumpPages() {
      let url = "/api/operation/page/new-app-entrances/jump-list";
      let jump_pages = this.jump_pages;
      this.$axios.get(url).then((res) => {
        if (res?.data?.code === 0) {
          jump_pages = res.data.data;
          jump_pages.forEach((e) => {
            if (e.jump_type === "原生") {
              e.jump_type = "NATIVE";
            }
          });
          this.jump_pages = jump_pages;
          this.getTargetPages(true);
        } else {
          this.$message.error(
            `code: ${res.data?.code}; message: ${res.data?.message}`
          );
        }
      });
    },
    getTargetPages(init = false) {
      let target_pages = [];
      this.jump_pages.forEach((e) => {
        if (e.jump_type === "URL") {
          target_pages.push({
            id: e.id,
            remark: e.remark,
            jump_data: e.jump_data,
          });
        }
      });
      this.target_pages = target_pages;
    },

    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          done();
        })
        .catch((_) => { });
    },
    jump_submit() {
      this.$refs["jump_data"].validate((valid) => {
        if (!valid) {
          this.$alert("校验失败请修改", "校验失败请修改", {
            confirmButtonText: "确定",
          });
        } else {
          let method = "put";
          if (this.jump_action === this.DIALOG_CREATION) {
            method = "post";
          }
          this.$axios[method](
            "/api/operation/page/new-app-entrances/jump-list",
            this.jump_data
          )
            .then((res) => {
              this.fetchJumpPages();
              if (res.data.code === 0) {
                this.res_success_notice(res);
                this.dialog_show = false;
                this.jump_show = false;
                this.jump_action_show = false;
                this.jump_action = "";
              } else {
                this.res_fail_notice(res);
              }
            })
            .catch((_) => {
              this.res_error_notice(res);
            });
        }
      });
    },
    notice(title, message) {
      this.$alert(message, title, {
        confirmButtonText: "确定",
      });
    },
    res_success_notice(r) {
      let title = "提交成功";
      this.notice(title, title);
    },
    res_fail_notice(res) {
      let title = "提交失败";
      let message = `(code: ${res.data?.code}; message: ${res.data?.message})`;
      this.notice(title, message);
    },
    res_error_notice(error) {
      let title = "提交失败";
      let message = `(code: ${error.response.status}; message: ${error.message})`;
      this.notice(title, message);
    },
    format_date(timestamp, pattern = "YYYY-MM-DD HH:mm:ss") {
      return moment(Number(timestamp) * 1000).format(pattern);
    },
    get_data() {
      let id = this.$route.query.id;
      if (id === "0") {
        this.action = this.DIALOG_CREATION;
        return;
      }
      this.action = this.DIALOG_EDIT;
      this.loading = true;
      this.activitiesData = [];
      this.$axios
        .get(`/api/operation/perpetual-topic/activities/${id}`)
        .then((res) => {
          this.loading = false;
          if (res?.data?.code === 0) {
            let data = res.data.data;
            let form = this.form;
            form.name = data.name || "";
            form.remark = data.remark || "";
            form.start_at = data.start_at * 1000 || null;
            form.end_at = data.end_at * 1000 || null;
            form.activity_count = data.activity_count || null;
            this.langs = data.langs || {};
            this.status = data.status || "";
            let activities = data.activities || [];
            this.baseData = data.statistics.table_data;
            let lang_list = Object.keys(this.langs);
            this.cur_lang = lang_list[0];
            for (let i = 0; i < data.activity_count; i++) {
              if (activities[i]) {
                let activity = activities[i];
                let contents = {};
                if (activity.contents[0]) {
                  activity.contents.forEach((item) => {
                    contents[item.lang] = {
                      title: item.title,
                      subtitle: item.subtitle,
                    };
                  });
                }
                let allContents = Object.fromEntries(
                  lang_list.map((lang) => [
                    lang,
                    contents[lang] || {
                      title: "",
                      subtitle: "",
                    },
                  ])
                );
                activity.contents = allContents;
                this.activitiesData.push(activity);
              } else {
                let contents = Object.fromEntries(
                  lang_list.map((lang) => [
                    lang,
                    {
                      title: "",
                      subtitle: "",
                    },
                  ])
                );
                let activityData = JSON.parse(
                  JSON.stringify(this.initActivityData)
                );
                activityData.contents = contents;
                this.activitiesData.push(activityData);
              }
            }
            if (["OFFLINE", "ONLINE"].includes(this.status)) {
              this.activeNames.push("5");
              this.isDisabledEdit = true;
            }
            this.fetchJumpPages();
            this.chartOptions.series = data.statistics.chart_data || [];
          } else {
            this.$message.error(
              `code: ${res.data?.code}; message: ${res.data?.message}`
            );
          }
        });
    },
  },
  mounted() {
    this.get_data();
    this.debounceSubmitBase = this.debounce(this.submitBase, 1000, false);
  },
  data() {
    return {
      jump_action: null,
      jump_show: false,
      jump_action_show: false,
      jump_pages: [],
      jump_types: { URL: "URL" },
      jump_data: {},
      target_pages: {},
      jump_id: null,
      cur_lang: null,
      action: null,
      activeNames: ["1"],
      DIALOG_CREATION: "CREATION",
      DIALOG_EDIT: "EDIT",
      debounceSubmitBase: () => { },
      form: {
        name: "",
        remark: "",
        start_at: null,
        end_at: null,
        activity_count: null,
      },
      statisticsData: {},
      chartOptions: {
        title: {
          text: "30天内活动访问情况",
        },
        xAxis: {
          type: "datetime",
          labels: {
            step: 3,
          },
          dateTimeLabelFormats: {
            day: "%m-%d",
          },
        },
        yAxis: {
          min: 0,
          title: {
            text: "数量",
          },
        },
        tooltip: {
          shared: true,
          xDateFormat: "%Y-%m-%d",
          valueDecimals: 0,
        },
        series: [],
      },
      baseData: [],
      isDisabledEdit: false,
      groupForm: {
        groups: [],
        coupon_apply_id: null,
      },
      status: "",
      loading: false,
      AcitivtyCount: [1, 2, 3],
      dialogApplyFilters: {
        page: 1,
        limit: 10,
      },
      langs: {},
      couponApplyData: [],
      activitiesData: [],
      contents: {},
      initActivityData: {
        contents: {},
        jump_type: "",
        jump_id: null,
        night_file_key: null,
        night_url: "",
        daylight_url: "",
        daylight_file_key: null,
        rtl_night_file_key: null,
        rtl_night_url: "",
        rtl_daylight_url: "",
        rtl_daylight_file_key: null,
        activity_id: null,
      },
      baseRules: {
        name: [
          { required: true, message: "请输入名称", trigger: "blur" },
          { min: 0, max: 200, message: "最大200个字符", trigger: "blur" },
        ],
        start_at: [
          { required: true, message: "请选择活动开始时间", trigger: "blur" },
        ],
        activity_count: [
          { required: true, message: "请选择活动数量", trigger: "blur" },
        ],
        start_at: [
          { required: true, message: "请选择活动开始的时间", trigger: "blur" },
        ],
        end_at: [
          { required: true, message: "请选择活动结束的时间", trigger: "blur" },
        ],
      },
    };
  },
};
</script>


<style>
.img-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.img-uploader .el-upload:hover {
  border-color: #409eff;
}

.img-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.hide .el-upload--picture-card {
  display: none;
}
</style>
