<template>
  <div class="table-data">
    <el-row type="flex" justify="space-between" align="middle">
      <el-col :span="22">
        <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
          {{ "首页插画" }}
        </h2>
      </el-col>

      <el-col :span="1">
        <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
          <el-button icon="el-icon-refresh-left"
                     circle
                     @click="get_data"></el-button>
        </el-tooltip>
      </el-col>
    </el-row>
    <el-collapse v-model="activeNames">
      <el-collapse-item name="0">
        <template slot="title"><h3 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">基本信息</h3></template>
        <el-form :model="filters_form"
                 label-width="100px"
                 v-loading="loading"
                 :disabled="disabled">
          <el-form-item label="名称: " required>
            <el-input v-model="filters_form.display_name" style="width: 300px">
            </el-input>
          </el-form-item>

          <el-form-item label="开始时间:" required>
            <el-date-picker v-model="filters_form.begin_at" type="datetime" clearable></el-date-picker>
            <el-row>
              <span>UTC时间：{{ $formatUTCDate(filters_form.begin_at) }}</span>
            </el-row>
          </el-form-item>

          <el-form-item label="结束时间" required>
            <el-date-picker v-model="filters_form.end_at" type="datetime" clearable></el-date-picker>
            <el-row>
              <span>UTC时间：{{ $formatUTCDate(filters_form.end_at) }}</span>
            </el-row>
          </el-form-item>

          <el-form-item v-if="filters_form.content_style === 'ACTIVITY'" label="跳转URL: " required>
            <el-input v-model="filters_form.url" style="width: 300px">
            </el-input>
            <el-button type="success" @click="save_help_announcement_url">
              填充帮助中心/公告中心多语言链接
            </el-button>
          </el-form-item>

          <el-form-item label="配置样式:" required>
            <el-radio-group :disabled="$route.query.id !== '0'" v-model="filters_form.content_style">
              <template v-for="(name, key) in content_styles">
                <el-radio :label="key">{{ name }}</el-radio>
              </template>
            </el-radio-group>
          </el-form-item>
          <el-divider></el-divider>
          <el-form-item v-if=!disabled>
            <el-button type="primary" @click="save_form()">
              保存
            </el-button>
          </el-form-item>
        </el-form>
      </el-collapse-item>
      <template v-if="$route.query.id !== '0' && contents_visible">
        <el-collapse-item name="1">
          <template slot="title">
            <h3>文案内容</h3>&#12288;
          </template>
          <el-form>
            <el-form-item required>
              <el-row>
                <el-col :span="2">
                  <el-upload
                    action
                    :key="Date.now()"
                    ref="upload"
                    name="batch-upload"
                    :auto-upload="false"
                    :show-file-list="false"
                    :on-change="handle_update_template"
                    accept=".xlsx"
                  >
                    <el-button type="primary" @click="$refs.upload.submit()">上传</el-button>
                  </el-upload>
                </el-col>
                <el-col :span="1">
                  <el-button type="primary" @click="dowloadTemplate">下载模板</el-button>
                </el-col>
              </el-row>
              <el-tabs v-model="cur_lang" type="card">
                <template v-for="(name, lang) in languages">
                  <el-tab-pane :label="name" :name="lang">
                    <el-form :model="filters_form"
                             label-width="100px">
                  <span v-if="filters_form.content_style === 'ACTIVITY'">
                    <el-form-item label="主标题">
                    <el-input :maxlength="60"
                              placeholder="请输入标题（最多60个字符）"
                              show-word-limit
                              v-model="contents[lang].title"></el-input>
                  </el-form-item>
                    <el-form-item label="副标题">
                    <el-input :maxlength="100"
                              placeholder="请输入标题（最多100个字符）"
                              show-word-limit
                              v-model="contents[lang].sub_title"></el-input>
                  </el-form-item>
                  <el-form-item label="按钮文案">
                    <el-input :maxlength="30"
                              placeholder="请输入文案（最多30个字符）"
                              show-word-limit
                              v-model="contents[lang].button_text"></el-input>
                  </el-form-item>
                  <el-form-item label="跳转URL">
                    <el-input v-model="contents[lang].url"></el-input>
                  </el-form-item>
                  </span>
                      <el-form-item prop="light_pic_id" label="图片(日间模式)" label-width="120px">
                        <el-upload
                          class="avatar-uploader"
                          ref="upload-icon"
                          :show-file-list="false"
                          action="/api/upload/image"
                          accept=".jpg, .jpeg, .png, .webp, .mp4, .json"
                          :before-upload="beforeUpload"
                          :headers="{'AUTHORIZATION': $cookies.get('admin_token')}"
                          name="img"
                          :data="{ auto_zip: 'WEBP' }"
                          :on-success="res => {contents[lang].light_pic_url = res.data.file_url; contents[lang].light_pic_id = res.data.file_id}">
                          <img v-if="checkFileType(contents[lang].light_pic_url, FILE_TYPE_IMAGE)" :src="contents[lang].light_pic_url" class="avatar" alt="">
                          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                        </el-upload>
                        <el-button v-if="checkFileType(contents[lang].light_pic_url, FILE_TYPE_LOTTIE)" icon="el-icon-zoom-in" circle @click="previewLottie(contents[lang].light_pic_url)"></el-button>
                        <el-button v-if="checkFileType(contents[lang].light_pic_url, FILE_TYPE_MP4)" icon="el-icon-zoom-in" circle @click="previewMP4(contents[lang].light_pic_url)"></el-button>
                        <el-button v-if="contents[lang].light_pic_url" type="danger" @click="delete_light_img(lang)">
                          删除图片(日间模式)
                        </el-button>
                      </el-form-item>
                      <el-form-item prop="dark_pic_id" label="图片(夜间模式)" label-width="120px">
                        <el-upload
                          class="avatar-uploader"
                          ref="upload-icon"
                          :show-file-list="false"
                          action="/api/upload/image"
                          accept=".jpg, .jpeg, .png, .webp, .mp4, .json"
                          :before-upload="beforeUpload"
                          :headers="{'AUTHORIZATION': $cookies.get('admin_token')}"
                          name="img"
                          :data="{ auto_zip: 'WEBP' }"
                          :on-success="res => {contents[lang].dark_pic_url = res.data.file_url; contents[lang].dark_pic_id = res.data.file_id}">
                          <img v-if="checkFileType(contents[lang].dark_pic_url, FILE_TYPE_IMAGE)" :src="contents[lang].dark_pic_url" class="avatar" alt="">
                          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                        </el-upload>
                        <el-button v-if="checkFileType(contents[lang].dark_pic_url, FILE_TYPE_LOTTIE)" icon="el-icon-zoom-in" circle @click="previewLottie(contents[lang].dark_pic_url)"></el-button>
                        <el-button v-if="checkFileType(contents[lang].dark_pic_url, FILE_TYPE_MP4)" icon="el-icon-zoom-in" circle @click="previewMP4(contents[lang].dark_pic_url)"></el-button>
                        <el-button v-if="contents[lang].dark_pic_url" type="danger" @click="delete_dark_img(lang)">
                          删除图片(夜间模式)
                        </el-button>
                      </el-form-item>
                      <el-form-item><p>支持上传jpg、jpeg、png、webp、mp4、json(lottie)格式文件</p></el-form-item>
                      <el-form-item>
                        <el-select v-model="same_as_lang"
                                   filterable
                                   style="width: 50%"
                                   value=""
                                   placeholder="使用其他语言的图片&背景色&主副标题颜色"
                                   @change="copy_img_from_other_lang(lang)">
                          <el-option
                            v-for="(name, lang) in languages"
                            :key="lang" :label="name" :value="lang"
                          ></el-option>
                        </el-select>
                      </el-form-item>
                      <el-form-item label="背景色(日间模式)" label-width="130px">
                        <div style="display: flex; align-items: center;">
                          <el-tooltip
                            content="色值格式：#及6位字符(数字/字母)，例如：#12345A"
                            placement="top"
                            :open-delay="500"
                            :hide-after="2000"
                            class="tooltip"
                          >
                            <el-icon name="info" style="margin-right: 15px; cursor: pointer;"></el-icon>
                          </el-tooltip>
                          <el-color-picker
                            v-model="contents[lang].light_background_color"
                            color-format="hex"></el-color-picker>
                        </div>
                      </el-form-item>
                      <el-form-item label="背景色(夜间模式)" label-width="130px">
                        <div style="display: flex; align-items: center;">
                          <el-tooltip
                            content="色值格式：#及6位字符(数字/字母)，例如：#12345A"
                            placement="top"
                            :open-delay="500"
                            :hide-after="2000"
                            class="tooltip"
                          >
                            <el-icon name="info" style="margin-right: 15px; cursor: pointer;"></el-icon>
                          </el-tooltip>
                          <el-color-picker v-model="contents[lang].dark_background_color"></el-color-picker>
                        </div>
                      </el-form-item>
                      <div v-if="filters_form.content_style === 'ACTIVITY'">
                        <el-form-item label="主标题(日间模式)" label-width="130px">
                          <div style="display: flex; align-items: center;">
                            <el-tooltip
                              content="RGBA格式：例如：rgba(247, 253, 251, 1)"
                              placement="top"
                              :open-delay="500"
                              :hide-after="2000"
                              class="tooltip"
                            >
                              <el-icon name="info" style="margin-right: 15px; cursor: pointer;"></el-icon>
                            </el-tooltip>
                            <el-color-picker
                              v-model="contents[lang].light_title_color"
                              color-format="rgb" :show-alpha=true></el-color-picker>
                          </div>
                        </el-form-item>
                        <el-form-item label="副标题(日间模式)" label-width="130px">
                          <div style="display: flex; align-items: center;">
                            <el-tooltip
                              content="RGBA格式：例如：rgba(247, 253, 251, 1)"
                              placement="top"
                              :open-delay="500"
                              :hide-after="2000"
                              class="tooltip"
                            >
                              <el-icon name="info" style="margin-right: 15px; cursor: pointer;"></el-icon>
                            </el-tooltip>
                            <el-color-picker
                              v-model="contents[lang].light_subtitle_color"
                              color-format="rgb" :show-alpha=true></el-color-picker>
                          </div>
                        </el-form-item>
                        <el-form-item label="主标题(夜间模式)" label-width="130px">
                          <div style="display: flex; align-items: center;">
                            <el-tooltip
                              content="RGBA格式：例如：rgba(247, 253, 251, 1)"
                              placement="top"
                              :open-delay="500"
                              :hide-after="2000"
                              class="tooltip"
                            >
                              <el-icon name="info" style="margin-right: 15px; cursor: pointer;"></el-icon>
                            </el-tooltip>
                            <el-color-picker
                              v-model="contents[lang].dark_title_color"
                              color-format="rgb" :show-alpha=true></el-color-picker>
                          </div>
                        </el-form-item>
                        <el-form-item label="副标题(夜间模式)" label-width="130px">
                          <div style="display: flex; align-items: center;">
                            <el-tooltip
                              content="RGBA格式：例如：rgba(247, 253, 251, 1)"
                              placement="top"
                              :open-delay="500"
                              :hide-after="2000"
                              class="tooltip"
                            >
                              <el-icon name="info" style="margin-right: 15px; cursor: pointer;"></el-icon>
                            </el-tooltip>
                            <el-color-picker
                              v-model="contents[lang].dark_subtitle_color"
                              color-format="rgb" :show-alpha=true></el-color-picker>
                          </div>
                        </el-form-item>
                      </div>
                    </el-form>
                    <el-divider></el-divider>
                    <div>
                      <el-button type="primary" @click="save_contents('')">
                        保存
                      </el-button>

                      <!--                      <el-button type="primary" @click="save_contents('')" :disabled="disabled">-->
                      <!--                        保存所有语言-->
                      <!--                      </el-button>-->
                    </div>
                  </el-tab-pane>
                </template>
              </el-tabs>
            </el-form-item>
          </el-form>
        </el-collapse-item>

        <el-dialog title="预览" :visible.sync="source_lottie_preview" :before-close="handlePreviewClose" width="50%" v-if="preview_url != null">
          <lottie-vue-player :src="preview_url"
                             :theme="options.theme"
                             :player-size="options.playerSize"
                             :player-controls="true"
                             :showColorPicker="true"
                             style="width: 100%; height:100%">
          </lottie-vue-player>
        </el-dialog>

        <el-dialog title="预览" :visible.sync="source_mp4_preview" :before-close="handlePreviewClose" width="50%" v-if="preview_url != null">
          <video controls width="50%" preload="metadata">
            <source :src="preview_url" type="video/mp4">
            您的浏览器不支持 Video 标签。
          </video>
        </el-dialog>
      </template>
    </el-collapse>

  </div>
</template>

<script>
import XLSX from "xlsx";
import {readFile} from "@/plugins/tools";
const Color = require('color');

export default {
  components: {},
  methods: {
    get_data() {
      let id = this.$route.query.id;
      if (id === '0') {
        this.activeNames = ['0', '1',];
      }
      this.loading = true;
      this.$axios.get(`/api/operation/inset/new/${id}`).then(res => {
        this.loading = false;
        if (res?.data?.code === 0) {
          let data = res.data.data;
          this.assign_form(data);
          this.get_contents();
        } else {
          this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
        }
      });
    },
    assign_form(data) {
      let form = this.filters_form;
      form.display_name = data.display_name;
      form.url = data.url;
      form.begin_at = data.begin_at ? new Date(data.begin_at * 1000) : null;
      form.end_at = data.end_at ? new Date(data.end_at * 1000) : null;
      form.content_style = data.content_style;
      this.disabled = ["offline", "online"].includes(data.activate_status);

      let extra = data.extra;
      this.content_styles = extra.content_styles;
      this.languages = extra.languages;
      let lang_list = Object.keys(this.languages);
      if (!lang_list.includes(this.cur_lang)) {
        this.cur_lang = lang_list[0];
      }
      this.contents = Object.fromEntries(lang_list.map(lang => [lang, this.contents[lang] || {
        title: '',
        sub_title: '',
        button_text: '',
        url: '',
        light_pic_id: '',
        light_pic_url: '',
        dark_pic_id: '',
        dark_pic_url: '',
        light_background_color: '',
        dark_background_color: '',
        light_title_color: '',
        dark_title_color: '',
        light_subtitle_color: '',
        dark_subtitle_color: '',
      }]));
    },
    get_contents(id = 0) {
      if (!id) {
        id = this.$route.query.id;
      }
      if (id === '0') {
        return;
      }
      let contents = this.contents;
      let lang_list = Object.keys(this.languages);
      lang_list.forEach(lang => {
        this.$axios.get(`/api/operation/inset/new/${id}/langs/${lang}`).then(res => {
          if (res?.data?.code === 0) {
            let data = res.data.data;
            contents[lang] = {
              title: data.title,
              sub_title: data.sub_title,
              button_text: data.button_text,
              url: data.url,
              light_pic_id: data.light_pic_id,
              light_pic_url: data.light_pic_url,
              dark_pic_id: data.dark_pic_id,
              dark_pic_url: data.dark_pic_url,
              light_background_color: data.light_background_color || this.default_light_background_color,
              dark_background_color: data.dark_background_color || this.default_dark_background_color,
              light_title_color: data.light_title_color || this.default_light_title_color,
              dark_title_color: data.dark_title_color || this.default_dark_title_color,
              light_subtitle_color: data.light_subtitle_color || this.default_light_subtitle_color,
              dark_subtitle_color: data.dark_subtitle_color || this.default_dark_subtitle_color,
            };
            this.contents_visible = lang_list.every(lang => contents[lang]);
          }
        })
      });
    },
    getTargetPages() {
      // debugger;
      let target_pages = [];
      this.target_pages = target_pages;
    },
    notice(title, message) {
      this.$alert(message, title, {
        confirmButtonText: '确定',
      });
    },
    res_success_notice(r) {
      let title = "提交成功";
      this.notice(title, title);
    },
    res_fail_notice(res) {
      let title = "提交失败";
      let message = `(code: ${res.data?.code}; message: ${res.data?.message})`;
      this.notice(title, message);
    },
    res_error_notice(error) {
      let title = "提交失败";
      let message = `(code: ${error.response.status}; message: ${error.message})`;
      this.notice(title, message);
    },
    save_help_announcement_url() {
      let url = this.filters_form.url;
      let is_help_url = url.indexOf("https://support.coinex.com/") == 0;
      let is_ann_url = url.indexOf("https://announcement.coinex.com/") == 0;
      if (!is_help_url && !is_ann_url) {
        this.$message.error(`链接不是帮助中心/公告中心域名`);
        return
      }

      let help_lang_map = {
        // 后端lang: 帮助中心url lang path
        AR_AE: "ar", // 阿拉伯语
        EN_US: "en-us", // 英语
        ES_ES: "es",  // 西班牙语
        FA_IR: "fa",  // 波斯语
        RU_KZ: "ru",  // 俄语
        ZH_HANS_CN: "zh-cn",  // 简体中文
      };
      // https://support.coinex.com/hc/zh-cn/articles/360025067274
      // https://support.coinex.com/hc/articles/360025067274
      if (is_help_url) {
        let pattern = ""
        if (url.indexOf("hc/articles") != -1) {
          pattern = /hc\/articles/;
        } else {
          pattern = /hc\/(.*)\/articles/;
        }
        let lang_list = Object.keys(help_lang_map);
        lang_list.forEach(lang => {
          let url_lang = help_lang_map[lang];
          let new_url = url.replace(pattern, `hc/${url_lang}/articles`);
          let contents = this.contents;
          if (contents[lang]) {
            contents[lang].url = new_url
          }
        });
        return
      }

      let ann_lang_map = {
        // 后端lang: 公告中心url lang path
        AR_AE: "ar", // 阿拉伯语
        DE_DE: "de",  // 德语
        EN_US: "en-us", // 英语
        ES_ES: "es",  // 西班牙语
        FA_IR: "fa",  // 波斯语
        FR_FR: "fr",  // 法语
        ID_ID: "id-id",  // 印尼语
        JA_JP: "ja",  // 日语
        KO_KP: "ko-kr",  // 韩语
        PT_PT: "pt",  // 葡萄牙语
        RU_KZ: "ru",  // 俄语
        TH_TH: "th",  // 泰语
        TR_TR: "tr",  // 土耳其语
        VI_VN: "vi-vn",  // 越南语
        ZH_HANS_CN: "zh-cn",  // 简体中文
        ZH_HANT_HK: "zh-tw", // 繁体中文
      };
      // https://announcement.coinex.com/hc/en-us/articles/8512448633108
      if (is_ann_url) {
        let pattern = ""
        if (url.indexOf("hc/articles") != -1) {
          pattern = /hc\/articles/;
        } else {
          pattern = /hc\/(.*)\/articles/;
        }
        let lang_list = Object.keys(ann_lang_map);
        lang_list.forEach(lang => {
          let url_lang = ann_lang_map[lang];
          let new_url = url.replace(pattern, `hc/${url_lang}/articles`);
          let contents = this.contents;
          if (contents[lang]) {
            contents[lang].url = new_url
          }
        });
        return
      }

    },
    save_form(status) {
      let form = this.filters_form;
      form.status = status;
      if (!form.display_name) {
        this.$message.error('请输入名称!');
        return;
      }
      // 活动才有URL
      if (form.content_style === 'ACTIVITY' && !form.url) {
        this.$message.error('请输入跳转URL!');
        return;
      }
      if (!form.begin_at) {
        this.$message.error('请选开始时间!');
        return;
      }
      if (!form.end_at) {
        this.$message.error('请选结束时间!');
        return;
      }
      if (form.end_at <= form.begin_at) {
        this.$message.error('开始时间不能小于结束时间');
        return;
      }
      if (!form.content_style) {
        this.$message.error('请选择配置样式!');
        return;
      }
      // debugger;
      let id = this.$route.query.id;
      if (id === '0') {
        this.loading = true;
        this.$axios.put(`/api/operation/inset/new/0`, this.get_form_data(form)).then(res => {
          this.loading = false;
          if (res?.data?.code === 0) {
            let data = res.data.data;
            let id = data.id;
            this.$router.replace({query: {id: id}});
            this.$message.success("保存成功!");
            this.get_contents(id);
          } else {
            this.$message.error(`保存失败! (code: ${res.data?.code}; message: ${res.data?.message})`);
          }
        }).catch(err => {
          this.loading = false;
          this.$message.error(`提交失败! (${err})`);
        });
      } else {
        if (!this.curStatusPermitted(true)) {
          this.$message.error('当前状态不允许修改!');
          return;
        }
        this.loading = true;
        this.$axios.put(`/api/operation/inset/new/${id}`, this.get_form_data(form)).then(res => {
          this.loading = false;
          if (res?.data?.code === 0) {
            let data = res.data.data;
            this.cur_status = data.status;
            this.$message.success("保存成功!");
          } else {
            this.$message.error(`保存失败! (code: ${res.data?.code}; message: ${res.data?.message})`);
          }
        }).catch(err => {
          this.loading = false;
          this.$message.error(`保存失败! (${err})`);
        });
      }
      this.afterSaveDisplay();
    },
    get_form_data(form) {
      return {
        display_name: form.display_name,
        url: form.content_style === 'ACTIVITY' ? form.url : '',
        begin_at: form.begin_at,
        end_at: form.end_at,
        content_style: form.content_style,
        light_pic_id: form.light_pic_id,
        dark_pic_id: form.dark_pic_id,
      }
    },
    copy_img_from_other_lang(cur_lang) {
      let other_lang_content = this.contents[this.same_as_lang];
      let cur_lang_content = this.contents[cur_lang];
      cur_lang_content.light_pic_id = other_lang_content.light_pic_id
      cur_lang_content.light_pic_url = other_lang_content.light_pic_url
      cur_lang_content.dark_pic_id = other_lang_content.dark_pic_id
      cur_lang_content.dark_pic_url = other_lang_content.dark_pic_url
      cur_lang_content.light_background_color = other_lang_content.light_background_color
      cur_lang_content.dark_background_color = other_lang_content.dark_background_color
      cur_lang_content.light_title_color = other_lang_content.light_title_color
      cur_lang_content.dark_title_color = other_lang_content.dark_title_color
      cur_lang_content.light_subtitle_color = other_lang_content.light_subtitle_color
      cur_lang_content.dark_subtitle_color = other_lang_content.dark_subtitle_color
      this.same_as_lang = ''
    },
    delete_light_img(lang) {
      this.contents[lang].light_pic_id = null
      this.contents[lang].light_pic_url = null
    },
    delete_dark_img(lang) {
      this.contents[lang].dark_pic_id = null
      this.contents[lang].dark_pic_url = null
    },
    checkSize(w, h, that) {
      const _isSize = (w === 1880 && h === 642) || (w === 3760 && h === 1284);
      if (!_isSize) {
        that.$message.error("图片格式不正确，请上传1880*642或3760*1284尺寸图片!");
      }
      return _isSize
    },
    beforeUpload(file) {
      const isJPG = (file.type === "image/jpg" || file.type === "image/jpeg" || file.type === "image/png");
      const isWebp = file.type === "image/webp";
      const isMP4 = file.type === "video/mp4";
      const isLottie = file.type === "application/json";
      const isAllowedImage = isJPG || isWebp || isMP4 || isLottie;
      if (!isAllowedImage) {
        this.$message.error("只能是 JPG、PNG、Webp、MP4、JSON(lottie) 格式!");
      }
      const isAvailableLength = file.size / 1024 / 1024 < 5;
      if (!isAvailableLength) {
        this.$message.error('上传文件大小不能超过 5MB!');
      }
      let that = this;
      const isSize = new Promise(function (resolve, reject) {
        const _URL = window.URL || window.webkitURL;
        const src = _URL.createObjectURL(file);
        if (isJPG || isWebp) {
          const image = new Image();
          image.onload = function () {
            that.checkSize(image.width, image.height, that) ? resolve() : reject();
          };
          image.src = src;
        } else if (isMP4) {
          const videoElem = document.createElement('video');
          videoElem.onloadedmetadata = function () {
            that.checkSize(videoElem.videoWidth, videoElem.videoHeight, that) ? resolve() : reject();
          };
          videoElem.setAttribute('preload', 'auto');
          videoElem.src = src;
        } else if (isLottie) {
          const fr = new FileReader();
          fr.onload = function (e) {
            const data = JSON.parse(e.target.result);
            if (!data.hasOwnProperty('w') || !data.hasOwnProperty('h')) {
              that.$message.error('JSON文件格式异常!');
              reject();
            }
            that.checkSize(data.w, data.h, that) ? resolve() : reject();
          };
          fr.readAsText(file);
        }
      }).then(
        () => {
          return file;
        },
        () => {
          return Promise.reject();
        }
      );
      if (!isSize) {
        this.$message.error("图片格式不正确，请上传1880*642或3760*1284尺寸图片!");
      }
      return isAllowedImage && isAvailableLength && isSize;
    },
    save_contents(lang = '', silent = false) {
      let contents = this.contents;
      let id = this.$route.query.id;
      let languages = lang ? [lang] : Object.keys(this.languages);
      let putLanguages = [];

      if (!lang) {
        languages.forEach(lang => {
          let content = contents[lang];
          // 确保内容有填充
          // if (content.title || content.sub_title || content.url || content.light_pic_id || content.dark_pic_id) {
          //   putLanguages.push(lang);
          // }
          putLanguages.push(lang)
        });

        if (putLanguages.length === 0) {
          this.$message.error('内容不能为空，请至少在一个语言区输入内容!');
          return;
        }
      } else {
        putLanguages = languages;
        let content = contents[lang];
        // 校验逻辑
      }

      for (let i = 0; i < putLanguages.length; i++) {
        let lang = putLanguages[i];
        let content = contents[lang];

        if (this.filters_form.content_style === 'ACTIVITY') {
          const {title, sub_title, light_pic_id, dark_pic_id} = content;
          const allFieldsPresent = title && sub_title && light_pic_id && dark_pic_id;
          const allFieldsAbsent = !title && !sub_title && !light_pic_id && !dark_pic_id;
          console.log(lang, allFieldsPresent, allFieldsAbsent);
          if (!(allFieldsPresent || allFieldsAbsent)) {
            setTimeout(() => {
              this.$message.error(`(${lang})当内容样式为 活动 时，主标题、副标题、图片必须同时存在!`);
            }, 10);
            continue;
          }
        }

        if (this.filters_form.content_style === 'IMAGE') {
          const {light_pic_id, dark_pic_id} = content;
          const allFieldsPresent = light_pic_id && dark_pic_id;
          const allFieldsAbsent = !light_pic_id && !dark_pic_id;
          if (!(allFieldsPresent || allFieldsAbsent)) {
            setTimeout(() => {
              this.$message.error(`语言(${lang})当内容样式为 情感化 时，图片必须同时存在!`);
            }, 10);
            continue;
          }
        }
        const color_fields = [
          "light_background_color",
          "dark_background_color",
          "light_title_color",
          "dark_title_color",
          "light_subtitle_color",
          "dark_subtitle_color",
        ];
        for (let i = 0; i < color_fields.length; i++) {
          let field = color_fields[i];
          if (field in content && content[field]) {
            try {
              Color(content[field].toLowerCase()).toString();
            } catch (error) {
              console.error('验证失败:', error.message);
              setTimeout(() => {
                this.$message.error(`语言(${lang}) ${field} 色值格式有问题请检查!`);
              }, 10);
              return
            }
          }
        }
        this.$axios.put(`/api/operation/inset/new/${id}/langs/${lang}`, {
          title: content.title,
          sub_title: content.sub_title,
          button_text: content.button_text,
          url: content.url,
          light_pic_id: content.light_pic_id,
          dark_pic_id: content.dark_pic_id,
          light_background_color: content.light_background_color,
          dark_background_color: content.dark_background_color,
          light_title_color: content.light_title_color,
          dark_title_color: content.dark_title_color,
          light_subtitle_color: content.light_subtitle_color,
          dark_subtitle_color: content.dark_subtitle_color,
        }).then(res => {
          if (res?.data?.code === 0) {
            if (!silent) {
              this.$message.success(`${lang} 保存成功!`);
            }
          } else {
            this.$message.error(`保存失败! (code: ${res.data?.code}; message: ${res.data?.message})`);
          }
        }).catch(err => {
          this.$message.error(`保存失败! (${err})`);
        });
      }

      this.afterSaveDisplay();
    },
    curStatusPermitted(fromRuleSave = false) {
      return !this.disabled
    },
    afterSaveDisplay() {
      this.activeNames = ['0', '1',];
    },
    before_upload(file) {
      let name = file.name;
      if (
        !name.endsWith(".xlsx") &&
        !name.endsWith(".xls") &&
        !name.endsWith("csv")
      ) {
        this.$message.error("只能上传excel表格");
        return false;
      }
    },
    upload_success(response) {
      if (response.code !== 0) {
        this.$alert(response.message, "错误")
          .then(() => {
          })
          .catch(() => {
          });
        return;
      }
      this.$message.success("已成功上传，请检查修后的翻译");

      let trans_data = {};
      response.data.forEach((e) => {
        trans_data[e.lang.toLowerCase()] = {
          'title': e.title,
          'subtitle': e.subtitle,
        };
      });
      Object.keys(this.contents).forEach(lang => {
        let trans = trans_data[lang.toLowerCase()]
        this.contents[lang].title = trans.title || this.contents[lang].title;
        this.contents[lang].sub_title = trans.subtitle || this.contents[lang].sub_title;
        this.contents[lang].button_text = trans.button_text || this.contents[lang].button_text;
      });
    },
    upload_error(err) {
      this.$message.error(`上传失败! (${err})`);
    },
    download_template() {
      let url = "/api/operation/title-subtitle/template";
      this.$download_from_url(url, "title-template.xlsx");
    },
    checkFileType(url, file_type) {
      if (url === '' || url === null || url === undefined) {
        return false;
      }
      const ext = url.substring(url.lastIndexOf('.') + 1);
      if (ext === 'json') {
        return this.FILE_TYPE_LOTTIE === file_type;
      } else if (ext === 'mp4') {
        return this.FILE_TYPE_MP4 === file_type;
      } else {
        return this.FILE_TYPE_IMAGE === file_type;
      }
    },
    handlePreviewClose() {
      this.preview_url = null;
      this.source_lottie_preview = false;
      this.source_mp4_preview = false;
    },
    previewLottie(url) {
      this.preview_url = url;
      this.source_lottie_preview = true;
      this.source_mp4_preview = false;
    },
    previewMP4(url) {
      this.preview_url = url;
      this.source_lottie_preview = false;
      this.source_mp4_preview = true;
    },
    async write_data_to_from(data_arr) {
      let langMapper = {}
      data_arr.forEach((item) => {
        let lang = item.lang.toUpperCase();
        langMapper[lang] = {
          title: item.title.trim(),
          sub_title: item.sub_title.trim(),
          button_text: item.button_text.trim()
        }
      });
      Object.keys(this.contents).forEach((lang) => {
        var fileInfo = langMapper[lang]
        if (!fileInfo) return;
        this.$set(this.contents[lang], 'title', fileInfo.title);
        this.$set(this.contents[lang], 'sub_title', fileInfo.sub_title);
      })

      this.$message.success("上传成功");
    },

    async handle_update_template(file) {
      let dataBinary = await readFile(file.raw);
      let workBook = XLSX.read(dataBinary, {type: "binary", cellDates: true});
      let workSheet = workBook.Sheets[workBook.SheetNames[0]];
      let json_data = XLSX.utils.sheet_to_json(workSheet);
      let data_arr = await this.fmt_read_excel_data(json_data);
      await this.write_data_to_from(data_arr);
    },

    async fmt_read_excel_data(json_data) {
      let character = {
        lang: "lang"
      };
      character['title'] = "主标题(必填)"
      character['sub_title'] = "副标题(必填)"
      character['button_text'] = "按钮文案(非必填)"
      let arr = [];
      json_data.forEach((item) => {
        let obj = {};
        for (let key in character) {
          if (!character.hasOwnProperty(key)) break;
          let v = item[character[key]];
          if (!v) {
            obj[key] = "";
            continue
          }
          obj[key] = v.replace(/\r?\n/g, "<br />");
        }
        arr.push(obj);
      });
      return arr;
    },

    dowloadTemplate() {
      let arr = [];
      Object.keys(this.languages).forEach(lang => {
        let arr_item = {
          语言: this.languages[lang],
          lang: lang.toLocaleLowerCase(),
        };
        arr_item['主标题(必填)'] = ""
        arr_item['副标题(必填)'] = ""
        arr_item['按钮文案(非必填)'] = ""
        arr.push(arr_item);
      })
      // 将json数据变为sheet数据
      let sheet = XLSX.utils.json_to_sheet(arr);
      // 新建表格
      let book = XLSX.utils.book_new();
      // 在表格中插入一个sheet
      XLSX.utils.book_append_sheet(book, sheet, "sheet1");
      // 通过xlsx的writeFile方法将文件写入
      XLSX.writeFile(book, `inset_template.xlsx`);
    },
  },
  mounted() {
    this.get_data();
  },
  data() {
    return {
      disabled: false,
      filters_form: {
        display_name: '',
        url: '',
        content_style: null,
        begin_at: null,
        end_at: null,
        dark_pic_id: null,
        light_pic_id: null,
        dark_pic_url: null,
        light_pic_url: null,
      },
      default_light_background_color: '#F7FDFB',
      default_dark_background_color: '#03080C',
      default_light_title_color: 'rgba(13, 14, 16, 0.95)',
      default_dark_title_color: 'rgba(255, 255, 255, 0.95)',
      default_light_subtitle_color: 'rgba(13, 14, 16, 0.5)',
      default_dark_subtitle_color: 'rgba(255, 255, 255, 0.5)',
      languages: {},
      cur_lang: null,
      cur_status: null,
      contents: {},
      content_styles: {},
      contents_visible: false,
      activeNames: ['0', '1'],
      loading: false,
      same_as_lang: '',
      DIALOG_CREATION: 'CREATION',
      DIALOG_EDIT: 'EDIT',
      FILE_TYPE_MP4: 'mp4',
      FILE_TYPE_LOTTIE: 'json',
      FILE_TYPE_IMAGE: 'image',
      source_lottie_preview: false,
      source_mp4_preview: false,
      preview_url: null,
      options: {
        minimizable: false,
        playerSize: "standard",
        backgroundColor: '#fff',
        backgroundStyle: 'color',
        theme: {
          controlsView: "standard",
          active: "light",
          light: {
            color: '#3D4852',
            backgroundColor: '#fff',
            opacity: '0.7',
          },
          dark: {
            color: '#fff',
            backgroundColor: '#202020',
            opacity: '0.7',
          }
        }
      }
    }
  }
}

</script>
