<template>
  <div class="table-data">
    <el-row type="flex" justify="space-between" align="middle">
      <el-col :span="22">
        <h2 style="font-family: 'Microsoft YaHei', Arial, sans-serif">
          交割规则设置
        </h2>
      </el-col>

      <el-col :span="1">
        <el-tooltip
          content="刷新"
          placement="right"
          :open-delay="500"
          :hide-after="2000"
        >
          <el-button
            icon="el-icon-refresh-left"
            circle
            @click="get_data"
          ></el-button>
        </el-tooltip>
      </el-col>
    </el-row>
    <el-collapse v-model="activeNames">
      <template v-if="$route.query.id !== '0' && contents_visible">
        <el-collapse-item name="0">
          <template slot="title">
            <h3 style="font-family: 'Microsoft YaHei', Arial, sans-serif">
              交割规则
            </h3>
            &#12288;
            <el-tooltip placement="right" :open-delay="500">
              <div slot="content">
                <p>
                  交割规则
                </p>
              </div>
              <i class="el-icon-question"></i>
            </el-tooltip>
          </template>
          <MessageEditor
            :messageConfig="messageConfig"
            :contents="contents"
            :languages="languages"
            :template_filters="template_filters"
          ></MessageEditor>
        </el-collapse-item>
      </template>
    </el-collapse>
  </div>
</template>

<script>
import Vue from "vue";
import VueClipboard from "vue-clipboard2";
import moment from "moment";
import MessageEditor from "@/components/MessageEditor";

Vue.use(VueClipboard);

export default {
  components: {
    MessageEditor,
  },
  methods: {
    get_data(_id = 0) {
      let id = this.$route.query.id || _id || 0;
      try {
        this.$axios.get(`/api/pre-trading/asset-config/${id}`).then((res) => {
          if (res?.data?.code === 0) {
            this.mode = id ? "edit" : "new";
            let data = res.data.data;
            this.assign_form(data);
          } else {
            this.$message.error(
              `请求失败! (code: ${res.data?.code}; message: ${res.data?.message})`
            );
          }
        });
      } catch (e) {
        this.$message.error(`请求失败! (${e})`);
      }
    },

    buildContent(content) {
      return content;
    },

    assign_form(data) {
      this.langs = data.extra.langs;
      this.types = data.extra.types;
      this.platforms = data.extra.platforms;
      this.trigger_pages = data.extra.trigger_pages;
      this.filter_types = data.extra.filter_types;
      this.languages = data.extra.langs;
      this.param_types = data.extra.param_types;
      this.markets = data.extra.markets;
      this.margin_markets = data.extra.margin_markets;
      this.assets = data.extra.assets;
      this.margin_assets = data.extra.margin_assets;
      this.perpetual_markets = data.extra.perpetual_markets;
      this.perpetual_assets = data.extra.perpetual_assets;
      this.series_data = data.extra.series_data;

      if (this.mode === "edit") {
        this.record = data.record;
        this.handleHistoryOnEdit(this.record);
        this.contents = data.contents;
        this.contents_visible = true;
        this.timerange = [
          new Date(this.record.started_at * 1000),
          new Date(this.record.ended_at * 1000),
        ];
        let status = data.record.status;
        let has_content = data.record.has_content;
        this.activeNames = this.build_activeNames(status, has_content);
        // 如果以上架且有内容，就冻结所有选项
        this.disabled =
          (status === "AUDITED" &&
            Number(this.record.started_at * 1000) <= Date.now()) ||
          status === "FINISHED";
      } else {
        this.record = {
          platform: "ALL",
          type: "OTHER",
          trigger_pages: [
            {
              trigger_page: "SPOT_MARKET",
              param_type: "MARKET",
              page_op: "IN",
              trigger_page_params: [],
            },
          ],
          filter_type: "filters",
          user_whitelist: "",
        };
        this.contents = {};
      }

      let lang_list = Object.keys(this.languages);
      if (!lang_list.includes(this.cur_lang)) {
        this.messageConfig.cur_lang = lang_list[0];
      }
      this.contents = Object.fromEntries(
        lang_list.map((lang) => [
          lang,
          this.buildContent(this.contents[lang]) || {
            content: "",
            title: "",
          },
        ])
      );
    },
    format_date(timestamp, pattern = "YYYY-MM-DD HH:mm:ss") {
      return moment(Number(timestamp) * 1000).format(pattern);
    },
    handleHistoryOnEdit(row) {
      let timePoint = new Date(2022, 11, 8).getTime();
      this.isOldHistory = row.created_at * 1000 <= timePoint;
      if (!this.isOldHistory) {
        return;
      }
      this.disabled = true;
    },
    save() {
      let canPostReq = this.beforeSaveRule();
      if (!canPostReq) {
        return;
      }
      let groups = [];
      this.tagGroups.forEach((group) => {
        groups.push(group.id);
      });
      this.record.groups = groups;
      console.log(this.record.trigger_pages);
      this.record.trigger_pages.forEach((e) => {
        if (["SPOT_MARKET", "PERPETUAL_MARKET"].includes(e.trigger_page)) {
          if (!e.param_type) {
            e.param_type = "MARKET";
          }
          if (!e.page_op) {
            e.page_op = "IN";
          }
          if (!e.trigger_page_params) {
            e.trigger_page_params = [];
          }
        }
      });
      let promise = null;
      if (this.mode === "edit") {
        let permitStatusList = [
          "DRAFT",
          "CREATED",
          "AUDITED",
          "REJECTED",
          "FAILED",
        ];
        if (permitStatusList.includes(this.cur_status)) {
          this.$message.error("当前状态不允许修改!");
          return;
        }
        promise = this.$axios.patch(
          `/api/pre-trading/asset-config/${this.record.id}`,
          this.record
        );
      } else if (this.mode === "new") {
        promise = this.$axios.post("/api/pre-trading/asset-config", this.record);
      } else {
        return;
      }
      this.submit_loading = true;
      try {
        promise.then((res) => {
          if (res?.data?.code === 0) {
            let id = res.data.data.id;
            this.$message.success("保存成功");
            if (this.mode === "new") {
              this.$router.replace({ query: { id: id } });
              this.mode = "edit";
              this.record.id = id;
            }
            this.get_data(id);
            this.activeNames = ["0", "1", "2"];
          } else {
            this.$message.error(`请求失败! ${res.data?.message})`);
          }
          this.submit_loading = false;
        });
      } catch (e) {
        this.$message.error(`请求失败! (${e})`);
        this.submit_loading = false;
      }
    },
    beforeSaveRule() {
      let form = this.record;
      if (!form.name) {
        this.$message.error("请输入名称!");
        return false;
      }
      if (!form.platform) {
        this.$message.error("请选择提示条终端!");
        return false;
      }
      if (!form.type) {
        this.$message.error("请选择提示条消息类型!");
        return false;
      }
      if (form.trigger_pages.length <= 0) {
        this.$message.error("请输入触发页面!");
        return false;
      }
      let checkSuccess = true;
      form.trigger_pages.forEach((e) => {
        if (!e.trigger_page) {
          checkSuccess = false;
        }
        if (["PERPETUAL_MARKET", "SPOT_MARKET"].includes(form.trigger_page)) {
          if (!e.param_type) {
            checkSuccess = false;
          }
          if (!e.page_op) {
            checkSuccess = false;
          }
        }
        if (["ASSET_DATA"].includes(form.trigger_page)) {
          if (!e.page_op) {
            checkSuccess = false;
          }
        }
      });
      if (!checkSuccess) {
        this.$message.error("请检查触发页面配置是否完善!");
        return false;
      }
      if (!form.started_at && !form.ended_at) {
        this.$message.error("请选择提示条时间!");
        return false;
      }
      if (!form.filter_type) {
        this.$message.error("请选择客群类型!");
        return false;
      }
      if (form.filter_type !== "none") {
        if (!this.tagGroups || this.tagGroups.length === 0) {
          this.$message.error("请选择客群!");
          return;
        }
      }
      return true;
    },
    addItem() {
      this.record.trigger_pages.push({
        trigger_page: null,
        param_type: null,
        page_op: null,
        trigger_page_params: [],
      });
    },
    deleteItem(item, index) {
      this.record.trigger_pages.splice(index, 1);
    },
    notice(title, message) {
      this.$alert(message, title, {
        confirmButtonText: "确定",
      });
    },
    res_success_notice(r) {
      let title = "提交成功";
      this.notice(title, title);
    },
    res_fail_notice(res) {
      let title = "提交失败";
      let message = `(code: ${res.data?.code}; message: ${res.data?.message})`;
      this.notice(title, message);
    },
    res_error_notice(error) {
      let title = "提交失败";
      let message = `(code: ${error.response.status}; message: ${error.message})`;
      this.notice(title, message);
    },
    changeSelectableTriggerPages() {
      this.record.trigger_pages = [];
      this.selectable_trigger_pages = { ...this.trigger_pages };
      this.updateSelectableTriggerPages();
    },
    initTriggerPageParams(item) {
      // debugger
      item.trigger_page_params = null;
      item.page_type = null;
      item.page_op = null;
    },
    initTriggerPageMarketParams(item) {
      // debugger
      item.trigger_page_params = null;
      item.param_type = "MARKET";
      item.page_op = "IN";
      item.trigger_page_params = [];
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.dialog_push_test = false;
          this.dialog_template = false;
          done();
        })
        .catch((_) => {});
    },
    download_template() {
      let url = "/api/operation/email-push/template";
      this.$download_from_url(url, "email-push-template.xlsx");
    },
    before_upload(file) {
      let name = file.name;
      if (
        !name.endsWith(".xlsx") &&
        !name.endsWith(".xls") &&
        !name.endsWith("csv")
      ) {
        this.$message.error("只能上传excel表格");
        return false;
      }
    },
    upload_success(res, filters_form) {
      if (res?.code === 0) {
        // debugger;
        filters_form.user_whitelist = res.data.items.join();
        this.$message.success(`共导入 ${res.data.total} 条记录`);
      } else {
        this.$message.error(
          `上传失败! (code: ${res?.code}; message: ${res?.message})`
        );
      }
    },
    handle_template() {
      this.dialog_template = true;
      this.template_filters.title = "";
      this.template_query();
    },
    handle_template_change() {
      this.template_filters.title = null;
      this.template_query();
    },
    template_query() {
      this.template_loading = true;
      this.$axios
        .get("/api/operation/templates", { params: this.template_filters })
        .then((res) => {
          this.template_loading = false;
          if (res && res.data.code === 0) {
            let data = res.data.data;
            this.template_items = data.items;
            this.template_total = data.total;
          } else {
            this.items = [];
            this.$message.error(
              `code: ${res.data?.code}; message: ${res.data?.message}`
            );
          }
        });
    },
    handleCurrentChange(row, column, cell, event) {
      if (column.label === "操作") {
        return;
      }
      this.$confirm(`确认选择?`).then(() => {
        let id = row.id;
        if (!id) {
          return;
        }
        let contents = this.contents;
        let lang_list = Object.keys(this.languages);
        lang_list.forEach((lang) => {
          this.$axios
            .get(`/api/operation/template-content/${id}/langs/${lang}`)
            .then((res) => {
              if (res?.data?.code === 0) {
                let data = res.data.data;
                contents[lang] = {
                  content: data.content,
                };
              }
            });
        });
        this.dialog_template = false;
      });
    },
    upload_error(err) {
      this.$message.error(`上传失败! (${err})`);
    },
    toggleDisplay(dayDisplay = false) {
      this.dayDisplay = dayDisplay;
    },
    build_activeNames(status, has_content) {
      if (status === "pending" || !has_content) {
        return ["0", "1", "2"];
      } else {
        return ["0", "3"];
      }
    },
  },
  provide() {
    return { testSendFunc: null };
  },
  mounted() {
    this.get_data();
  },
  data() {
    return {
      messageConfig: {
        extr_params: {},
        has_title: false,
        use_editor: true,
        save_url: "/api/pre-trading/asset-config/${id}/langs/${lang}",
        cur_lang: null,
        has_test: false,
      },
      isOldHistory: false,
      dialog_template: false,
      mode: null,
      timerange: null,
      record: {},
      contents: {},
      langs: {},
      types: {},
      platforms: {},
      trigger_pages: {},
      filter_types: {},
      target_pages: {},
      markets: [],
      margin_markets: [],
      assets: [],
      margin_assets: [],
      perpetual_markets: [],
      perpetual_assets: [],
      param_types: {},
      page_ops: { IN: "in" },
      uploading: false,
      upload_tip: null,
      series_data: {},
      audit_data: [],
      template_items: [],
      template_filters: {
        business: "PRE_TRADING",
        enabled: true,
        title: null,
        page: null,
        limit: 10,
      },
      template_total: 0,
      template_loading: false,

      tagGroups: [],
      dialogUserGroupMap: {
        dialogUserGroup: false,
        dialogUserGroupTotal: 0,
        dialogUserGroupItems: [],
        group_types: {},
        dialogUserGroupLoading: false,
        dialogUserGroupFilters: {
          name: null,
          page: null,
          limit: 10,
        },
      },

      // statistics_data: [],
      dayDisplay: false,
      DIALOG_CREATION: "CREATION",
      DIALOG_EDIT: "EDIT",
      contents_visible: false,
      languages: {},
      cur_lang: null,

      activeNames: ["0"],
      disabled: false,
      submit_loading: false,
    };
  },
  watch: {
    timerange(val) {
      this.record.started_at = val && val[0] ? val[0].getTime() / 1000 : null;
      this.record.ended_at = val && val[1] ? val[1].getTime() / 1000 : null;
    },
  },
};
</script>
