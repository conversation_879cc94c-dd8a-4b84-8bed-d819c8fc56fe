<template>
  <el-container>
    <el-main>
      <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
        现货-深度详情
      </h2>
      <el-form :inline="true" :model="search_data">
        <el-form-item label="排序">
          <el-select clearable @change="get_data" v-model="search_data.order" placeholder="排序">
            <el-option label="±0.2%深度" value="1"></el-option>
            <el-option label="±0.5%深度" value="2"></el-option>
            <el-option label="±1%深度" value="3"></el-option>
            <el-option label="±2%深度" value="4"></el-option>
            <el-option label="±5%深度" value="5"></el-option>
          </el-select>
        </el-form-item>
        <template v-if="search_data.report_type === 'DAILY'">
          <el-form-item label="日期">
            <el-date-picker
              @change="get_data"
              v-model="search_data.report_date"
              placeholder="日期"
              value-format="yyyy-MM-dd"
              type="date"
              :picker-options="pickerOptions">
            </el-date-picker>
          </el-form-item>
        </template>

        <template v-if="search_data.report_type === 'MONTHLY'">
          <el-form-item label="日期">
            <el-date-picker
              @change="get_data"
              v-model="search_data.report_date"
              placeholder="日期"
              value-format="yyyy-MM-01"
              type="month"
              :picker-options="pickerOptions">
            </el-date-picker>
          </el-form-item>
        </template>

      </el-form>

      <el-table
        :data="items"
        style="width: 100%">

        <el-table-column
          prop="report_date"
          label="日期">
        </el-table-column>

        <el-table-column
          prop="market"
          label="市场">
        </el-table-column>

        <el-table-column
          prop="bid_ask_delta"
          :formatter="row => row.market == 'ALL'?'/':$formatPercent(row.bid_ask_delta || 0, (row.market == 'BTCUSDT' || row.market == 'BTCUSDC') ? 4 : 2)"
          label="盘口价差">
        </el-table-column>

        <el-table-column
          prop="depth1"
          label="±0.2%深度">
        </el-table-column>

        <el-table-column
          prop="depth2"
          label="±0.5%深度">
        </el-table-column>

        <el-table-column
          prop="depth3"
          label="±1%深度">
        </el-table-column>

        <el-table-column
          prop="depth4"
          label="±2%深度">
        </el-table-column>

        <el-table-column
          prop="depth5"
          label="±5%深度">
        </el-table-column>

      </el-table>

      <el-pagination :current-page.sync="search_data.page"
                     :page-size.sync="search_data.limit"
                     :total="total"
                     @current-change="get_data"
                     :page-sizes="[50]"
                     :hide-on-single-page="true"
                     layout="total, sizes, prev, pager, next, jumper"
      >
      </el-pagination>

      <el-backtop></el-backtop>
    </el-main>
  </el-container>
</template>


<script>

  const binding_url = "/api/report/spot/depth-detail";

  export default {
    watchQuery: ['order', 'report_date', 'page', 'limit'],
    key: to => to.fullPath,
    asyncData({app, query}) {
      let new_query = _.clone(query);
      if (!new_query.hasOwnProperty("order")) {
        new_query.order = '1';
      }
      if (!new_query.hasOwnProperty("market")) {
        new_query.market = 'BTCUSDT';
      }
      return app.$axios["get"](binding_url, {params: new_query})
        .then((res) => {
          if (res.data.code === 0) {
            let data = res.data.data;
            return {
              items: data.records,
              total: data.total,
              search_data: _.clone(new_query),
            }
          } else {
            this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
            return {}
          }
        })
        .catch((e) => {
        })
    },
    methods: {
      get_data(reset = true) {
        if (reset === true) {
          this.search_data.page = 1;
        }
        this.$router.replace({path: this.$route.path, query: this.search_data});
      },
    },
    data() {
      return {
        search_data: {
          report_type: 'DAILY',
          limit: 50,
        },
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() > Date.now();
          }
        }
      }
    },
  }

</script>
