<template>
  <el-container>
    <el-main>
      <h2 style="font-family: 'Microsoft YaHei',<PERSON>l,sans-serif;">
        现货-深度报表
      </h2>
      <el-tabs v-model="search_data.report_type" type="card" @tab-click="get_data(true)">
        <el-tab-pane label="日报" name="DAILY"></el-tab-pane>
        <el-tab-pane label="月报" name="MONTHLY"></el-tab-pane>
      </el-tabs>
      <template>
        <el-form :inline="true" :model="search_data">
          <el-form-item prop="start_date" label="开始时间">
            <el-date-picker
              @change="get_data"
              v-model="search_data.start_date"
              type="date"
              value-format="yyyy-MM-dd"
              :picker-options="pickerOptions"
              placeholder="时间">
            </el-date-picker>
          </el-form-item>
          <el-form-item prop="end_date" label="结束时间">
            <el-date-picker
              @change="get_data"
              v-model="search_data.end_date"
              type="date"
              value-format="yyyy-MM-dd"
              :picker-options="pickerOptions"
              placeholder="时间">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="市场">
            <el-select @change="get_data" filterable v-model="search_data.market" placeholder="市场">
              <el-option
                v-for="item in market_list"
                :key="item"
                :label="item"
                :value="item">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </template>

      <FixedTable
        :data="items"
        style="width: 100%" @header-click="show_series_dialog" ref="table">

        <el-table-column
          label="日期"
          prop="report_date">
          <template slot-scope="scope">
            <el-link :href="`/report/spot-depth-detail?report_date=${scope.row.report_date}&report_type=${search_data.report_type}`"
                     type="primary"
                     target="_blank"
                     :underline="false">
              {{ scope.row.report_date }}
            </el-link>
          </template>
        </el-table-column>

        <el-table-column
          prop="market"
          label="市场">
        </el-table-column>
        <el-table-column
          prop="bid_ask_delta"
          :formatter="row => row.market == 'ALL'?'/':$formatPercent(row.bid_ask_delta || 0, (row.market == 'BTCUSDT' || row.market == 'BTCUSDC') ? 4 : 2)"
          label="盘口价差" :render-header="renderHeader" column-key="盘口价差：盘口价差">
        </el-table-column>
        <el-table-column
          prop="depth1"
          label="±0.2%深度" :render-header="renderHeader" column-key="±0.2%深度：±0.2%深度">
        </el-table-column>

        <el-table-column
          prop="depth2"
          label="±0.5%深度" :render-header="renderHeader" column-key="±0.5%深度：±0.5%深度">
        </el-table-column>

        <el-table-column
          prop="depth3"
          label="±1%深度" :render-header="renderHeader" column-key="±1%深度：±1%深度">
        </el-table-column>

        <el-table-column
          prop="depth4"
          label="±2%深度" :render-header="renderHeader" column-key="±2%深度：±2%深度">
        </el-table-column>

        <el-table-column
          prop="depth5"
          label="±5%深度" :render-header="renderHeader" column-key="±5%深度：±5%深度">
        </el-table-column>

      </FixedTable>

      <el-pagination :current-page.sync="search_data.page"
                     :page-size.sync="search_data.limit"
                     :page-count.sync="page_count"
                     :total="total"
                     @current-change="get_data"
                     :page-sizes="[100]"
                     :hide-on-single-page="true"
                     layout="total, sizes, prev, pager, next, jumper"
      >
      </el-pagination>
      <el-dialog :visible.sync="show_dialog" width="80%" :before-close="handleClose">
        <Series ref="series" :render_info="render_info" v-if="show_dialog === true" @getChildValue="setChildValue"></Series>
      </el-dialog>

      <el-backtop></el-backtop>
    </el-main>
  </el-container>
</template>


<script>
  import Series from '~/components/Series'
  import FixedTable from '~/components/FixedTableRender.vue';
  import series from '~/plugins/report/series'
  import thousandSeperatorMixins from "~/plugins/instances/report";
  const binding_url = "/api/report/spot/depth";
  const exclude_render_columns = ['report_date', 'market'];

  export default {
    components: {
      Series,
      FixedTable,
    },
    mixins: [series, thousandSeperatorMixins],
    watchQuery: ['report_type', 'market', 'start_date', 'end_date', 'page', 'limit'],
    key: to => to.fullPath,
    asyncData({app, query}) {
      query.limit = query.limit ? query.limit : 100
      query.page = query.page ? query.page : 1
      let new_query = _.clone(query);
      if (!new_query.hasOwnProperty("report_type")) {
        new_query.report_type = 'DAILY';
      }
      if (!new_query.hasOwnProperty("market")) {
        new_query.market = 'ALL';
      }
      return app.$axios["get"](binding_url, {params: new_query})
        .then((res) => {
          if (res.data.code === 0) {
            let data = res.data.data;
            return {
              items: data.records,
              total: data.total,
              market_list: data.market_list,
              page_count: data.total_page,
              search_data: _.clone(new_query),
            }
          } else {
            this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
            return {}
          }
        })
        .catch((e) => {
        })
    },
    methods: {
      show_series_dialog(column) {
      if (exclude_render_columns.includes(column.property)) {
        return
      }
      this.show_dialog = true;
      this.set_render_info(column, {
        exclude_columns: exclude_render_columns,
        binding_url: binding_url + '?market=' + this.search_data.market,
        resp_key: 'records',
      });
    },

      get_data(reset = true) {
        if (reset === true) {
          this.search_data.page = 1;
        }
        this.$router.replace({path: this.$route.path, query: this.search_data});
      },
    },
    data() {
      return {
        page_count: 0,
        search_data: {
          report_type: 'DAILY'
        },
        market_list: [],
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() > Date.now();
          }
        }
      }
    },
  }

</script>
