<template>
  <el-container>
    <el-main>
      <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
        合约-全站收入报表
        <el-tooltip placement="right" :open-delay="500">
          <div slot="content">
            <p>全站成交额：周期内成交总市值，单向统计；</p>
            <p>总手续费收入：周期内总手续费收入市值，双向统计；</p>
            <p>持仓市值：日报数据为所有市场每小时持仓市值快照之和/24，月报数据为每天持仓市值之和/当月天数；</p>
            <p>持仓人数：日报数据为生成“每小时持仓市值快照”时所有市场的持仓人数累加/24，月报数据为每天持仓人数累加/当月天数，舍位取整</p>
            <p>成交人数：周期内成交账户数，双向统计；</p>
            <p>合约使用人数：当天合约成交人数+当天24点合约持仓人数，去重；如果是月报则为当月合约成交人数+当月最后一天24点合约持仓人数，去重；</p>
            <p>API用户数：周期内，使用API成交用户数（不排除子账号）</p>
            <p>成交笔数：周期内所有合约市场的成交笔数，单向统计；</p>
            <p>单笔金额：周期内所有合约市场的全站成交额/成交笔数</p>
            <p>新增合约用户：周期内新增的合约交易用户；</p>
            <p>平均费率：周期内总手续费（单边）/周期内全站成交额（单边）*100%；</p>
            <p>做市商平均费率：周期内外部做市商手续费/周期内外部做市商成交市值*100%；</p>
            <p>普通用户平均费率：周期内普通用户手续费/周期内普通用户成交市值*100%，普通用户为除内外部做市商的其他用户类型；</p>
            <p>普通手续费比例：周期内普通用户手续费市值/总手续费收入市值*100%；</p>
            <p>普通成交比例：普通用户成交市值/全站成交额市值*100%；</p>
            <p>实际杠杆倍数：日报数据为所有市场每小时杠杆倍数之和/24，月报数据为每天杠杆倍数之和/当月天数；</p>
          </div>
          <i class="el-icon-question"></i>
        </el-tooltip>
      </h2>
      <el-tabs v-model="search_data.report_type" type="card" @tab-click="get_data(true)">
        <el-tab-pane label="日报" name="DAILY"></el-tab-pane>
        <el-tab-pane label="月报" name="MONTHLY"></el-tab-pane>
      </el-tabs>
      <template v-if="search_data.report_type === 'DAILY'">
        <el-form :inline="true" :model="search_data">
          <el-form-item prop="start_date" label="开始时间">
            <el-date-picker
              @change="get_data"
              v-model="search_data.start_date"
              type="date"
              value-format="yyyy-MM-dd"
              :picker-options="pickerOptions"
              placeholder="时间">
            </el-date-picker>
          </el-form-item>
          <el-form-item prop="end_date" label="结束时间">
            <el-date-picker
              @change="get_data"
              v-model="search_data.end_date"
              type="date"
              value-format="yyyy-MM-dd"
              :picker-options="pickerOptions"
              placeholder="时间">
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-tooltip
              content="导出数据"
              placement="right"
              :open-delay="500"
              :hide-after="2000"
            >
              <el-button
                icon="el-icon-download"
                circle
                @click="download"
              ></el-button>
            </el-tooltip>
          </el-form-item>

        </el-form>
      </template>

      <template v-else>
        <el-form :inline="true" :model="search_data">
          <el-form-item prop="start_date" label="开始时间">
            <el-date-picker
              @change="get_data"
              v-model="search_data.start_date"
              type="month"
              value-format="yyyy-MM-dd"
              :picker-options="pickerOptions"
              placeholder="时间">
            </el-date-picker>
          </el-form-item>
          <el-form-item prop="end_date" label="结束时间">
            <el-date-picker
              @change="get_data"
              v-model="search_data.end_date"
              type="month"
              value-format="yyyy-MM-dd"
              :picker-options="pickerOptions"
              placeholder="时间">
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-tooltip
              content="导出数据"
              placement="right"
              :open-delay="500"
              :hide-after="2000"
            >
              <el-button
                icon="el-icon-download"
                circle
                @click="download"
              ></el-button>
            </el-tooltip>
          </el-form-item>

        </el-form>
      </template>

      <FixedTable
        :data="items"
        style="width: 100%" @header-click="show_series_dialog" ref="table">

        <el-table-column
          label="日期"
          prop="report_date">
          <template slot-scope="scope">
            <el-link :href="`/report/perpetual-market-detail?report_date=${$formatDate(scope.row.report_date, 'YYYY-MM-DD')}&report_type=${search_data.report_type}`"
                     type="primary"
                     target="_blank"
                     :underline="false">
              {{ search_data.report_type === 'DAILY' ? $formatDate(scope.row.report_date, 'YYYY-MM-DD'):
              $formatDate(scope.row.report_date, 'YYYY-MM') }}
            </el-link>
          </template>
        </el-table-column>

        <el-table-column
          label="市场数"
          prop="market_count">
        </el-table-column>

        <el-table-column
          prop="trade_usd"
          :formatter="row => $formatNumber(row.trade_usd, 2)"
          label="全站成交额（USD）" :render-header="renderHeader" column-key="成交额（USD）：全站成交额（USD）">
        </el-table-column>

        <el-table-column
          prop="fee_usd"
          :formatter="row => $formatNumber(row.fee_usd, 2)"
          label="总手续费收入（USD）" :render-header="renderHeader" column-key="手续费收入（USD）：总手续费收入（USD）">
        </el-table-column>

        <el-table-column
          prop="position_amount_usd"
          :formatter="row => $formatNumber(row.position_amount_usd, 2)"
          label="持仓市值" :render-header="renderHeader" column-key="市值：持仓市值">
        </el-table-column>

        <el-table-column
          prop="position_user_count"
          label="持仓人数" :render-header="renderHeader" column-key="人数：持仓人数">
        </el-table-column>

        <el-table-column
          prop="deal_user_count"
          label="成交人数" :render-header="renderHeader" column-key="人数：成交人数">
        </el-table-column>

        <el-table-column
          prop="active_user_count"
          label="合约使用人数" :render-header="renderHeader" column-key="人数：合约使用人数">
        </el-table-column>

        <el-table-column
          prop="api_user_count"
          label="API用户数" :render-header="renderHeader" column-key="API用户数：API用户数">
        </el-table-column>

        <el-table-column
          prop="deal_count"
          label="成交笔数" :render-header="renderHeader" column-key="笔数：成交笔数">
        </el-table-column>

        <el-table-column
          prop="single_deal_amount"
          :formatter="row => $formatNumber(row.single_deal_amount, 2)"
          label="单笔金额" :render-header="renderHeader" column-key="单笔金额：单笔金额">
        </el-table-column>

        <el-table-column
          prop="increase_trade_user"
          label="新增合约用户" :render-header="renderHeader" column-key="用户：新增合约用户">
        </el-table-column>
        <el-table-column
          prop="avg_fee_rate"
          :formatter="row => $formatPercent(row.avg_fee_rate)"
          label="平均费率" :render-header="renderHeader" column-key="费率：平均费率">
        </el-table-column>

        <el-table-column
          prop="avg_mm_fee_rate"
          :formatter="row => $formatPercent(row.avg_mm_fee_rate)"
          label="做市商平均费率" :render-header="renderHeader" column-key="费率：做市商平均费率">
        </el-table-column>

        <el-table-column
          prop="avg_normal_fee_rate"
          :formatter="row => $formatPercent(row.avg_normal_fee_rate)"
          label="普通用户平均费率" :render-header="renderHeader" column-key="费率：普通用户平均费率">
        </el-table-column>

        <el-table-column
          prop="normal_fee_rate"
          :formatter="row => $formatPercent(row.normal_fee_rate)"
          label="普通手续费比例" :render-header="renderHeader" column-key="手续费比例：普通手续费比例">
        </el-table-column>

        <el-table-column
          prop="normal_trade_rate"
          :formatter="row => $formatPercent(row.normal_trade_rate)"
          label="普通成交比例" :render-header="renderHeader" column-key="成交比例：普通成交比例">
        </el-table-column>

        <el-table-column
          prop="real_leverage"
          label="实际杠杆倍数" :render-header="renderHeader" column-key="杠杆倍数：实际杠杆倍数">
        </el-table-column>

      </FixedTable>

      <el-pagination :current-page.sync="search_data.page"
                     :page-size.sync="search_data.limit"
                     :page-count.sync="page_count"
                     :total="total"
                     @size-change="get_data"
                     @current-change="get_data"
                     :page-sizes="[100, 500, 1000]"
                     :hide-on-single-page="false"
                     layout="total, sizes, prev, pager, next, jumper"
      >
      </el-pagination>

      <el-dialog :visible.sync="show_dialog" width="80%" :before-close="handleClose">
        <Series ref="series" :render_info="render_info" v-if="show_dialog === true" @getChildValue="setChildValue"></Series>
      </el-dialog>

      <el-backtop></el-backtop>
    </el-main>
  </el-container>
</template>


<script>
  import moment from "moment";
  import Series from '~/components/Series'
  import FixedTable from '~/components/FixedTableRender.vue';
  import series from '~/plugins/report/series'

  const binding_url = "/api/perpetual/report-trade";
  const exclude_render_columns = ['report_date'];

  export default {
    components: {
      Series,
      FixedTable,
    },
    mixins: [series],
    watchQuery: ['report_type', 'start_date', 'end_date', 'page', 'limit'],
    key: to => to.fullPath,
    asyncData({app, query}) {
      let new_query = _.clone(query);
      if (!new_query.hasOwnProperty("report_type")) {
        new_query.report_type = 'DAILY';
      }
      if (!new_query.hasOwnProperty("limit")) {
        new_query.limit = 100;
      }
      if (!new_query.hasOwnProperty("report_type")) {
        new_query.page = 1;
      }
      if (!new_query.hasOwnProperty("report_type")) {
        new_query.report_type = 'DAILY';
      }
      return app.$axios["get"](binding_url, {params: new_query})
        .then((res) => {
          if (res.data.code === 0) {
            let data = res.data.data;
            return {
              items: data.data,
              total: data.total,
              page_count: data.total_page,
              search_data: _.clone(new_query),
            }
          } else {
            this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
            return {}
          }
        })
        .catch((e) => {
        })
    },
    methods: {
      show_series_dialog(column) {
      if (exclude_render_columns.includes(column.property)) {
        return
      }
      this.show_dialog = true;
      this.set_render_info(column, {
        exclude_columns: exclude_render_columns,
        binding_url: binding_url,
        resp_key: 'data',
      });
    },

      get_data(reset = true) {
        if (reset === true) {
          this.search_data.page = 1;
        }
        this.$router.replace({path: this.$route.path, query: this.search_data});
      },


    download() {
      let filename;
        if (this.search_data.report_type && this.search_data.report_type.toLowerCase() === 'monthly') {
          filename = 'perpetual_trade_monthly.xlsx';
        } else {
          filename = 'perpetual_trade_daily.xlsx';
        }
        let params =  {...this.search_data, export: true}
        this.$download_from_url(binding_url, filename, params)
      },
    },
    data() {
      return {
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() > Date.now();
          }
        }
      }
    },
  }

</script>
