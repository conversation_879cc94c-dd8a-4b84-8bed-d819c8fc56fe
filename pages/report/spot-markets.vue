<template>
  <el-container>
    <el-main>
      <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
        现货-交易市场报表
      </h2>
      <el-tabs v-model="search_data.report_type" type="card" @tab-click="get_data(true)">
        <el-tab-pane label="日报" name="DAILY"></el-tab-pane>
        <el-tab-pane label="月报" name="MONTHLY"></el-tab-pane>
      </el-tabs>
      <template>
        <el-form :inline="true" :model="search_data">

          <el-form-item label="排序">
            <el-select @change="get_data" v-model="search_data.order" placeholder="排序">
              <el-option label="日期" value="report_date"></el-option>
              <el-option label="成交市值" value="deal_volume_usd"></el-option>
              <el-option label="成交人数" value="deal_user_count"></el-option>
              <el-option label="成交笔数" value="deal_count"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="市场">
            <el-select @change="get_data" filterable v-model="search_data.market" placeholder="市场">
              <el-option
                v-for="item in market_list"
                :key="item"
                :label="item"
                :value="item">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item prop="start_date" label="开始时间">
            <el-date-picker
              @change="get_data"
              v-model="search_data.start_date"
              type="date"
              value-format="yyyy-MM-dd"
              :picker-options="pickerOptions"
              placeholder="时间">
            </el-date-picker>
          </el-form-item>
          <el-form-item prop="end_date" label="结束时间">
            <el-date-picker
              @change="get_data"
              v-model="search_data.end_date"
              type="date"
              value-format="yyyy-MM-dd"
              :picker-options="pickerOptions"
              placeholder="时间">
            </el-date-picker>
          </el-form-item>

          <el-form-item>
            <el-tooltip content="导出" placement="right" :open-delay="500" :hide-after="2000">
              <el-button icon="el-icon-download" circle type="success" @click="download"></el-button>
            </el-tooltip>
          </el-form-item>
        </el-form>
      </template>

      <FixedTable
        :data="items"
        style="width: 100%" @header-click="show_series_dialog" ref="table">

        <el-table-column
          label="日期"
          prop="report_date">
          <template slot-scope="scope">
            <el-link v-if="scope.row.report_date!='合计'" :href="`/report/spot-market-detail?report_date=${scope.row.report_date}&report_type=${search_data.report_type}`"
                     type="primary"
                     target="_blank"
                     :underline="false">
              {{ scope.row.report_date }}
            </el-link>
            <p v-if="scope.row.report_date==='合计'">
              {{ scope.row.report_date }}
            </p>
          </template>
        </el-table-column>

        <el-table-column
          prop="coin"
          label="币种">
        </el-table-column>

        <el-table-column
          prop="market"
          label="市场">
        </el-table-column>

        <el-table-column
          prop="trading_area"
          label="交易区"
          >
          <template slot-scope="scope">
            <el-link
              :href="`/report/spot-blocks?trading_area=${scope.row.trading_area}`"
              type="primary"
              target="_blank"
              :underline="false"
            >
              {{ scope.row.trading_area }}
            </el-link>
          </template>
        </el-table-column>

        <el-table-column
          prop="trade_amount"
          label="成交量" :render-header="renderHeader" column-key="成交量：成交量">
        </el-table-column>

        <el-table-column
          prop="trade_volume"
          label="成交额" :render-header="renderHeader" column-key="成交额：成交额">
        </el-table-column>

        <el-table-column
          prop="trade_usd"
          label="成交市值(USD)" :render-header="renderHeader" column-key="成交市值(USD)：成交市值(USD)">
        </el-table-column>

        <el-table-column
          prop="deal_usd_percent"
          :formatter="(row) => $formatPercent(row.deal_usd_percent, 2)"
          label="成交市值占比" :render-header="renderHeader" column-key="成交市值占比：成交市值占比">
        </el-table-column>

        <el-table-column
          prop="normal_deal_rate"
          label="普通成交比例" :render-header="renderHeader" column-key="普通成交比例：普通成交比例">
        </el-table-column>

        <el-table-column
          prop="fee_usd"
          label="手续费市值(USD)" :render-header="renderHeader" column-key="手续费市值(USD)：手续费市值(USD)">
        </el-table-column>

        <el-table-column
          prop="normal_fee_usd_rate"
          label="普通手续费比例" :render-header="renderHeader" column-key="普通手续费比例：普通手续费比例">
        </el-table-column>

        <el-table-column
          prop="deal_user_count"
          label="成交人数" :render-header="renderHeader" column-key="成交人数：成交人数">
        </el-table-column>

        <el-table-column
          prop="deal_count"
          label="成交笔数" :render-header="renderHeader" column-key="笔数：成交笔数">
        </el-table-column>

        <el-table-column
          prop="single_deal_amount"
          label="单笔金额" :render-header="renderHeader" column-key="单笔金额：单笔金额">
        </el-table-column>

        <el-table-column
          prop="taker_buy_amount"
          label="主动买入量" :render-header="renderHeader" column-key="主动买入量：主动买入量">
        </el-table-column>

        <el-table-column
          prop="taker_buy_count"
          label="主动买入笔数" :render-header="renderHeader" column-key="笔数：主动买入笔数">
        </el-table-column>

        <el-table-column
          prop="taker_sell_amount"
          label="主动卖出量" :render-header="renderHeader" column-key="主动卖出量：主动卖出量">
        </el-table-column>

        <el-table-column
          prop="taker_sell_count"
          label="主动卖出笔数" :render-header="renderHeader" column-key="笔数：主动卖出笔数">
        </el-table-column>

      </FixedTable>

      <el-pagination :current-page.sync="search_data.page"
                     :page-size.sync="search_data.limit"
                     :page-count.sync="page_count"
                     :total="total"
                     @current-change="get_data"
                     :page-sizes="[100]"
                     :hide-on-single-page="true"
                     layout="total, sizes, prev, pager, next, jumper"
      >
      </el-pagination>
      <el-dialog :visible.sync="show_dialog" width="80%" :before-close="handleClose">
        <Series ref="series" :render_info="render_info" v-if="show_dialog === true" @getChildValue="setChildValue"></Series>
      </el-dialog>

      <el-backtop></el-backtop>
    </el-main>
  </el-container>
</template>


<script>
  import moment from "moment";
  import Series from '~/components/Series'
  import FixedTable from '~/components/FixedTableRender.vue';
  import series from '~/plugins/report/series'
  import thousandSeperatorMixins from "~/plugins/instances/report";

  const exclude_render_columns = ['report_date', 'market', 'coin', 'trading_area'];

  const binding_url = "/api/report/spot/markets";

  export default {
    components: {
      Series,
      FixedTable,
    },
    mixins: [series, thousandSeperatorMixins],
    watchQuery: ['report_type', 'order', 'market', 'start_date', 'end_date', 'page', 'limit'],
    key: to => to.fullPath,
    asyncData({app, query}) {
      query.limit = query.limit ? query.limit : 100
      query.page = query.page ? query.page : 1
      let new_query = _.clone(query);
      if (!new_query.hasOwnProperty("report_type")) {
        new_query.report_type = 'DAILY';
      }
      if (!new_query.hasOwnProperty("order")) {
        new_query.order = 'report_date';
      }
      if (!new_query.hasOwnProperty("market")) {
        new_query.market = 'BTCUSDT';
      }
      return app.$axios["get"](binding_url, {params: new_query})
        .then((res) => {
          if (res.data.code === 0) {
            let data = res.data.data;
            return {
              items: data.records,
              total: data.total,
              market_list: data.market_list,
              page_count: data.total_page,
              search_data: _.clone(new_query),
            }
          } else {
            this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
            return {}
          }
        })
        .catch((e) => {
        })
    },
    methods: {
      show_series_dialog(column) {
        if (exclude_render_columns.includes(column.property)) {
          return
        }
        this.show_dialog = true;
        this.set_render_info(column, {
          exclude_columns: exclude_render_columns,
          binding_url: binding_url + '?market=' + this.search_data.market + '&order=' + this.search_data.order,
          resp_key: 'records',
        });
      },

      get_data(reset = true) {
        if (reset === true) {
          this.search_data.page = 1;
        }
        this.$router.replace({path: this.$route.path, query: this.search_data});
      },
      download() {
        let filename = 'spot_market_report.xlsx'
        let params =  {...this.search_data, export: true}
        this.$download_from_url(binding_url, filename, params)
      },

    },
    data() {
      return {
        page_count: 0,
        search_data: {
          report_type: 'DAILY'
        },
        market_list: [],
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() > Date.now();
          }
        }
      }
    },
  }

</script>
