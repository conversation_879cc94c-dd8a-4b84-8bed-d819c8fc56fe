<template>
  <el-container>
    <el-main>
      <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
        现货-交易区报表
      </h2>
      <el-tabs v-model="search_data.report_type" type="card" @tab-click="get_data(true)">
        <el-tab-pane label="日报" name="DAILY"></el-tab-pane>
        <el-tab-pane label="月报" name="MONTHLY"></el-tab-pane>
      </el-tabs>
      <template>
        <el-form :inline="true" :model="search_data">

          <el-form-item label="排序">
            <el-select @change="get_data" v-model="search_data.order" placeholder="排序">
              <el-option label="日期" value="report_date"></el-option>
              <el-option label="成交市值" value="deal_volume_usd"></el-option>
              <el-option label="成交人数" value="deal_user_count"></el-option>
              <el-option label="成交笔数" value="deal_count"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="交易区">
            <el-select @change="get_data" filterable v-model="search_data.trading_area" placeholder="交易区">
              <el-option
                v-for="item in trade_area_list"
                :key="item"
                :label="item"
                :value="item">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item prop="start_date" label="开始时间">
            <el-date-picker
              @change="get_data"
              v-model="search_data.start_date"
              type="date"
              value-format="yyyy-MM-dd"
              :picker-options="pickerOptions"
              placeholder="时间">
            </el-date-picker>
          </el-form-item>
          <el-form-item prop="end_date" label="结束时间">
            <el-date-picker
              @change="get_data"
              v-model="search_data.end_date"
              type="date"
              value-format="yyyy-MM-dd"
              :picker-options="pickerOptions"
              placeholder="时间">
            </el-date-picker>
          </el-form-item>
        </el-form>
      </template>

      <FixedTable
        :data="items"
        style="width: 100%" @header-click="show_series_dialog" ref="table">

        <el-table-column
          label="日期"
          prop="report_date">
        </el-table-column>

        <el-table-column
          prop="trading_area"
          label="交易区">
        </el-table-column>

        <el-table-column
          label="市场数"
          prop="market_count">
        </el-table-column>

        <el-table-column
          prop="deal_volume"
          label="成交额" :render-header="renderHeader" column-key="成交额：成交额">
        </el-table-column>

        <el-table-column
          prop="deal_volume_usd"
          label="成交市值(USD)" :render-header="renderHeader" column-key="成交市值(USD)：成交市值(USD)">
        </el-table-column>

        <el-table-column
          prop="normal_deal_rate"
          label="普通成交比例" :render-header="renderHeader" column-key="普通成交比例：普通成交比例">
        </el-table-column>

        <el-table-column
          prop="deal_user_count"
          label="成交人数" :render-header="renderHeader" column-key="成交人数：成交人数">
        </el-table-column>

        <el-table-column
          prop="deal_count"
          label="成交笔数" :render-header="renderHeader" column-key="笔数：成交笔数">
        </el-table-column>

        <el-table-column
          prop="single_deal_amount"
          label="单笔金额" :render-header="renderHeader" column-key="单笔金额：单笔金额">
        </el-table-column>

        <el-table-column
          prop="fee_usd"
          label="手续费市值(USD)" :render-header="renderHeader" column-key="手续费市值(USD)：手续费市值(USD)">
        </el-table-column>

        <el-table-column
          prop="normal_fee_usd_rate"
          label="普通手续费比例" :render-header="renderHeader" column-key="普通手续费比例：普通手续费比例">
        </el-table-column>

      </FixedTable>

      <el-pagination :current-page.sync="search_data.page"
                     :page-size.sync="search_data.limit"
                     :page-count.sync="page_count"
                     :total="total"
                     @current-change="get_data"
                     :page-sizes="[100]"
                     :hide-on-single-page="true"
                     layout="total, sizes, prev, pager, next, jumper"
      >
      </el-pagination>
      <el-dialog :visible.sync="show_dialog" width="80%" :before-close="handleClose">
        <Series ref="series" :render_info="render_info" v-if="show_dialog === true" @getChildValue="setChildValue"></Series>
      </el-dialog>

      <el-backtop></el-backtop>
    </el-main>
  </el-container>
</template>


<script>

  import moment from "moment";
  import Series from '~/components/Series'
  import FixedTable from '~/components/FixedTableRender.vue';
  import series from '~/plugins/report/series'
  import thousandSeperatorMixins from "~/plugins/instances/report";
  const exclude_render_columns = ['report_date', 'trading_area'];
  const binding_url = "/api/report/spot/blocks";

  export default {
    components: {
      Series,
      FixedTable,
    },
    mixins: [series, thousandSeperatorMixins],
    watchQuery: ['report_type', 'order', 'trading_area', 'start_date', 'end_date', 'page', 'limit'],
    key: to => to.fullPath,
    asyncData({app, query}) {
      query.limit = query.limit ? query.limit : 100
      query.page = query.page ? query.page : 1
      let new_query = _.clone(query);
      if (!new_query.hasOwnProperty("report_type")) {
        new_query.report_type = 'DAILY';
      }
      if (!new_query.hasOwnProperty("trading_area")) {
        new_query.trading_area = 'BTC';
      }
      if (!new_query.hasOwnProperty("order")) {
        new_query.order = 'report_date';
      }
      return app.$axios["get"](binding_url, {params: new_query})
        .then((res) => {
          if (res.data.code === 0) {
            let data = res.data.data;
            return {
              items: data.records,
              total: data.total,
              page_count: data.total_page,
              search_data: _.clone(new_query),
            }
          } else {
            this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
            return {}
          }
        })
        .catch((e) => {
        })
    },
    methods: {
      show_series_dialog(column) {
        if (exclude_render_columns.includes(column.property)) {
          return
        }
        this.show_dialog = true;
        this.set_render_info(column, {
          exclude_columns: exclude_render_columns,
          binding_url: binding_url + '?trading_area=' + this.search_data.trading_area,
          resp_key: 'records',
        });
      },

      get_data(reset = true) {
        if (reset === true) {
          this.search_data.page = 1;
        }
        this.$router.replace({path: this.$route.path, query: this.search_data});
      },
    },
    data() {
      return {
        page_count: 0,
        search_data: {
          report_type: 'DAILY',
          trading_area: 'BTC',
          order: 'report_date',
        },
        trade_area_list: [
          "BTC",
          "BCH",
          "ETH",
          "USDT",
          "USDC",
          "TUSD",
          "PAX",
          "CET",
        ],
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() > Date.now();
          }
        }
      }
    },
  }

</script>
