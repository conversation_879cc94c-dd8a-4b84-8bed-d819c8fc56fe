<template>
  <el-container>
    <el-main>
      <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
        合约-市场报表
      </h2>
      <el-tabs v-model="search_data.report_type" type="card" @tab-click="get_data(true)">
        <el-tab-pane label="日报" name="DAILY"></el-tab-pane>
        <el-tab-pane label="月报" name="MONTHLY"></el-tab-pane>
      </el-tabs>
      <template v-if="search_data.report_type === 'DAILY'">
        <el-form :inline="true" :model="search_data">
          <el-form-item prop="start_date" label="开始时间">
            <el-date-picker
              @change="get_data"
              v-model="search_data.start_date"
              type="date"
              value-format="yyyy-MM-dd"
              :picker-options="pickerOptions"
              placeholder="开始时间">
            </el-date-picker>
          </el-form-item>
          <el-form-item prop="end_date" label="结束时间">
            <el-date-picker
              @change="get_data"
              v-model="search_data.end_date"
              type="date"
              value-format="yyyy-MM-dd"
              :picker-options="pickerOptions"
              placeholder="结束时间">
            </el-date-picker>
          </el-form-item>


          <el-form-item label="市场" :model="search_data">
            <el-select filterable clearable v-model="search_data.market" @change="get_data" placeholder="<ALL>">
              <el-option v-for="name in $store.state.perpetual.info.market_list"
                         :key="name"
                         :label="name"
                         :value="name">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="排序">
            <el-select clearable filterable v-model="search_data.order" placeholder="请选择" @change="get_data">
              <el-option v-for="(v, k) in order_type"
                         :key="k"
                         :label="v"
                         :value="k">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-tooltip
              content="导出数据"
              placement="right"
              :open-delay="500"
              :hide-after="2000"
            >
              <el-button
                icon="el-icon-download"
                circle
                @click="download"
              ></el-button>
            </el-tooltip>
          </el-form-item>

        </el-form>

      </template>

      <template v-else>
        <el-form :inline="true" :model="search_data">
          <el-form-item prop="start_date" label="开始时间">
            <el-date-picker
              @change="get_data"
              v-model="search_data.start_date"
              type="month"
              value-format="yyyy-MM-dd"
              :picker-options="pickerOptions"
              placeholder="开始时间">
            </el-date-picker>
          </el-form-item>
          <el-form-item prop="end_date" label="结束时间">
            <el-date-picker
              @change="get_data"
              v-model="search_data.end_date"
              type="month"
              value-format="yyyy-MM-dd"
              :picker-options="pickerOptions"
              placeholder="结束时间">
            </el-date-picker>
          </el-form-item>

          <el-form-item label="市场" :model="search_data">
            <el-select filterable clearable v-model="search_data.market" @change="get_data" placeholder="<ALL>">
              <el-option v-for="name in $store.state.perpetual.info.market_list"
                         :key="name"
                         :label="name"
                         :value="name">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="排序">
            <el-select clearable filterable v-model="search_data.order" placeholder="请选择" @change="get_data">
              <el-option v-for="(v, k) in order_type"
                         :key="k"
                         :label="v"
                         :value="k">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-tooltip
              content="导出数据"
              placement="right"
              :open-delay="500"
              :hide-after="2000"
            >
              <el-button
                icon="el-icon-download"
                circle
                @click="download"
              ></el-button>
            </el-tooltip>
          </el-form-item>

        </el-form>

      </template>

      <FixedTable
        :data="items"
        style="width: 100%"
        @header-click="show_series_dialog" ref="table">

        <el-table-column
          label="日期"
          prop="report_date"
          :formatter="row => $formatDate(row.report_date, search_data.report_type === 'DAILY' ? 'YYYY-MM-DD' : 'YYYY-MM')">
        </el-table-column>

        <el-table-column
          prop="market"
          label="市场">
        </el-table-column>

        <el-table-column
          prop="position_amount_usd"
          :formatter="row => $formatNumber(row.position_amount_usd, 2)"
          label="持仓市值" :render-header="renderHeader" column-key="市值：持仓市值">
        </el-table-column>

        <el-table-column
          prop="position_user_count"
          label="持仓人数" :render-header="renderHeader" column-key="人数：持仓人数">
        </el-table-column>

        <el-table-column
          prop="active_user_count"
          label="合约使用人数" :render-header="renderHeader" column-key="人数：合约使用人数">
        </el-table-column>

        <el-table-column
          prop="deal_user_count"
          label="成交人数" :render-header="renderHeader" column-key="人数：成交人数">
        </el-table-column>

        <el-table-column
          prop="deal_amount"
          :formatter="row => $formatNumber(row.deal_amount, 2)"
          label="成交量（USD）" :render-header="renderHeader" column-key="成交量（USD）：成交量（USD）">
        </el-table-column>

        <el-table-column
          prop="deal_usd_percent"
          :formatter="row => $formatPercent(row.deal_usd_percent)"
          label="成交额占比" :render-header="renderHeader" column-key="占比：成交额占比">
        </el-table-column>

        <el-table-column
          prop="deal_count"
          label="成交笔数" :render-header="renderHeader" column-key="笔数：成交笔数">
        </el-table-column>

        <el-table-column
          prop="single_deal_amount"
          :formatter="row => $formatNumber(row.single_deal_amount, 2)"
          label="单笔金额" :render-header="renderHeader" column-key="单笔金额：单笔金额">
        </el-table-column>

        <el-table-column
          prop="buy_count"
          label="主动买入笔数" :render-header="renderHeader" column-key="笔数：主动买入笔数">
        </el-table-column>

        <el-table-column
          prop="buy_amount"
          :formatter="row => $formatNumber(row.buy_amount, 2)"
          label="主动买入量（USD）" :render-header="renderHeader" column-key="买入量（USD）：主动买入量（USD）">
        </el-table-column>

        <el-table-column
          prop="sell_count"
          label="主动卖出笔数" :render-header="renderHeader" column-key="笔数：主动卖出笔数">
        </el-table-column>

        <el-table-column
          prop="sell_amount"
          :formatter="row => $formatNumber(row.sell_amount, 2)"
          label="主动卖出量（USD）" :render-header="renderHeader" column-key="卖出量（USD）：主动卖出量（USD）">
        </el-table-column>

        <el-table-column
          prop="real_leverage"
          label="实际杠杆倍数" :render-header="renderHeader" column-key="倍数：实际杠杆倍数">
        </el-table-column>

        <el-table-column
          prop="fee_usd"
          label="手续费市值(USD)" :render-header="renderHeader" column-key="手续费市值(USD)：手续费市值(USD)">
        </el-table-column>

      </FixedTable>

      <el-pagination :current-page.sync="search_data.page"
                     :page-size.sync="search_data.limit"
                     :page-count.sync="page_count"
                     :total="total"
                     @size-change="get_data"
                     @current-change="get_data"
                     :page-sizes="[100, 500, 1000]"
                     :hide-on-single-page="false"
                     layout="total, sizes, prev, pager, next, jumper"
      >
      </el-pagination>

      <el-dialog :visible.sync="show_dialog" width="80%" :before-close="handleClose">
        <Series ref="series" :render_info="render_info" v-if="show_dialog === true" @getChildValue="setChildValue"></Series>
      </el-dialog>

      <el-backtop></el-backtop>
    </el-main>
  </el-container>
</template>


<script>
  import Series from '~/components/Series'
  import FixedTable from '~/components/FixedTableRender.vue';
  import series from '~/plugins/report/series'

  const binding_url = "/api/perpetual/report-market";
  const exclude_render_columns = ['report_date', 'market'];

  export default {
    components: {
      Series,
      FixedTable,
    },
    mixins: [series],
    watchQuery: ['report_type', 'start_date', 'end_date', 'order', 'market', 'page', 'limit'],
    key: to => to.fullPath,
    asyncData({app, query}) {
      let new_query = _.clone(query);
      if (!new_query.hasOwnProperty("report_type")) {
        new_query.report_type = 'DAILY';
      }
      if (!new_query.hasOwnProperty("limit")) {
        new_query.limit = 100;
      }
      if (!new_query.hasOwnProperty("page")) {
        new_query.page = 1;
      }
      if (!new_query.hasOwnProperty("market")) {
        new_query.market = "BTCUSDT"
      }
      if (!new_query.hasOwnProperty("order")) {
        new_query.order = "report_date"
      }
      return app.$axios["get"](binding_url, {params: new_query})
        .then((res) => {
          if (res.data.code === 0) {
            let data = res.data.data;
            return {
              items: data.data,
              total: data.total,
              page_count: data.total_page,
              search_data: _.clone(new_query),
            }
          } else {
            this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
            return {}
          }
        })
        .catch((e) => {
        })
    },
    methods: {
      show_series_dialog(column) {
      if (exclude_render_columns.includes(column.property)) {
        return
      }
      this.show_dialog = true;
      this.set_render_info(column, {
        exclude_columns: exclude_render_columns,
        binding_url: binding_url + '?market=' + this.search_data.market,
        resp_key: 'data',
      });
    },
      get_data(reset = true) {
        if (reset === true) {
          this.search_data.page = 1;
        }
        this.$router.replace({path: this.$route.path, query: this.search_data});
      },


    download() {
      let filename;
        if (this.search_data.report_type && this.search_data.report_type.toLowerCase() === 'monthly') {
          filename = 'perpetual_market_monthly.xlsx';
        } else {
          filename = 'perpetual_market_daily.xlsx';
        }
        let params =  {...this.search_data, export: true}
        this.$download_from_url(binding_url, filename, params)
      },
    },
    data() {
      return {
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() > Date.now();
          }
        },
        order_type: {
          report_date : "日期",
          deal_amount : "成交量",
          deal_user_count : "成交人数",
          deal_count : "成交笔数",
        }
      }
    },
  }

</script>
