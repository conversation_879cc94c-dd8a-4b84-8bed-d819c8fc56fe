<template>
  <el-container>
    <el-main>
      <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
        现货-交易币种详情
      </h2>
      <el-form :inline="true" :model="search_data">
        <el-form-item label="排序">
          <el-select clearable @change="get_data" v-model="search_data.order" placeholder="排序">
            <el-option label="成交市值" value="deal_volume_usd"></el-option>
            <el-option label="成交人数" value="deal_user_count"></el-option>
            <el-option label="成交笔数" value="deal_count"></el-option>
            <el-option label="手续费市值" value="fee_usd"></el-option>
          </el-select>
        </el-form-item>

        <template v-if="search_data.report_type === 'DAILY'">
          <el-form-item label="日期">
            <el-date-picker
              @change="get_data"
              v-model="search_data.report_date"
              placeholder="日期"
              value-format="yyyy-MM-dd"
              type="date"
              :picker-options="pickerOptions">
            </el-date-picker>
          </el-form-item>
        </template>

        <template v-if="search_data.report_type === 'MONTHLY'">
          <el-form-item label="日期">
            <el-date-picker
              @change="get_data"
              v-model="search_data.report_date"
              placeholder="日期"
              value-format="yyyy-MM-01"
              type="month"
              :picker-options="pickerOptions">
            </el-date-picker>
          </el-form-item>
        </template>

      </el-form>

      <el-table
        :data="items"
        style="width: 100%">

        <el-table-column
          prop="report_date"
          label="日期">
        </el-table-column>

        <el-table-column
          prop="coin"
          label="交易币种">
          <template slot-scope="scope">
            <span v-if="scope.row.coin != 'ALL'">
            <el-link :href="`/report/spot-coin?coin=${scope.row.coin}`" type="primary" target="_blank" :underline="false">
              {{ scope.row.coin }}
            </el-link>
            </span>
            <span v-else>{{ scope.row.coin }}</span>
          </template>
        </el-table-column>

        <el-table-column
          prop="trade_amount"
          label="成交量">
        </el-table-column>

        <el-table-column
          prop="trade_usd"
          label="成交市值(USD)">
        </el-table-column>

        <el-table-column
          prop="fee_usd"
          label="手续费市值(USD)">
        </el-table-column>

        <el-table-column
          prop="deal_user_count"
          label="成交人数">
        </el-table-column>

        <el-table-column
          prop="deal_count"
          label="成交笔数">
        </el-table-column>

        <el-table-column
          prop="single_deal_amount"
          label="单笔金额">
        </el-table-column>

        <el-table-column
          prop="taker_buy_amount"
          label="主动买入量">
        </el-table-column>

        <el-table-column
          prop="taker_buy_count"
          label="主动买入笔数">
        </el-table-column>

        <el-table-column
          prop="taker_sell_amount"
          label="主动卖出量">
        </el-table-column>

        <el-table-column
          prop="taker_sell_count"
          label="主动卖出笔数">
        </el-table-column>

      </el-table>

      <el-pagination :current-page.sync="search_data.page"
                     :page-size.sync="search_data.limit"
                     :page-count.sync="page_count"
                     @current-change="get_data"
                     :page-sizes="[100]"
                     :hide-on-single-page="true"
                     layout="total, sizes, prev, pager, next, jumper"
      >
      </el-pagination>

      <el-backtop></el-backtop>
    </el-main>
  </el-container>
</template>


<script>
  import moment from "moment";

  const binding_url = "/api/report/spot/coin-detail";

  export default {
    watchQuery: ['report_type', 'order', 'market', 'report_date', 'page', 'limit'],
    key: to => to.fullPath,
    asyncData({app, query}) {
      let new_query = _.clone(query);
      if (!new_query.hasOwnProperty("order")) {
        new_query.order = 'deal_volume_usd';
      }
      return app.$axios["get"](binding_url, {params: new_query})
        .then((res) => {
          if (res.data.code === 0) {
            let data = res.data.data;
            return {
              items: data.records,
              total: data.total,
              market_list: data.market_list,
              page_count: data.total_page,
              search_data: _.clone(new_query),
            }
          } else {
            this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
            return {}
          }
        })
        .catch((e) => {
        })
    },
    methods: {
      get_data(reset = true) {
        if (reset === true) {
          this.search_data.page = 1;
        }
        this.$router.replace({path: this.$route.path, query: this.search_data});
      },
    },
    data() {
      return {
        page_count: 0,
        search_data: {
          report_type: 'DAILY'
        },
        market_list: [],
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() > Date.now();
          }
        }
      }
    },
  }

</script>
