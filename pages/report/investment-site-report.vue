<template>
  <el-container>
    <el-main>
      <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
        全站理财报表
        <el-tooltip placement="right" :open-delay="500">
          <div slot="content">
            <p>理财收益人数：对应日期有收到理财收益的用户，子账户独立计算</p>
            <p>新增理财收益人数：对应日期新增的理财用户数量，首次收到理财收益，子账户独立计算</p>
            <p>理财人数：对应日期理财账户有资产的用户数，子账户独立计算</p>
            <p>新增理财人数：对应日期新增的理财用户数量，理财账户首次有资产，子账户独立计算</p>
            <p>理财市值（USD）：对应日期的有效理财市值</p>
            <p>理财收益：对应日期的理财收益数量</p>
            <p>年化收益率：年化收益率=理财收益/理财市值*365</p>
          </div>
          <i class="el-icon-question"></i>
        </el-tooltip>
      </h2>
      <el-tabs v-model="search_data.report_type" type="card"  @tab-click="search(true)">
        <el-tab-pane label="日报" name="daily"></el-tab-pane>
        <el-tab-pane label="月报" name="monthly"></el-tab-pane>
      </el-tabs>
      <el-form :inline="true" :model="search_data">
        <template v-if="search_data.report_type === 'daily'">
          <el-form-item prop="start_date" label="开始时间">
            <el-date-picker
              required
              v-model="search_data.start_date"
              type="date"
              value-format="yyyy-MM-dd"
              :picker-options="pickerOptions"
              placeholder="开始时间">
            </el-date-picker>
          </el-form-item>
          <el-form-item prop="end_date" label="结束时间">
            <el-date-picker
              required
              v-model="search_data.end_date"
              type="date"
              value-format="yyyy-MM-dd"
              :picker-options="pickerOptions"
              placeholder="结束时间">
            </el-date-picker>
          </el-form-item>
        </template>

        <template v-if="search_data.report_type === 'monthly'">
          <el-form-item prop="start_date" label="开始时间">
            <el-date-picker
              required
              v-model="search_data.start_date"
              type="month"
              value-format="yyyy-MM-01"
              :picker-options="pickerOptions"
              placeholder="开始时间">
            </el-date-picker>
          </el-form-item>
          <el-form-item prop="end_date" label="结束时间">
            <el-date-picker
              required
              v-model="search_data.end_date"
              type="month"
              value-format="yyyy-MM-01"
              :picker-options="pickerOptions"
              placeholder="结束时间">
            </el-date-picker>
          </el-form-item>
        </template>

        <el-form-item>
          <el-button type="primary" @click="search">查询</el-button>
        </el-form-item>

        <el-form-item>
          <el-tooltip
            content="导出数据"
            placement="right"
            :open-delay="500"
            :hide-after="2000"
          >
            <el-button
              icon="el-icon-download"
              circle
              @click="download"
            ></el-button>
          </el-tooltip>
        </el-form-item>

      </el-form>

      <FixedTable
        :data="items"
        @header-click="show_series_dialog" ref="table"
        size="medium"
        stripe>
        <el-table-column
          label="日期"
          prop="report_date"
          >
          <template slot-scope="scope">
            <el-link :href="`/report/investment-asset-detail?report_date=${$formatDate(scope.row.report_date, 'YYYY-MM-DD')}&report_type=${search_data.report_type.toUpperCase()}`"
                     type="primary"
                     target="_blank"
                     :underline="false">
              {{ $formatDate(scope.row.report_date, search_data.report_type === 'daily'?'YYYY-MM-DD':'YYYY-MM') }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column
          label="理财收益人数"
          prop="interest_user_count"
          :render-header="renderHeader" column-key="理财收益人数：理财收益人数"
        >
        </el-table-column>
        <el-table-column
          label="新增理财收益人数"
          prop="increase_interest_user"
          :render-header="renderHeader" column-key="新增理财收益人数：新增理财收益人数"
        >
        </el-table-column>
        <el-table-column
          label="理财人数"
          prop="investment_user_count"
        :render-header="renderHeader" column-key="理财人数：理财人数">
        </el-table-column>
        <el-table-column
          label="新增理财人数"
          prop="increase_investment_user"
          :render-header="renderHeader" column-key="新增理财人数：新增理财人数">
        </el-table-column>
        <el-table-column
          label="理财市值(USD)"
          prop="usd"
          :formatter="row => $formatNumber(row.usd, 2)"
          :render-header="renderHeader" column-key="理财市值(USD)：理财市值(USD)"
        >
        </el-table-column>
        <el-table-column
          label="理财总收益(USD)"
          :formatter="row => $formatNumber(row.investment_interest_usd, 2)"
          prop="investment_interest_usd"
          :render-header="renderHeader" column-key="理财收益(USD)：理财收益(USD)"
        >
        </el-table-column>

        <el-table-column
          label="基础收益(USD)"
          :formatter="row => $formatNumber(row.interest_base_usd || 0, 2)"
          prop="interest_base_usd"
          :render-header="renderHeader" column-key="基础收益(USD)：基础收益(USD)"
        >
        </el-table-column>

        <el-table-column
          label="阶梯补贴(USD)"
          :formatter="row => $formatNumber(row.interest_ladder_usd || 0, 2)"
          prop="interest_ladder_usd"
          :render-header="renderHeader" column-key="阶梯补贴(USD)：阶梯补贴(USD)"
        >
        </el-table-column>

        <el-table-column
          label="固定补贴(USD)"
          :formatter="row => $formatNumber(row.interest_fixed_usd || 0, 2)"
          prop="interest_fixed_usd"
          :render-header="renderHeader" column-key="固定补贴(USD)：固定补贴(USD)"
        >
        </el-table-column>
        
        <el-table-column
          label="年化收益率"
          :formatter="row => $formatPercent(row.year_income_rate, 2)"
          prop="year_income_rate"
        :render-header="renderHeader" column-key="年化收益率：年化收益率">
        </el-table-column>
      </FixedTable>

      <el-pagination :current-page.sync="search_data.page"
                     :page-size.sync="search_data.limit"
                     @size-change="search"
                     @current-change="search"
                     :page-sizes="[100, 50]"
                     :hide-on-single-page="true"
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>

      <el-dialog :visible.sync="show_dialog" width="80%" :before-close="handleClose">
        <Series ref="series" :render_info="render_info" v-if="show_dialog === true" @getChildValue="setChildValue"></Series>
      </el-dialog>

      <el-backtop></el-backtop>
    </el-main>
  </el-container>
</template>


<script>
  import moment from "moment";
  import Series from '~/components/Series'
  import FixedTable from '~/components/FixedTableRender.vue';
  import series from '~/plugins/report/series'

  const exclude_render_columns = ['report_date', ];

  const binding_url = "/api/report/investment/site-investment-report";

  export default {
    components: {
      Series,
      FixedTable,
    },
    mixins: [series],
    watchQuery: ['report_type', 'start_date', 'end_date', 'page', 'limit'],
    key: to => to.fullPath,
    asyncData({app, query}) {
      query.limit = query.limit ? query.limit : 100
      query.page = query.page ? query.page : 1
      let new_query = _.clone(query);
      if(!new_query.hasOwnProperty("report_type")) {
        new_query.report_type = 'daily';
      }
      return app.$axios["get"](binding_url, {params: new_query})
        .then((res) => {
          return {
            total: res.data.data.total,
            items: res.data.data.items,
            search_data: _.clone(new_query),
          }
        })
        .catch((e) => {
        })
    },
    methods: {
      show_series_dialog(column) {
        if (exclude_render_columns.includes(column.property)) {
          return
        }
        this.show_dialog = true;
        this.set_render_info(column, {
          exclude_columns: exclude_render_columns,
          binding_url: binding_url,
          resp_key: 'items',
          report_type_lower: true,
        });
      },

      search(reset=true) {
        if(reset === true) {
          this.search_data.page = 1;
        }
        this.$router.push({path: this.$route.path, query: this.search_data});
      },
      format_date(timestamp, pattern = "YYYY-MM-DD") {
        return moment(Number(timestamp) * 1000).format(pattern);
      },


    download() {
      let filename;
        if (this.search_data.report_type && this.search_data.report_type.toLowerCase() === 'monthly') {
          filename = 'investment_site_monthly.xlsx';
        } else {
          filename = 'investment_site_daily.xlsx';
        }
        let params =  {...this.search_data, export: true}
        this.$download_from_url(binding_url, filename, params)
      },
    },
    data() {
      return {
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() > Date.now();
          }
        },
        series_search_data: {
          start_date: null,
          end_date: null,
          series_type: null,
        },
        search_data: {},
        series_types: {},
        show_dialog: false,
        chart_type: "user_count",
        series_chart_options: {},
        series_time_range: null
      }
    },
  }

</script>
