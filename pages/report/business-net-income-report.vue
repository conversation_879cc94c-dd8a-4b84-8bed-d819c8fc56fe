<template>
  <div class="table-data">
    <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
      应收报表
      <el-tooltip placement="right" :open-delay="500">
        <div slot="content">
          <p>返佣：平台返佣支出包括了大使返佣、普通返佣和经纪商返佣</p>
          <p>AAVE质押收入和利息币质押收入被统计进staking收入中</p>
        </div>
        <i class="el-icon-question"></i>
      </el-tooltip>
    </h2>

    <el-tabs v-model="filters.report_type" type="card" @tab-click="get_data">
      <el-tab-pane label="日报" name="daily"></el-tab-pane>
      <el-tab-pane label=月报 name="monthly"></el-tab-pane>
    </el-tabs>
    <el-form :inline="true" :model="filters">
      <template>
        <el-form-item prop="start_date" label="开始时间">
          <el-date-picker
            v-model="filters.start_date"
            :type="filters.report_type == 'daily' ? 'date' : 'month'"
            value-format="yyyy-MM-dd"
            :picker-options="pickerOptions"
            placeholder="时间"
            @change="handle_page_refresh"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item prop="end_date" label="结束时间">
          <el-date-picker
            v-model="filters.end_date"
            :type="filters.report_type == 'daily' ? 'date' : 'month'"
            value-format="yyyy-MM-dd"
            :picker-options="pickerOptions"
            placeholder="时间"
            @change="handle_page_refresh"
          >
          </el-date-picker>
        </el-form-item>
      </template>

      <el-form-item>
        <el-button type="primary" @click="handle_page_refresh">查询</el-button>
      </el-form-item>
      <ExportExcel id="table" title="应收报表"></ExportExcel>
    </el-form>

    <FixedTable v-loading="loading"
              id="table"
              :data="records"
               @header-click="show_series_dialog" ref="table"
              stripe>

      <el-table-column
        label="日期"
        prop="report_date"
        :formatter="row => $formatDate(row.report_date, 'YYYY-MM-DD')">
      </el-table-column>

      <el-table-column
        prop="net_income"
        :formatter="row => $formatNumber(row.net_income, 2)"
        :render-header="renderHeader" column-key="净收入（USD）：净收入（USD）"
        label="净收入（USD）">
      </el-table-column>

      <el-table-column
        prop="gross_margin"
        :formatter="row => $formatPercent(row.gross_margin, 2)"
        :render-header="renderHeader" column-key="毛利率：毛利率"
        label="毛利率">
      </el-table-column>

      <el-table-column label="收入（USD）">

        <el-table-column
          prop="spot_trade_fee"
          :formatter="row => $formatNumber(row.spot_trade_fee, 2)"
          :render-header="renderHeader" column-key="现货手续费：现货手续费"
          label="现货手续费">
        </el-table-column>

        <el-table-column
          prop="exchange_fee"
          :formatter="row => $formatNumber(row.exchange_fee, 2)"
          :render-header="renderHeader" column-key="兑换手续费：兑换手续费"
          label="兑换手续费">
        </el-table-column>

        <el-table-column
          prop="perpetual_trade_fee"
          :formatter="row => $formatNumber(row.perpetual_trade_fee, 2)"
          :render-header="renderHeader" column-key="合约手续费：合约手续费"
          label="合约手续费">
        </el-table-column>

        <el-table-column
          prop="withdraw_fee"
          :formatter="row => $formatNumber(row.withdraw_fee, 2)"
          :render-header="renderHeader" column-key="提现手续费：提现手续费"
          label="提现手续费">
        </el-table-column>

        <el-table-column
          prop="margin_interest"
          :formatter="row => $formatNumber(row.margin_interest, 2)"
          :render-header="renderHeader" column-key="杠杆利息：杠杆利息"
          label="杠杆利息">
        </el-table-column>

        <el-table-column
          prop="pledge_interest"
          :formatter="row => $formatNumber(row.pledge_interest, 2)"
          :render-header="renderHeader" column-key="借贷利息：借贷利息"
          label="借贷利息">
        </el-table-column>

        <el-table-column
          prop="staking_interest"
          :formatter="row => $formatNumber(row.staking_interest, 2)"
          :render-header="renderHeader" column-key="Staking：Staking"
          label="Staking">
        </el-table-column>

        <!-- <el-table-column
          prop="sign_off_user_balance"
          :formatter="row => $formatNumber(row.sign_off_user_balance, 2)"
          :render-header="renderHeader" column-key="注销账户资产：注销账户资产"
          label="注销账户资产">
        </el-table-column> -->
      </el-table-column>

      <el-table-column label="支出（USD）">

        <el-table-column
          prop="withdraw_pay_fee"
          :formatter="row => $formatNumber(Math.abs(row.withdraw_pay_fee), 2)"
          :render-header="renderHeader" column-key="钱包Gas费：钱包Gas费"
          label="钱包Gas费">
        </el-table-column>

        <el-table-column
          prop="refer_pay"
          :formatter="row => $formatNumber(Math.abs(row.refer_pay), 2)"
          :render-header="renderHeader" column-key="返佣：返佣"
          label="返佣">
        </el-table-column>

        <el-table-column
          prop="investment_pay"
          :formatter="row => $formatNumber(Math.abs(Number(row.investment_pay)), 2)"
          :render-header="renderHeader" column-key="活期理财：活期理财"
          label="活期理财">
        </el-table-column>

        <el-table-column
          prop="staking_pay"
          :formatter="row => $formatNumber(Math.abs(Number(row.staking_pay)), 2)"
          :render-header="renderHeader" column-key="Staking：Staking"
          label="Staking">
        </el-table-column>

        <el-table-column
          prop="amm_trade_pay_fee"
          :formatter="row => $formatNumber(Math.abs(row.amm_trade_pay_fee), 2)"
          :render-header="renderHeader" column-key="AMM：AMM"
          label="AMM">
        </el-table-column>

        <el-table-column
          prop="maker_cashback_pay"
          :formatter="row => $formatNumber(Math.abs(row.maker_cashback_pay), 2)"
          :render-header="renderHeader" column-key="现货返现：现货返现"
          label="现货返现">
        </el-table-column>
      </el-table-column>


      <el-table-column type="expand">
        <template slot-scope="props">
          <el-form label-position="left" inline class="demo-table-expand">
            <el-form-item label="p2p手续费收入">
              <span>{{ props.row.p2p_order_fee }}</span>
            </el-form-item>
            <el-form-item label="预测市场手续费收入">
              <span>{{ props.row.pre_trading_fee }}</span>
            </el-form-item>
            <el-form-item label="注销用户资产收入">
              <span>{{ props.row.sign_off_user_balance }}</span>
            </el-form-item>
            <el-form-item label="小额账户回收资产收入">
              <span>{{ props.row.cleaned_balance }}</span>
            </el-form-item>

            <el-form-item label="授信利息">
              <span>{{ props.row.credit_interest }}</span>
            </el-form-item>
            <el-form-item label="一口价兑换-收入">
              <span>{{ props.row.fixed_exchange_profit }}</span>
            </el-form-item>
            <el-form-item label="一口价兑换-支出">
              <span>{{ props.row.fixed_exchange_loss }}</span>
            </el-form-item>
          </el-form>
        </template>
      </el-table-column>

    </FixedTable>
    <el-pagination :current-page.sync="filters.page"
                   :page-size.sync="filters.limit"
                   @size-change="get_data(false)"
                   @current-change="get_data(false)"
                   :page-sizes="[50, 100, 200, 500]"
                   layout="total, sizes, prev, pager, next, jumper"
                   :total="total">
    </el-pagination>
    <el-dialog :visible.sync="show_dialog" width="80%" :before-close="handleClose">
        <Series ref="series" :render_info="render_info" v-if="show_dialog === true" @getChildValue="setChildValue"></Series>
      </el-dialog>

    <el-backtop></el-backtop>

  </div>
</template>

<script>
  import {Decimal} from "decimal.js";
  import Series from "@/components/Series";
  import FixedTable from '~/components/FixedTableRender.vue';
  import ExportExcel from '~/components/ExportExcel';
  import series from "@/plugins/report/series";
  const exclude_render_columns = ['report_date'];
const binding_url = "/api/report/income/business-report";
export default {
  components: {
      Series,
      FixedTable,
      ExportExcel
    },
  mixins: [series],
    methods: {
      show_series_dialog(column) {
        if (exclude_render_columns.includes(column.property)) {
          return
        }
        this.show_dialog = true;
        this.set_render_info(column, {
          exclude_columns: exclude_render_columns,
          binding_url: binding_url,
          resp_key: 'records',
          report_type_lower: true,
        });
      },

      get_data() {
        this.loading = true;
        this.$axios.get(
          '/api/report/income/business-report', {params: this.filters}
        ).then(
          res => {
            if (res.data.code === 0) {
              let data = res.data.data;
              this.records = data.records;
              this.total = data.total;
              this.loading = false;
            } else {
              this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
            }
          }
        ).catch(err => {
          this.$message.error(`刷新失败! (${err})`);
        });
      },
      update_router_query() {
        let query = {};
        Object.assign(query, this.filters);
        Object.keys(query).forEach(key => !query[key] && delete query[key]);
        this.$router.replace({ query: query });
      },
      handle_page_refresh() {
        this.reset_page();
        this.get_data();
      },
      reset_page() {
        this.filters.page = 1;
      },

    },
    mounted() {
      this.get_data();
    },
    created() {
      let query = this.$route.query;
      let filters = this.filters;
      filters.asset = query.asset || null;
      filters.chain = query.chain || null;
      filters.user = query.user || null;
      filters.type = query.type || null;
      filters.status = query.status || null;
      filters.start_date = query.status || null;
      filters.end_date = query.status || null;
      filters.page = query.page ? parseInt(query.page) : 1;
      filters.report_type = query.report_type || 'daily';
      this.$watch('filters', {
        handler: function() {
          this.update_router_query();
        },
        deep: true,
      });
    },
    data() {
      return {
        records: [],
        timestamp: null,
        show_dialog: false,
        loading: true,
        active_type: "",
        active_types: {},
        filters: {
          page: 1,
          limit: 50,
          start_date: null,
          end_date: null,
          report_type: null,
        },
        series_filters: {
          series_type: null,
          start_date: null,
          end_date: null,
        },
        series_keys: [
          'net_income',
          'gross_margin',
          'spot_trade_fee',
          'perpetual_trade_fee',
          'withdraw_fee',
          'margin_interest',
          'credit_interest',
          'withdraw_pay_fee',
          'refer_pay',
          'investment_pay',
          'amm_trade_pay_fee',
          'maker_cashback_pay',
        ],
        series_types: {},
        chart_options: {},
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() > Date.now();
          },
        },
        series_time_range: null
      }
    },
    computed: {
    }
  }
</script>
