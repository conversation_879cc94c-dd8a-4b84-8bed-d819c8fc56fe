<template>
  <el-container>
    <el-main>
      <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
        合约-市场报表详情
      </h2>

      <el-tabs v-model="search_data.report_type" type="card" @tab-click="get_data(true)">
        <el-tab-pane label="日报" name="DAILY"></el-tab-pane>
        <el-tab-pane label="月报" name="MONTHLY"></el-tab-pane>
      </el-tabs>

      <el-form :inline="true" :model="search_data">
        <el-form-item label="排序">
          <el-select clearable filterable v-model="search_data.order" placeholder="请选择" @change="get_data">
            <el-option v-for="(v, k) in order_type" :key="k" :label="v" :value="k"> </el-option>
          </el-select>
        </el-form-item>

        <el-form-item prop="report_date" label="日期">
          <el-date-picker
            @change="get_data"
            v-model="search_data.report_date"
            :type="search_data.report_type == 'DAILY' ? 'date' : 'month'"
            value-format="yyyy-MM-dd"
            :picker-options="pickerOptions"
            placeholder="日期"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>

      <el-table :data="items" style="width: 100%">
        <el-table-column label="日期" prop="report_date"> </el-table-column>

        <el-table-column prop="market" label="市场"> </el-table-column>

        <el-table-column prop="position_amount_usd" label="持仓市值"> </el-table-column>

        <el-table-column prop="deal_user_count" label="成交人数"> </el-table-column>

        <el-table-column prop="deal_amount" :formatter="row => $formatNumber(row.deal_amount, 2)" label="成交量（USD）"> </el-table-column>

        <el-table-column prop="deal_usd_percent" :formatter="row => $formatPercent(row.deal_usd_percent)" label="成交额占比">
        </el-table-column>

        <el-table-column prop="deal_count" label="成交笔数"> </el-table-column>

        <el-table-column prop="single_deal_amount" label="单笔金额">
        </el-table-column>

        <el-table-column prop="buy_count" label="主动买入笔数"> </el-table-column>

        <el-table-column prop="buy_amount" :formatter="row => $formatNumber(row.buy_amount, 2)" label="主动买入量（USD）">
        </el-table-column>

        <el-table-column prop="sell_count" label="主动卖出笔数"> </el-table-column>

        <el-table-column prop="sell_amount" :formatter="row => $formatNumber(row.sell_amount, 2)" label="主动卖出量（USD）">
        </el-table-column>

        <el-table-column prop="real_leverage" label="实际杠杆倍数"></el-table-column>

        <el-table-column prop="fee_usd" label="手续费市值(USD)">
        </el-table-column>

      </el-table>

      <el-pagination
        :current-page.sync="search_data.page"
        :page-size.sync="search_data.limit"
        :page-count.sync="page_count"
        :total="total"
        @size-change="get_data"
        @current-change="get_data"
        :page-sizes="[100, 500, 1000]"
        :hide-on-single-page="false"
        layout="total, sizes, prev, pager, next, jumper"
      >
      </el-pagination>

      <el-backtop></el-backtop>
    </el-main>
  </el-container>
</template>

<script>
const binding_url = '/api/perpetual/report-market-detail';

export default {
  watchQuery: ['report_type', 'report_date', 'order', 'page', 'limit'],
  key: to => to.fullPath,
  asyncData({ app, query }) {
    let new_query = _.clone(query);
    if (!new_query.hasOwnProperty('report_type')) {
      new_query.report_type = 'DAILY';
    }
    if (!new_query.hasOwnProperty('limit')) {
      new_query.limit = 100;
    }
    if (!new_query.hasOwnProperty('page')) {
      new_query.page = 1;
    }
    if (!new_query.hasOwnProperty('order')) {
      new_query.order = 'deal_amount';
    }
    return app.$axios['get'](binding_url, { params: new_query })
      .then(res => {
        if (res.data.code === 0) {
          let data = res.data.data;
          return {
            items: data.data,
            total: data.total,
            page_count: data.total_page,
            search_data: _.clone(new_query),
          };
        } else {
          this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
          return {};
        }
      })
      .catch(e => {});
  },
  methods: {
    get_data(reset = true) {
      if (reset === true) {
        this.search_data.page = 1;
      }
      this.$router.replace({ path: this.$route.path, query: this.search_data });
    },
  },
  data() {
    return {
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      order_type: {
        deal_amount: '成交量',
        deal_user_count: '成交人数',
        deal_count: '成交笔数',
      },
    };
  },
};
</script>
