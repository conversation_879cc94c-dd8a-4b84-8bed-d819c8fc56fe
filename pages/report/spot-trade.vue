<template>
  <el-container>
    <el-main>
      <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
        现货-全站收入报表
        <el-tooltip placement="right" :open-delay="500">
          <div slot="content">
            <p>全站成交额(USD)：现货成交量市值，单向统计</p>
            <p>总手续费收入(USD)：现货手续费收入市值，双向统计</p>
            <p>成交人数：现货成交账户数，双向统计</p>
            <p>成交笔数：周期内所有现货市场的成交笔数，单向统计。</p>
            <p>单笔金额：周期内所有现货市场的全站成交额/成交笔数。</p>
            <p>新增交易用户：周期内新增的交易用户</p>
            <p>API用户数：周期内，使用API成交用户数（不排除子账号）</p>
            <p>平均费率：周期内总手续费（单边）/周期内全站成交额（单边）*100%</p>
            <p>做市商平均费率：周期内外部做市商手续费/周期内外部做市商成交市值*100%</p>
            <p>普通用户平均费率：周期内普通用户手续费/周期内普通用户成交市值*100%，普通用户为除内外部做市商的其他用户类型</p>
            <p>普通手续费比例=普通用户手续费市值/总手续费收入市值*100%（普通用户为除内外部做市商的其他用户类型）</p>
            <p>普通成交比例=普通用户成交市值/全站成交额市值*100%（普通用户为除内外部做市商的其他用户类型）</p>
            <p>CET收入比例=CET手续费收入市值/总手续费收入市值*100%</p>
          </div>
          <i class="el-icon-question"></i>
        </el-tooltip>
      </h2>
      <el-tabs v-model="search_data.report_type" type="card" @tab-click="get_data(true)">
        <el-tab-pane label="日报" name="DAILY"></el-tab-pane>
        <el-tab-pane label=月报 name="MONTHLY"></el-tab-pane>
      </el-tabs>
      <template>
        <el-form :inline="true" :model="search_data">
          <el-form-item prop="start_date" label="开始时间">
            <el-date-picker
              @change="get_data"
              v-model="search_data.start_date"
              type="date"
              value-format="yyyy-MM-dd"
              :picker-options="pickerOptions"
              placeholder="时间">
            </el-date-picker>
          </el-form-item>
          <el-form-item prop="end_date" label="结束时间">
            <el-date-picker
              @change="get_data"
              v-model="search_data.end_date"
              type="date"
              value-format="yyyy-MM-dd"
              :picker-options="pickerOptions"
              placeholder="时间">
            </el-date-picker>
          </el-form-item>

          <el-form-item>
            <el-tooltip
              content="导出数据"
              placement="right"
              :open-delay="500"
              :hide-after="2000"
            >
              <el-button
                icon="el-icon-download"
                circle
                @click="download"
              ></el-button>
            </el-tooltip>
          </el-form-item>

        </el-form>
      </template>

      <FixedTable
        :data="items"
        style="width: 100%" @header-click="show_series_dialog" ref="table">

        <el-table-column
          label="日期"
          prop="report_date">
          <template slot-scope="scope">
            <el-link :href="`/report/spot-coin-detail?report_date=${scope.row.report_date}&report_type=${search_data.report_type}`"
                     type="primary"
                     target="_blank"
                     :underline="false">
              {{ scope.row.report_date }}
            </el-link>
          </template>
        </el-table-column>

        <el-table-column
          label="市场数"
          prop="market_count">
        </el-table-column>

        <el-table-column
          prop="trade_usd"
          label="全站成交额(USD)" :render-header="renderHeader" column-key="全站成交额(USD)：全站成交额(USD)">
        </el-table-column>

        <el-table-column
          prop="fee_usd"
          label="手续费收入(USD)" :render-header="renderHeader" column-key="手续费收入(USD)：手续费收入(USD)">
        </el-table-column>

        <el-table-column
          prop="deal_user_count"
          label="成交人数" :render-header="renderHeader" column-key="成交人数：成交人数">
        </el-table-column>

        <el-table-column
          prop="deal_count"
          label="成交笔数" :render-header="renderHeader" column-key="笔数：成交笔数">
        </el-table-column>

        <el-table-column
          prop="single_deal_amount"
          label="单笔金额" :render-header="renderHeader" column-key="单笔金额：单笔金额">
        </el-table-column>

        <el-table-column
          prop="increase_trade_user_count"
          label="新增交易人数" :render-header="renderHeader" column-key="新增交易人数：新增交易人数">
        </el-table-column>

        <el-table-column
          prop="api_user_count"
          label="API用户数" :render-header="renderHeader" column-key="API用户数：API用户数">
        </el-table-column>

        <el-table-column
          prop="avg_fee_rate"
          label="平均费率" :render-header="renderHeader" column-key="平均费率：平均费率">
        </el-table-column>

        <el-table-column
          prop="avg_mm_fee_rate"
          label="做市商平均费率" :render-header="renderHeader" column-key="做市商平均费率：做市商平均费率">
        </el-table-column>

        <el-table-column
          prop="avg_normal_fee_rate"
          label="普通用户平均费率" :render-header="renderHeader" column-key="普通用户平均费率：普通用户平均费率">
        </el-table-column>

        <el-table-column
          prop="normal_fee_rate"
          label="普通手续费比例" :render-header="renderHeader" column-key="普通手续费比例：普通手续费比例">
        </el-table-column>

        <el-table-column
          prop="normal_trade_rate"
          label="普通成交比例" :render-header="renderHeader" column-key="普通成交比例：普通成交比例">
        </el-table-column>

        <el-table-column
          prop="cet_fee_rate"
          label="CET收入比例" :render-header="renderHeader" column-key="CET收入比例：CET收入比例">
        </el-table-column>
        <el-table-column
          prop="cet_user_rate"
          :formatter="row => row.cet_user_rate"
          label="CET用户比例" :render-header="renderHeader" column-key="CET用户比例：CET用户比例">
        </el-table-column>

      </FixedTable>

      <el-pagination :current-page.sync="search_data.page"
                     :page-size.sync="search_data.limit"
                     :page-count.sync="page_count"
                     :total="total"
                     @current-change="get_data"
                     :page-sizes="[100]"
                     :hide-on-single-page="true"
                     layout="total, sizes, prev, pager, next, jumper"
      >
      </el-pagination>
      <el-dialog :visible.sync="show_dialog" width="80%" :before-close="handleClose">
        <Series ref="series" :render_info="render_info" v-if="show_dialog === true" @getChildValue="setChildValue"></Series>
      </el-dialog>

      <el-backtop></el-backtop>
    </el-main>
  </el-container>
</template>


<script>
  import moment from "moment";
  import Series from '~/components/Series'
  import FixedTable from '~/components/FixedTableRender.vue';
  import series from '~/plugins/report/series'
  import thousandSeperatorMixins from "~/plugins/instances/report";


  const binding_url = "/api/report/spot/deals";
  const exclude_render_columns = ['report_date', ];

  export default {
    components: {
      Series,
      FixedTable,
    },
    mixins: [series, thousandSeperatorMixins],
    watchQuery: ['report_type', 'start_date', 'end_date', 'page', 'limit'],
    key: to => to.fullPath,
    asyncData({app, query}) {
      query.limit = query.limit ? query.limit : 100
      query.page = query.page ? query.page : 1
      let new_query = _.clone(query);
      if (!new_query.hasOwnProperty("report_type")) {
        new_query.report_type = 'DAILY';
      }
      return app.$axios["get"](binding_url, {params: new_query})
        .then((res) => {
          if (res.data.code === 0) {
            let data = res.data.data;
            return {
              items: data.records,
              total: data.total,
              page_count: data.total_page,
              search_data: _.clone(new_query),
            }
          } else {
            this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
            return {}
          }
        })
        .catch((e) => {
        })
    },
    methods: {
      show_series_dialog(column) {
        if (exclude_render_columns.includes(column.property)) {
          return
        }
        this.show_dialog = true;
        this.set_render_info(column, {
          exclude_columns: exclude_render_columns,
          binding_url: binding_url,
          resp_key: 'records',
          report_type_lower: false,
        });
      },

      get_data(reset = true) {
        if (reset === true) {
          this.search_data.page = 1;
        }
        this.$router.replace({path: this.$route.path, query: this.search_data});
      },

      download() {
      let filename;
        if (this.search_data.report_type && this.search_data.report_type.toLowerCase() === 'monthly') {
          filename = 'spot_trade_monthly.xlsx';
        } else {
          filename = 'spot_trade_daily.xlsx';
        }
        let params =  {...this.search_data, export: true}
        this.$download_from_url(binding_url, filename, params)
      },
    },
    data() {
      return {
        page_count: 0,
        search_data: {
          report_type: 'DAILY',
          start_date: null,
          end_date: null,
          page: 1,
          limit: 100,
        },
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() > Date.now();
          }
        }
      }
    },
  }

</script>
