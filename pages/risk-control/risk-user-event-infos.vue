<template>
  <div class="table-data">
    <h2 style="font-family: 'Microsoft YaHei',<PERSON><PERSON>,sans-serif;">
      系统风控事件列表
    </h2>

    <el-form :inline="true">

      <el-form-item label="来源" v-if="filters.reason" >
        <el-select clearable filterable v-model="filters.source" placeholder="<ALL>" @change="refresh">
          <el-option v-for="name in source_list"
                     :key="name"
                     :label="name"
                     :value="name">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="类型">
        <el-select clearable v-model="filters.reason" placeholder="<ALL>" @change="refresh">
          <el-option v-for="(val, key) in reasons"
                     :key="key"
                     :label="val"
                     :value="key">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item prop="start" label="开始时间">
        <el-date-picker
          @change="refresh"
          v-model="filter_date_range[0]"
          type="datetime"
          value-format="timestamp"
          placeholder="开始时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item prop="end" label="结束时间">
        <el-date-picker
          @change="refresh"
          v-model="filter_date_range[1]"
          type="datetime"
          value-format="timestamp"
          placeholder="结束时间">
        </el-date-picker>
      </el-form-item>

      <el-form-item label="状态">
        <el-select clearable v-model="filters.status" placeholder="<ALL>" @change="refresh">
          <el-option v-for="(val, key) in statuses"
                     :key="key"
                     :label="val"
                     :value="key">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="refresh">查询</el-button>
      </el-form-item>

      <el-form-item>
        <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
          <el-button icon="el-icon-refresh-left"
                     circle
                     @click="refresh"></el-button>
        </el-tooltip>
      </el-form-item>

      <el-form-item>
        <el-tooltip content="下载" placement="right" :open-delay="500" :hide-after="2000">
          <el-button icon="el-icon-download"
                     circle type="success"
                     @click="download"></el-button>
        </el-tooltip>
      </el-form-item>

    </el-form>

    <el-row> 统计：共 {{ total }} 条 </el-row>

    <el-container>
      <el-table v-if="['PERPETUAL_LIQUIDATION', 'MARGIN_LIQUIDATION'].includes(filters.reason) && filters.market" :data="statistics_table" :show-header="false" stripe>
        <el-table-column prop="name" width="200px"></el-table-column>
        <el-table-column prop="value"></el-table-column>
      </el-table>
    </el-container>
    <br/>

    <el-table :data="items"
              v-loading="loading"
              stripe>

      <el-table-column
        label="序号"
        prop="id"
      >
      </el-table-column>

      <el-table-column
        prop="source"
        label="来源">
      </el-table-column>

      <el-table-column label="风控类型" prop="reason_type_str"></el-table-column>
      <el-table-column label="风控详情">
        <template slot-scope="scope">
          <div style="white-space: pre-wrap;"> {{ scope.row.reason_detail }}</div>
        </template>

      </el-table-column>
      <el-table-column label="触发时间" prop="trigger_time"></el-table-column>
      <el-table-column label="审核时间" prop="operate_at" :formatter="row => $formatDate(row.operate_at)"></el-table-column>
      <el-table-column label="恢复时间" prop="resume_time"></el-table-column>
      <el-table-column label="审核类型">
        <template slot-scope="scope">
          {{"人工审核"}}
        </template>
      </el-table-column>
      <el-table-column label="备注" prop="remark"></el-table-column>
      <el-table-column prop="relate_user_list" label="关联用户">
        <template slot-scope="scope">
            <template v-if="['margin_liquidation', 'perpetual_liquidation'].includes(scope.row.reason)">
            <el-row>保险金垫付前10:</el-row>
            <template v-for="user_id in scope.row.extra.user_list_rank">
              <el-link
                class="no-underline"
                :underline="false"
                type="primary"
                :href="'/users/user-details?id=' + user_id"
              >
                {{ user_id }},
              </el-link>
            </template>
          </template>

            <template v-if="scope.row.reason === 'pledge_liquidation_beyond_threshold'">
            <el-row>保险金垫付前5:</el-row>
            <template v-for="user_id in scope.row.extra.user_list_rank">
              <el-link
                class="no-underline"
                :underline="false"
                type="primary"
                :href="'/users/user-details?id=' + user_id"
              >
                {{ user_id }},
              </el-link>
            </template>
          </template>

          <template v-if="['market_volatility', 'perpetual_market_volatility'].includes(scope.row.reason)">
            <el-row>净买入量前5:</el-row>
            <template v-for="user_id in scope.row.extra.buy_list_rank">
              <el-link
                class="no-underline"
                :underline="false"
                type="primary"
                :href="'/users/user-details?id=' + user_id"
              >
                {{ user_id }},
              </el-link>
            </template>
            <el-row>净卖出量前5:</el-row>
            <template v-for="user_id in scope.row.extra.sell_list_rank">
              <el-link
                class="no-underline"
                :underline="false"
                type="primary"
                :href="'/users/user-details?id=' + user_id"
              >
                {{ user_id }},
              </el-link>
            </template>
          </template>

          <template v-if="scope.row.reason === 'viabtc_trans_beyond_threshold'">
            <el-row>入账市值前10:</el-row>
            <template v-for="user_id in scope.row.extra.user_list_rank">
              <el-link
                class="no-underline"
                :underline="false"
                type="primary"
                :href="'/users/user-details?id=' + user_id"
              >
                {{ user_id }},
              </el-link>
            </template>
          </template>

          <template v-if="scope.row.reason === 'withdrawal_fuse'">
            <el-row>提现市值前10:</el-row>
            <template v-for="(value, user_id) in scope.row.extra.usd_top_10_data">
              <el-link
                class="no-underline"
                :underline="false"
                type="primary"
                :href="'/users/user-details?id=' + user_id"
              >
                {{ user_id }}: {{value}} USD
              </el-link>
            </template>

            <el-row>提现笔数前10:</el-row>
            <template v-for="(value, user_id) in scope.row.extra.count_top_10_data">
              <el-link
                class="no-underline"
                :underline="false"
                type="primary"
                :href="'/users/user-details?id=' + user_id"
              >
                {{ user_id }}: {{value}} 笔
              </el-link>
            </template>
          </template>

          <template v-if="scope.row.reason === 'deposits_fuse'">
            <el-row>充值市值前10:</el-row>
            <template v-for="(value, user_id) in scope.row.extra.top10_usd_data">
              <el-link
                class="no-underline"
                :underline="false"
                type="primary"
                :href="'/users/user-details?id=' + user_id"
              >
                {{ user_id }}: {{value}} USD
              </el-link>
            </template>
          </template>

          <template v-if="scope.row.reason==='signed_withdrawals_cancel'">
            <el-row>提现市值前10:</el-row>
            <template v-for="(value, user_id) in scope.row.extra.top10_usd_data">
              <el-link
                class="no-underline"
                :underline="false"
                type="primary"
                :href="'/users/user-details?id=' + user_id"
              >
                {{ user_id }}: {{value}} USD
              </el-link>
            </template>
          </template>

          <template v-if="['accumulated_asset_deposit', 'accumulated_asset_deposit_proportion'].includes(scope.row.reason)">
            <el-row>充值市值前10:</el-row>
            <template v-for="user_id in scope.row.extra.user_list_rank">
              <el-link
                class="no-underline"
                :underline="false"
                type="primary"
                :href="'/users/user-details?id=' + user_id"
              >
                {{ user_id }},
              </el-link>
             </template>
          </template>
          <template v-if="scope.row.reason === 'accumulated_asset_withdrawal'">
            <el-row>提现市值前10:</el-row>
            <template v-for="user_id in scope.row.extra.user_list_rank">
              <el-link
                class="no-underline"
                :underline="false"
                type="primary"
                :href="'/users/user-details?id=' + user_id"
              >
                {{ user_id }},
              </el-link>
            </template>
          </template>

          <template v-if="['bus_online_coin_market_volatility', 'bus_online_coin_deposit_volatility'].includes(scope.row.reason)">
            <el-row>周期内{{
                scope.row.reason === 'bus_online_coin_market_volatility' ? '卖出量' : '充值数量'
              }}前10:
            </el-row>
            <template v-for="user_id in scope.row.extra.user_list_rank">
              <el-link
                class="no-underline"
                :underline="false"
                type="primary"
                :href="'/users/user-details?id=' + user_id"
              >
                {{ user_id }},
              </el-link>
            </template>
          </template>

          <template v-if="scope.row.reason === 'abnormal_issuance'">
            <el-row>累计充值市值前10:</el-row>
            <template v-for="user_value in scope.row.extra.user_list_rank">
              <el-link
                class="no-underline"
                :underline="false"
                type="primary"
                :href="'/users/user-details?id=' + user_value.user_id"
              >
                {{ user_value.user_id }},
              </el-link>
             </template>
          </template>

          <template v-if="scope.row.reason === 'wallet_small_amount_withdrawal_fuse'">
            <el-row>最近24H小额提现市值前10用户ID:</el-row>
            <template v-for="user_id in scope.row.extra.top_user_ids">
              <el-link
                class="no-underline"
                :underline="false"
                type="primary"
                :href="'/users/user-details?id=' + user_id"
              >
                {{ user_id }},
              </el-link>
             </template>
          </template>

          <template v-if="scope.row.reason === 'asset_small_amount_withdrawal_fuse'">
            <el-row>最近24H {{ scope.row.extra.asset }} 小额提现市值前10用户ID:</el-row>
            <template v-for="user_id in scope.row.extra.top_user_ids">
              <el-link
                class="no-underline"
                :underline="false"
                type="primary"
                :href="'/users/user-details?id=' + user_id"
              >
                {{ user_id }},
              </el-link>
             </template>
          </template>

        </template>

      </el-table-column>

      <el-table-column label="状态" prop="status" :formatter="row => statuses[row.status]"></el-table-column>

      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-tooltip content="修改备注" placement="right" :open-delay="500" :hide-after="2000">
            <el-button size="mini" type="primary" icon="el-icon-edit" circle @click="handle_edit(scope.row)">
            </el-button>
          </el-tooltip>
          <span v-if="allowed_audit_reasons.includes(scope.row.reason)">
            <span v-if="!['PASSED', 'REJECTED'].includes(scope.row.status)">
              <el-tooltip content="审核" placement="right" :open-delay="500" :hide-after="2000">
                <el-button size="mini" type="warning" icon="el-icon-check" circle @click="handleAudit(scope.row)">
                </el-button>
              </el-tooltip>
            </span>
          </span>
        </template>
      </el-table-column>

    </el-table>

    <el-dialog
      :title="'修改备注信息'"
      :visible.sync="dialog_show"
      :before-close="handleClose"
      width="70%">

      <el-form :model="submit_data" ref="submit_data" label-width="100px">

        <el-form-item label="备注" required>
          <el-input v-model="submit_data.remark"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
      <el-button type="danger" @click="dialog_show = false">取消</el-button>
      <el-button type="primary" @click="handle_remark">确定</el-button>
      </span>
    </el-dialog>

    <el-dialog
      :title="'审核'"
      :visible.sync="dialogAudit.show"
      :before-close="handleClose"
      width="40%">

      <el-form :model="dialogAudit.data" ref="dialog_data" label-width="100px">
        <el-form-item label="审核结果" required>
          <el-radio-group v-model="dialogAudit.data.status">
            <el-radio label="PASSED">通过</el-radio>
            <el-radio label="REJECTED">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="auditRow">确定</el-button>
        <el-button type="danger" @click="dialogAudit.show=false">取消</el-button>
      </span>
    </el-dialog>

    <el-pagination :current-page.sync="filters.page"
                   :page-size.sync="filters.limit"
                   :page-sizes="[50, 100, 200, 500]"
                   :total="total"
                   @size-change="refresh"
                   @current-change="refresh(true)"
                   :hide-on-single-page="true"
                   layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <br>
  </div>
</template>

<script>
  import axios from 'axios'
  import Vue from 'vue';

  export default {
    methods: {
      download() {
        let params = {...this.filters, export: 1};
        delete params['page'];
        delete params['limit'];
        this.$axios.get('/api/risk-control/risk-user-event-record', {params: params, responseType: 'blob'}).then(res => {
          const content_type = res.headers['content-type'];
          const url = window.URL.createObjectURL(new Blob([res.data], content_type? {type: content_type} : {}));
          const a = document.createElement(('a'));
          a.href = url;
          document.body.appendChild(a);
          const content_disposition = res.headers['content-disposition'];
          a.download = content_disposition? content_disposition.split('filename=')[1] : 'temp.xlsx';
          a.click();
          window.URL.revokeObjectURL(url);
        });
      },

      refresh(keep) {
        if (keep !== true) {
          this.filters.page = 1;
          // this.filters.reason = null;
        }
        this.loading = true;
        this.$axios.get(
          '/api/risk-control/risk-user-event-record',
          {params: this.filters}
        ).then(
          res => {
            if (res.data.code == 0) {
              let data = res.data.data;
              this.items = data.items;
              this.source_list = data.extra.source_list;
              this.reasons = data.extra.reasons;
              this.statuses = data.extra.statuses;
              this.allowed_audit_reasons = data.extra.allowed_audit_reasons;
              this.extra = data.extra;
              this.total = data.total;
            } else {
              this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
            }
            this.loading = false;
          }
        ).catch(err => {
          this.$message.error(`刷新失败! (${err})`);
          this.loading = false;
        });
      },
      handle_edit(row) {
        this.dialog_show = true;
        this.submit_data.remark = row.remark;
        this.row_id = row.id;
      },
      handleAudit(row) {
        this.dialogAudit.show = true;
        this.dialogAudit.data.id = row.id;
        this.dialogAudit.row = row;
      },
      auditRow() {
        let patch_url = '/api/risk-control/risk-user-event-record/' + this.dialogAudit.data.id + '/audit'
        this.$axios.patch(patch_url, {...this.dialogAudit.data}).then(
          res => {
            if (res && res.data.code === 0) {
              this.dialogAudit.show = false;
              if (this.dialogAudit.data.status === 'PASSED') {
                this.confirmToCloseCfg(this.dialogAudit.row);
              }
              this.refresh();
            } else {
              this.items = [];
              this.total = 0;
              this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
            }
          }
        );
      },
      confirmToCloseCfg(row) {
        let url = `/system/asset-configs?asset=${row.source}`;
        this.confirmToNewPage('已开启防干扰，该币种周期内不再被该策略风控，请前往币种设置开启该币种充值。', url);
      },
      confirmToNewPage(msg, url) {
        this.$confirm(msg)
          .then(_ => {
            window.open(url, '_blank');
            done();
          })
          .catch(_ => {
          });
      },
      handleClose(done) {
        this.dialog_show = false;
        this.dialogAudit.show = false;
        done();
      },
      handle_remark() {
        let patch_url = '/api/risk-control/risk-user-event-record/' + this.row_id + '/remark'
        this.$axios.patch(patch_url, {...this.submit_data}).then(
          res => {
            if (res && res.data.code === 0) {
              this.dialog_show = false;
              this.refresh();
            } else {
              this.items = [];
              this.total = 0;
              this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
            }
          }
        );
      },
    },
    mounted() {
      this.refresh();
    },
    computed: {
      statistics_table() {
        return [
          {name: "影响总用户数", value: this.extra.total_user_count},
          {name: "穿仓总金额", value: this.$formatNumber(this.extra.total_amount, 2) + 'USD'},
        ]
      }
    },
    data() {
      return {
        loading: null,
        dialog_show: false,
        dialogAudit: {
          show: false,
          data: {
            id: null,
            status: null,
          }
        },
        filters: {
          page: 1,
          limit: 50,
          reason: null,
          status: null,
          start_time: null,
          end_time: null,
        },
        filter_date_range: [null, null],
        items: [],
        source_list: [],
        total: 0,
        reasons: {},
        statuses: {},
        allowed_audit_reasons: [],
        submit_data: {
          remark: ''
        },
        extra: {
          total_amount: null,
          total_user_count: null,
        },
      }
    },
    watch: {
      filter_date_range: function (val) {
        this.filters.start_time = val && val[0] ? val[0] / 1000 : null;
        this.filters.end_time = val && val[1] ? val[1] / 1000 : null;
      }
    }
  }
</script>
