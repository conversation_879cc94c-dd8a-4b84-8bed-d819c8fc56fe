<template>
  <div class="table-data">
    <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
      风控账户审核
    </h2>

    <h3 v-if="!has_data">
      当前无待审核的风控账户
    </h3>
    <div v-else>
      <h3>用户信息</h3>
      <el-table :data="user_info_table" :show-header="false" stripe>
        <el-table-column prop="name" width="300px">
        </el-table-column>
        <el-table-column prop="value">
          <template slot-scope="scope">
            <span v-if="scope.row.name == '注册时间'">
              {{ $formatDate(scope.row.value) }}
            </span>
            <el-link v-else-if="scope.row.name == '用户ID'"
              type="primary"
              :href="`/users/user-details?id=${scope.row.value}`"
              :underline="false"
              target="_blank">
              {{ scope.row.value }}
            </el-link >
            <el-link v-else-if="scope.row.name == '邮箱'"
              type="primary"
              :href="`/users/user-details?id=${user_info.id}`"
              :underline="false"
              target="_blank">
              {{ scope.row.value }}
            </el-link >
            <span v-else>
              {{ scope.row.value }}
            </span>
          </template>
        </el-table-column>
      </el-table>
      <br>
      <h3>风控信息</h3>
      <el-table :data="risk_info_table" :show-header="false" stripe>
        <el-table-column prop="name" width="300px">
          <template slot-scope="scope">
            <div v-if="scope.row.name == '风控详情' && (risk_info.reason == 'abnormal_profit' || risk_info.reason == 'period_abnormal_profit')">
              <span>{{ scope.row.name }}</span>
              <el-tooltip placement="right" :open-delay="500">
                <div slot="content">
                  <p>对账公式：</p>
                  <p>盈利 = 本次剩余价值 + 提现 - (上次剩余价值 + 充值) - (资产变更 + 系统赠送 + 红包收支)</p>
                  <p>百分比 = 盈利 / (上次剩余价值 + 充值)</p>
                </div>
                <i class="el-icon-question"></i>
              </el-tooltip>
            </div>
            <span v-else>
              {{ scope.row.name }}
            </span>
          </template>
        </el-table-column>

        <el-table-column prop="value">
          <template slot-scope="scope">
            <div v-if="scope.row.name == '审核'">
              <div v-if="scope.row.value == 'audit_required'">
                <el-button type="text" style="color: red" @click="show_dialog('audit_rejected')">审核不通过</el-button>
                <el-button type="text" @click="show_dialog('audited')">审核通过</el-button>
              </div>
              <span v-else>
                {{ statuses[scope.row.value] }}
              </span>
            </div>
            <span v-else-if="scope.row.name == '审核时间' && scope.row.value">
              {{ $formatDate(scope.row.value) }}
            </span>
            <el-link v-else-if="scope.row.name == '审核人' && scope.row.value"
              type="primary"
              :href="`/users/user-details?id=${risk_info.audited_by}`"
              :underline="false"
              target="_blank">
              {{ scope.row.value }}
            </el-link >
            <span v-else>
              {{ scope.row.value }}
            </span>

          </template>
        </el-table-column>
      </el-table>

      <br>
      <el-tabs type="card" class="table-data" v-model="cur_tab">
        <el-tab-pane label="现货账户" name="spot_info">
          <SpotInfo v-if="cur_tab == 'spot_info'" :user="user"></SpotInfo>
        </el-tab-pane>
        <el-tab-pane label="充值信息" name="deposit_info">
          <DepositInfo v-if="cur_tab == 'deposit_info'" :user="user"></DepositInfo>
        </el-tab-pane>
        <el-tab-pane label="提现信息" name="withdrawal_info">
          <WithdrawalInfo v-if="cur_tab == 'withdrawal_info'" :user="user"></WithdrawalInfo>
        </el-tab-pane>
        <el-tab-pane label="币币订单" name="coin_orders_info">
          <CoinOrdersInfo v-if="cur_tab == 'coin_orders_info'" :user="user"></CoinOrdersInfo>
        </el-tab-pane>
        <el-tab-pane label="合约订单" name="perpetual_orders">
          <PerpetualOrders v-if="cur_tab == 'perpetual_orders'" :user="user"></PerpetualOrders>
        </el-tab-pane>
        <el-tab-pane label="资金流水" name="balance_history_info">
          <BalanceHistoryInfo v-if="cur_tab == 'balance_history_info'" :user="user"></BalanceHistoryInfo>
        </el-tab-pane>
        <el-tab-pane label="登录记录" name="login_history">
          <LoginHistory v-if="cur_tab == 'login_history'" :user="user"></LoginHistory>
        </el-tab-pane>
        <el-tab-pane label="操作记录" name="operation_log">
          <OperationLog v-if="cur_tab == 'operation_log'" :user="user"></OperationLog>
        </el-tab-pane>
      </el-tabs>

      <el-dialog
        title="审核"
        :visible.sync="dialog.visible"
        width="30%">
        <el-form label-position="left" label-width="60px">
          <el-form-item label="结果">
            <el-select v-model="dialog.option" placeholder="请选择" style="width: 300px">
              <el-option
                v-for="(val, key) in dialog.options"
                :key="key"
                :label="val"
                :value="key">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="防干扰时间" v-if="dialog.option === 'audited' && enabled_reasons.includes(risk_info.reason)">
            <el-radio-group v-model="dialog.prevent_seconds">
              <el-radio
                v-for="(name, label) in prevent_options"
                :label="name">
                {{ label }}
              </el-radio>
            </el-radio-group>
            <el-tooltip class="item" effect="dark" content="选择了具体的时间后，用户在该时间范围内不会被该类型的风控限制" placement="right">
              <el-button size="mini" icon="el-icon-info" circle></el-button>
            </el-tooltip>
          </el-form-item>

          <el-form-item label="备注">
            <el-input v-model="dialog.remark" placeholder="请输入备注" style="width: 300px"></el-input>
          </el-form-item>
        </el-form>

        <span slot="footer" class="dialog-footer">
          <el-button @click="dialog.visible = false">取 消</el-button>
          <el-button type="primary" @click="dialog.visible = false; audit()">确 定</el-button>
        </span>
      </el-dialog>

    </div>
  </div>
</template>

<script>
  import axios from 'axios'
  import Vue from 'vue';
  import moment from "moment";

  import SpotInfo from '~/components/user/SpotInfo'
  import PerpetualOrders from '~/components/user/PerpetualOrders'
  import CoinOrdersInfo from '~/components/user/CoinOrdersInfo'
  import BalanceHistoryInfo from '~/components/user/BalanceHistoryInfo'
  import LoginHistory from '~/components/user/LoginHistory'
  import OperationLog from '~/components/user/OperationLog'
  import DepositInfo from '~/components/user/DepositInfo'
  import WithdrawalInfo from '~/components/user/WithdrawalInfo'

  export default {
    components: {
      SpotInfo,
      PerpetualOrders,
      CoinOrdersInfo,
      BalanceHistoryInfo,
      LoginHistory,
      OperationLog,
      DepositInfo,
      WithdrawalInfo
    },
    methods: {
      refresh() {
        this.$axios.get(
          `/api/risk-control/risk-infos/${this.next}`
        ).then(
          res => {
            if (res.data.code == 0) {
              if (Object.keys(res.data.data).length == 0) {
                this.has_data = false;
                this.user = null;
              } else {
                this.has_data = true;
                let data = res.data.data;
                this.user_info = data.user_info;
                this.risk_info = data.risk_info;
                this.kyc_info = data.kyc_info;
                this.reasons = data.reasons;
                this.statuses = data.statuses;
                this.permissions = data.permissions;
                this.prevent_options = data.prevent_options;
                this.enabled_reasons = data.enabled_reasons;
                this.dialog.options = data.audit_options;
                this.next = data.next;
                this.set_table_data();
                this.user = this.user_info;
              }
            } else {
              this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
            }
          }
        ).catch(err => {
          this.$message.error(`请求失败! (${err})`);
        });
      },
      set_table_data () {
        this.user_info_table = [
          {'name': '注册时间', 'value': this.user_info.created_at},
          {'name': '注册IP', 'value': this.user_info.registration_ip},
          {'name': '所在地区', 'value': this.user_info.registration_location},
          {'name': '用户ID', 'value': this.user_info.id},
          {'name': '邮箱', 'value': this.user_info.email},
          {'name': '手机号', 'value': this.user_info.mobile},
          {'name': '用户类型', 'value': this.user_info.user_type},
          {'name': '实名认证', 'value': this.kyc_info ? `姓名：${this.kyc_info.full_name}　　　　国籍：${this.kyc_info.country}　　　　证件号：${this.kyc_info.id_number}` : '未认证'}
        ];

        this.risk_info_table = [
          {'name': '风控类型', 'value': this.reasons[this.risk_info.reason]},
          {'name': '风控详情', 'value': this.risk_info.detail},
          {'name': '权限禁止', 'value': this.risk_info.permissions.length !== 0 ? this.risk_info.permissions.map(i => this.permissions[i]).join(): '无限制'},
          {'name': '来源', 'value': this.risk_info.source},
          {'name': '备注', 'value': this.risk_info.remark},
          {'name': '审核', 'value': this.risk_info.status},
          {'name': '审核时间', 'value': this.risk_info.audited_at},
          {'name': '审核人', 'value': this.risk_info.audited_by_name}
        ]
      },
      show_dialog(option) {
          this.dialog.option = option;
          this.dialog.remark = this.risk_info.remark;
          this.dialog.visible = true;
          this.dialog.prevent_seconds = null;
      },
      audit () {
          this.$axios.post('/api/risk-control/risk-infos', {
              ids: [this.risk_info.id],
              status: this.dialog.option,
              remark: this.dialog.remark,
              prevent_seconds: this.dialog.prevent_seconds
          }).then(res => {
              if (res.data.code == 0) {
                this.$message.success('审核成功');
                this.$router.replace({query: {id: this.next}});
                this.refresh();
              } else {
                  this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
              }
          }).catch(err => {
              this.$message.error(`审核失败! (${err})`);
          });
      }
    },
    mounted() {
      this.next = this.$route.query.id || 0;
      this.refresh();
    },
    data() {
      return {
        has_data: false,
        user_info: {},
        risk_info: {},
        kyc_info: {},
        reasons: {},
        statuses: {},
        permissions: {},
        next: 0,
        dialog: {
            visible: false,
            options: {},
            option: null,
            prevent_seconds: null,
            remark: null
        },
        prevent_options: {},
        enabled_reasons: [],
        user_info_table: [],
        risk_info_table: [],
        cur_tab: null,
        user: null
      }
    }
  }
</script>
