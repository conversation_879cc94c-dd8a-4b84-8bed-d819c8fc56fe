<template>
  <div class="table-data">
    <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
      用户风控审核
    </h2>

    <el-form :inline="true">
      <el-form-item label="类型">
        <el-select clearable v-model="filters.reason" placeholder="<ALL>">
          <el-option v-for="(val, key) in reasons"
                     :key="key"
                     :label="val"
                     :value="key">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="来源" v-if="sources && sources.length > 0">
        <el-select clearable filterable v-model="filters.source" placeholder="<ALL>" @change="refresh">
          <el-option v-for="val in sources"
                     :key="val"
                     :label="val"
                     :value="val">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="权限" style="padding-right: 5px;">
        <el-tooltip class="item" effect="dark" placement="top">
          <div slot="content">
            <p>需要同时筛选类型</p>
            <p></p>
            <p>禁止提现：禁止提现审核、主子账号划转、发红包</p>
            <p>禁止交易：禁止现货、杠杆、合约交易</p>
            <p>禁止借币：禁止杠杆借币</p>
            <p>交易受限：禁止现货交易，合约交易仅限平/减仓，杠杆交易仅允许有借币的市场</p>
            <p>禁止划出：禁止杠杆、合约账户划出</p>
            <p>提现受限：允许用户发起提现，并卡在系统审核阶段，允许用户撤销提现订单</p>

          </div>
          <i class="el-icon-info"></i>
        </el-tooltip>
        <el-select clearable multiple v-model="filters.block_permissions" style="width: 220px" placeholder="<ALL>" @change="refresh">
          <el-option v-for="(val, key) in permissions"
                     :key="key"
                     :label="val"
                     :value="key">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="状态">
        <el-select clearable v-model="filters.status" placeholder="<ALL>" @change="refresh">
          <el-option v-for="(val, key) in statuses"
                     :key="key"
                     :label="val"
                     :value="key">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item prop="start" label="开始时间">
        <el-date-picker
          @change="refresh"
          v-model="filter_date_range[0]"
          type="datetime"
          value-format="timestamp"
          placeholder="开始时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item prop="end" label="结束时间">
        <el-date-picker
          @change="refresh"
          v-model="filter_date_range[1]"
          type="datetime"
          value-format="timestamp"
          placeholder="结束时间">
        </el-date-picker>
      </el-form-item>

      <el-form-item label="用户">
        <el-input v-model="filters.keyword"
                  clearable
                  placeholder="ID/邮箱/用户名/手机号"
                  @change="refresh"></el-input>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="refresh">查询</el-button>
      </el-form-item>

      <el-form-item>
        <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
          <el-button icon="el-icon-refresh-left"
                     circle
                     @click="refresh"></el-button>
        </el-tooltip>
      </el-form-item>

      <el-form-item>
        <el-tooltip content="下载" placement="right" :open-delay="500" :hide-after="2000">
          <el-button icon="el-icon-download"
                     circle type="success"
                     @click="download"></el-button>
        </el-tooltip>
      </el-form-item>

    </el-form>

    <el-row> 统计：共 {{ total }} 条 </el-row>
    </br>

    <el-table :data="items"
              v-loading="loading"
              @selection-change="handle_selection_change"
              stripe>

      <el-table-column
        type="selection"
        width="55">
      </el-table-column>

      <el-table-column
        label="ID">
        <template slot-scope="scope">
          <el-link
            type="primary"
            :href="`/risk-control/risk-detail?id=${scope.row.id}`"
            :underline="false"
            target="_blank">
            {{ scope.row.id }}
          </el-link >
        </template>
      </el-table-column>

      <el-table-column
        prop="created_at"
        :formatter="(row, col, val) => $formatDate(val)"
        label="风控时间">
      </el-table-column>

      <el-table-column
        label="用户邮箱">
        <template slot-scope="scope">
          <el-link
            type="primary"
            :href="`/users/user-details?id=${scope.row.user_id}`"
            :underline="false"
            target="_blank">
            {{ scope.row.email || scope.row.user_id}}
          </el-link >
        </template>
      </el-table-column>

      <el-table-column
        prop="risk_count"
        label="风控次数">
        <template slot="header" slot-scope="scope">
          <span>风控次数</span>
          <el-tooltip content="从2025年8月28日开始统计" placement="top" effect="light">
            <i class="el-icon-question" style="color: #909399; margin-left: 4px; cursor: help;"></i>
          </el-tooltip>
        </template>
      </el-table-column>

      <el-table-column
        prop="reason"
        :formatter="(row, col, val) => reasons[val]"
        label="风控类型">
      </el-table-column>

      <el-table-column
        prop="permissions"
        :formatter="(row, col, val) => val.length !== 0 ? val.map(i => permissions[i]).join() : '无限制'"
        label="权限">
      </el-table-column>

      <el-table-column
        prop="status"
        :formatter="(row, col, val) => statuses[val]"
        label="状态">
      </el-table-column>

      <el-table-column
        prop="source"
        label="来源">
      </el-table-column>

      <el-table-column
        prop="audited_at"
        :formatter="(row, col, val) => val ? $formatDate(val) : ''"
        label="审核时间">
      </el-table-column>

      <el-table-column
        label="审核人">
        <template slot-scope="scope">
          <el-link v-if="scope.row.audited_by"
            type="primary"
            :href="`/users/user-details?id=${scope.row.audited_by}`"
            :underline="false"
            target="_blank">
            {{ scope.row.audited_by_name }}
          </el-link >
        </template>
      </el-table-column>

      <el-table-column
        prop="prevent_expired_at"
        :formatter="row => $formatDate(row.prevent_expired_at)"
        label="防干扰时间">
      </el-table-column>

      <el-table-column show-overflow-tooltip label="风控详情" prop="detail">
        <template slot-scope="scope">
          <el-link v-if="scope.row.reason === 'withdrawal_address_blacklisted'"
            type="primary"
            :href="`/asset/withdrawals?id=${scope.row.detail}`"
            :underline="false"
            target="_blank">
            {{ scope.row.detail }}
          </el-link >
          <div v-else>{{ scope.row.detail }}</div>
        </template>
      </el-table-column>

      <el-table-column label="备注" prop="remark"></el-table-column>

      <el-table-column label="操作" width="90px">
        <template slot-scope="scope">
          <el-tooltip content="审核" placement="left" :open-delay="500" :hide-after="2000">
            <el-button size="mini" type="primary" icon="el-icon-check" circle
                       @click="show_dialog([scope.row], false, false)"></el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page.sync="filters.page"
                   :page-size.sync="filters.limit"
                   :page-sizes="[50, 100, 200, 500]"
                   :total="total"
                   @size-change="refresh"
                   @current-change="refresh(true)"
                   :hide-on-single-page="true"
                   layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>

    <div class="el-backtop" style="right: 20px; bottom: 40px; position: fixed;"
      onclick="window.scrollTo(0, document.body.scrollHeight)">
      <i class="el-icon-caret-bottom"></i>
    </div>
    <div class="el-backtop" style="right: 20px; bottom: 90px; position: fixed;"
      onclick="window.scrollTo(0, 0)">
      <i class="el-icon-caret-top"></i>
    </div>

    <br>
    <el-button type="primary" @click="show_dialog(seleted_rows, false, true)">批量审核</el-button>
    <el-button v-if="filters.reason" @click="show_dialog([], true, true)">一键审核</el-button>
    <el-tooltip placement="right" :open-delay="500" style="margin-left: 10px;">
      <div slot="content">
        <p>批量审核：审核选中的记录</p>
        <p>一键审核：审核当前类型下所有的待审核记录</p>
      </div>
      <i class="el-icon-question"></i>
    </el-tooltip>

    <el-dialog
      title="审核"
      :visible.sync="dialog.visible"
      width="30%">
      <el-form label-position="left" label-width="60px">
        <el-form-item label="结果">
          <el-select v-model="dialog.option" placeholder="请选择" style="width: 300px">
            <el-option
              v-for="(val, key) in dialog.options"
              :key="key"
              :label="val"
              :value="key">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="防干扰时间" v-if="dialog.second_show && dialog.option == 'audited'">
          <el-radio-group v-model="dialog.prevent_seconds">
            <el-radio
              v-for="(name, label) in prevent_options"
              :key="name"
              :label="name">
              {{ label }}
            </el-radio>
          </el-radio-group>
          <el-tooltip class="item" effect="dark" content="选择了具体的时间后，用户在该时间范围内不会被该类型的风控限制" placement="right">
            <el-button size="mini" icon="el-icon-info" circle></el-button>
          </el-tooltip>
        </el-form-item>


        <el-form-item label="备注">
          <el-input v-model="dialog.remark" placeholder="请输入备注" style="width: 300px"></el-input>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="dialog.visible = false">取 消</el-button>
        <el-button type="primary" @click="dialog.visible = false; audit()">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import axios from 'axios'
  import Vue from 'vue';

  export default {
    methods: {
      download() {
        let params = {...this.filters, export: 1};
        delete params['page'];
        delete params['limit'];
        this.$axios.get('/api/risk-control/risk-infos', {params: params, responseType: 'blob'}).then(res => {
          const content_type = res.headers['content-type'];
          const url = window.URL.createObjectURL(new Blob([res.data], content_type? {type: content_type} : {}));
          const a = document.createElement(('a'));
          a.href = url;
          document.body.appendChild(a);
          const content_disposition = res.headers['content-disposition'];
          a.download = content_disposition? content_disposition.split('filename=')[1] : 'temp.xlsx';
          a.click();
          window.URL.revokeObjectURL(url);
        });
      },

      refresh(keep) {
        if (keep !== true) {
          this.filters.page = 1;
        }
        this.loading = true;

        var params = _.cloneDeep(this.filters);
        if(params.block_permissions){
          params.block_permissions = params.block_permissions.join(",")
        }
        this.$axios.get(
          '/api/risk-control/risk-infos',
          {params: params}
        ).then(
          res => {
            if (res.data.code == 0) {
              let data = res.data.data;
              this.items = data.items;
              this.reasons = data.reasons;
              this.statuses = data.statuses;
              this.sources = data.sources;
              this.permissions = data.permissions;
              this.prevent_options = data.prevent_options;
              this.enabled_reasons = data.enabled_reasons;
              this.dialog.options = data.audit_options;
              this.seleted_rows = [];
              this.total = data.total;
            } else {
              this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
            }
            this.loading = false;
          }
        ).catch(err => {
          this.$message.error(`刷新失败! (${err})`);
          this.loading = false;
        });
      },
      handle_selection_change(val) {
        this.seleted_rows = val;
      },
      show_dialog(rows, audit_by_filter, is_batch) {
        let option = Object.keys(this.dialog.options)[0];
        let is_audited = option === 'audited'
        if (audit_by_filter) {
          this.dialog.audit_by_filter = true;
          this.dialog.rows = [];
          this.dialog.second_show = is_audited && this.enabled_reasons.includes(this.filters.reason)
        } else if (rows.length > 0) {
          this.dialog.audit_by_filter = false;
          this.dialog.rows = rows;
          this.dialog.second_show = is_audited && rows.every(row => this.enabled_reasons.includes(row.reason))
        } else {
          return;
        }
        this.dialog.option = option;
        this.dialog.remark = this.dialog.rows.length === 1 ? this.dialog.rows[0].remark: null;
        this.dialog.visible = true;
        this.dialog.is_batch = is_batch;
        this.dialog.prevent_seconds = null;
      },
      audit () {
          let params;
          if (this.dialog.audit_by_filter) {
            params = {
              filter: this.filters.reason,
              status: this.dialog.option,
              remark: this.dialog.remark,
              prevent_seconds: this.dialog.prevent_seconds
            }
          } else if (this.dialog.rows.length > 0) {
            params = {
              ids: this.dialog.rows.map(i => i.id),
              status: this.dialog.option,
              remark: this.dialog.remark,
              prevent_seconds: this.dialog.prevent_seconds
            }
          } else {
            return;
          }
          this.$axios.post('/api/risk-control/risk-infos',
            params
          ).then(res => {
              if (res.data.code === 0) {
                  this.$message.success('审核成功');
                  this.refresh();
              } else {
                  this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
              }
          }).catch(err => {
              this.$message.error(`审核失败! (${err})`);
          });
      },
    },
    mounted() {
      if (this.$route.query.keyword) {
        this.filters.keyword = this.$route.query.keyword;
      } else {
        this.filters.keyword = this.$route.query.user_id;
      }
      this.refresh();
    },
    data() {
      return {
        loading: null,
        filters: {
          page: 1,
          limit: 50,
          reason: null,
          status: 'audit_required',
          start_time: null,
          end_time: null,
          keyword: null,
          block_permissions: [],
        },
        filter_date_range: [null, null],
        items: [],
        total: 0,
        reasons: {},
        sources: [],
        statuses: {},
        prevent_options: {},
        enabled_reasons: [],
        permissions: {},
        seleted_rows: [],
        dialog: {
            audit_by_filter: false,
            rows: [],
            visible: false,
            options: {},
            option: null,
            is_batch: false,
            prevent_seconds: null,
            remark: null
        }
      }
    },
    watch: {
      'filters.reason': function (newVal, oldVal) {
        // 当类型发生变化时，清空来源字段
        this.filters.source = null;
        this.refresh();
      },
      filter_date_range: function (val) {
        this.filters.start_time = val && val[0] ? val[0] / 1000 : null;
        this.filters.end_time = val && val[1] ? val[1] / 1000 : null;
      }
    }
  }
</script>

<style>
.el-table .cell {
  white-space: pre-wrap;
}
</style>
