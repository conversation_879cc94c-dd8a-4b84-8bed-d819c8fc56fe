{"name": "Coinex-Dashboard", "version": "1.0.0", "description": "Coinex-Dashboard", "author": "<PERSON><PERSON><PERSON>", "private": true, "scripts": {"dev": "nuxt", "build": "nuxt build", "start": "nuxt start", "generate": "nuxt generate", "update-config": "node update-dev-config.js"}, "dependencies": {"@bachdgvn/vue-otp-input": "^1.0.8", "@ckeditor/ckeditor5-build-classic": "^19.0.2", "@ckeditor/ckeditor5-vue": "^1.0.1", "@highcharts/map-collection": "^2.1.0", "@lottiefiles/vue-lottie-player": "^1.1.0", "@nuxt/ufo": "^0.5.4", "@nuxtjs/proxy": "^2.1.0", "@tinymce/tinymce-vue": "^3.0.0", "at-least-node": "^1.0.0", "axios": "^0.19.2", "babel-runtime": "^6.26.0", "chokidar": "^3.5.1", "cookie-universal": "^2.1.4", "cookie-universal-nuxt": "^2.1.3", "decimal.js": "^10.2.0", "decode-uri-component": "^0.2.0", "element-ui": "^2.15.14", "file-saver": "^2.0.5", "graceful-fs": "^4.2.6", "highcharts": "^8.1.2", "highcharts-vue": "^1.3.5", "is-what": "^4.1.16", "isarray": "^2.0.5", "isobject": "^4.0.0", "jquery": "^3.6.3", "json-parse-better-errors": "^1.0.2", "less": "^3.11.3", "less-loader": "^6.1.2", "moment": "^2.26.0", "neo-async": "^2.6.2", "nuxt": "^2.17.3", "papaparse": "^5.4.1", "path-to-regexp": "^6.2.0", "qrcode.vue": "^1.7.0", "sortablejs": "^1.14.0", "spark-md5": "^3.0.2", "tinymce": "^4.9.11", "union-value": "^2.0.1", "universalify": "^2.0.0", "via-editor": "^1.0.31", "vue": "^2.7.16", "vue-clipboard2": "^0.3.1", "vue-focus-lock": "^1.4.0", "vuex": "^3.6.2", "vuex-persistedstate": "^3.0.1", "webpack-sources": "^2.2.0", "xlsx": "^0.16.9"}, "config": {"nuxt": {"host": "0.0.0.0", "port": "7000"}}, "devDependencies": {"eslint": "^9.10.0", "eslint-webpack-plugin": "^4.2.0", "webpack": "~4.46.0", "webpack-cli": "^5.1.4", "vue-template-compiler": "^2.7.16"}}